import "reflect-metadata";
import SignupComplete from "@src/pages/signup-complete";
import { SignupCompletePageLabels } from "@src/server/contentManagement/SignupCompletePageMapper";
import { render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/router";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";

jest.mock("next/router");

describe("SignupComplete", () => {
  const router = {
    locale: "en-us",
    push: jest.fn()
  };
  const signupCompleteProps = {
    pageLabels: {
      signupCompleteLabels: {
        title: "Signup Complete Title",
        subtitle: "Signup Complete Subtitle",
        body: "Signup Complete Body",
        getStarted: "Get Started"
      }
    } as SignupCompletePageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("shows signup complete elements", () => {
    render(<SignupComplete {...signupCompleteProps} />);

    expect(screen.getByTestId("signup-complete-header-image")).toHaveRole("img");
    expect(screen.getByTestId("signup-complete-characters")).toHaveRole("img");
    expect(screen.getByRole("heading", { name: "Signup Complete Title" })).toBeInTheDocument();
    expect(screen.getByText("Signup Complete Subtitle")).toBeInTheDocument();
    expect(screen.getByText("Signup Complete Body")).toBeInTheDocument();
  });

  it("verifies button click navigates to dashboard", async () => {
    render(<SignupComplete {...signupCompleteProps} />);

    await userEvent.click(screen.getByRole("button", { name: "Get Started" }));

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith("/dashboard");
    });
  });

  it("is accessible", async () => {
    const { container } = render(<SignupComplete {...signupCompleteProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
