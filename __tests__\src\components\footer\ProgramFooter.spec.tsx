import { act, render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/router";
import userEvent from "@testing-library/user-event";
import { mockMatchMedia } from "../../../helpers/window";
import { axe } from "jest-axe";
import ProgramFooter from "@components/footer/ProgramFooter";
import { useDetectScreen } from "@src/utils";
import { useDependency } from "@src/context/DependencyContext";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { footerLabels } from "@src/pages/404";

jest.mock("@src/utils");
jest.mock("@src/context/DependencyContext");
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

describe("ProgramFooter", () => {
  mockMatchMedia();
  const footerLinks = [
    ["Dashboard", "/dashboard"],
    ["Opportunities", "/opportunities"],
    ["My Content", "/my-content"],
    ["Documentation", "/documentation"],
    ["FAQs", "/faq"]
  ];
  const router = {
    push: jest.fn(),
    pathname: "/#how-it-works",
    locale: "en-us"
  };
  const analytics = ({ clickedFooterLink: jest.fn() } as unknown) as BrowserAnalytics;
  const mockUseDetectScreen = useDetectScreen as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { SUPPORTED_LOCALES: ["en-us"], PROGRAM_CODE: "sims_creator_program" }
    });
  });

  it("shows 'disclaimer' Link", () => {
    render(<ProgramFooter locale={""} analytics={analytics} labels={footerLabels} />);

    expect(screen.getByRole("link", { name: /disclaimer/i })).toBeInTheDocument();
  });

  it.each(footerLinks)("logs 'Link Clicked' event when clicking on '%s' marketing page", async (name, url) => {
    mockUseDetectScreen.mockImplementation((width) => width === 10000);
    const locale = "en-us";
    render(<ProgramFooter locale={locale} analytics={analytics} labels={footerLabels} />);

    await userEvent.click(screen.getByRole("button", { name }));

    await waitFor(async () => {
      expect(router.push).toHaveBeenCalledWith(url);
    });
  });

  it("shows 'SIMS' logo", () => {
    mockUseDetectScreen.mockImplementation((width) => width === 10000);
    const locale = "en-us";

    render(<ProgramFooter locale={locale} analytics={undefined} labels={footerLabels} />);

    expect(screen.getByTestId("footer-mobile-ea-icon")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ProgramFooter locale={"en-us"} analytics={analytics} labels={footerLabels} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
