.profile-card {
  @apply relative mb-meas16 mt-meas22 box-border flex h-[170px] w-full flex-col items-center justify-center rounded-[4px] bg-primary bg-opacity-[0.85] bg-none lg:mb-meas20;
}
.profile-card-logo-container {
  @apply relative flex h-[92px] flex-col items-center;
}
.profile-card-logo {
  @apply absolute top-[-50px] flex h-[100px] w-[100px] flex-col overflow-hidden rounded-full border border-[0px] border-agave-60 bg-[#6DD6E4];
}
.profile-card-avatar {
  @apply relative h-[100px] w-[100px] rounded-full;
}
.profile-card-info {
  @apply relative flex h-full w-full flex-col items-center justify-center;
}
.profile-card-title {
  @apply font-display-regular font-bold text-gray-10 xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.profile-card-title > h4 {
  @apply flex items-center justify-center;
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
}
.profile-card-sub-title {
  @apply pt-meas2 font-text-regular text-black xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.profile-card-creator-since {
  @apply font-text-bold text-black;
}
.profile-card-logo-edit {
  @apply absolute bottom-meas2 flex h-meas16 w-meas16 cursor-pointer items-center justify-center rounded-full bg-black text-white;
}
.profile-card-logo-edit > .icon-block .icon {
  @apply ml-[7px] h-meas9 w-meas9;
}
