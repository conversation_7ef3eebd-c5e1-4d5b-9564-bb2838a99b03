{"filters": "<PERSON><PERSON><PERSON>", "dateRange": "Intervallo di date", "startDate": "Data di inizio", "endDate": "Data di fine", "paymentStatus": "Stato pagamento", "opportunityType": "Tipo", "applyFilters": "Applica", "startDateError": "La data di inizio deve essere antecedente alla data di fine", "endDateError": "La data di fine deve essere successiva alla data di inizio", "sameDateError": "La data di inizio e la data di fine non possono essere uguali", "startDateRequired": "La data di inizio è obbligatoria", "endDateRequired": "La data di fine è obbligatoria", "range": {"allTime": "Di sempre", "thisMonth": "<PERSON>o mese", "past30Days": "Ultimi 30 giorni", "past90Days": "Ultimi 90 giorni", "past6Months": "Ultimi 6 mesi", "yearToDate": "Da inizio anno", "lastYear": "<PERSON>'anno scorso", "custom": "Personalizza"}, "status": {"all": "<PERSON><PERSON>", "processed": "Elaborato", "pending": "In attesa"}, "type": {"all": "<PERSON><PERSON>", "opportunity": "Opportunità", "creatorCode": "Codice creatore"}}