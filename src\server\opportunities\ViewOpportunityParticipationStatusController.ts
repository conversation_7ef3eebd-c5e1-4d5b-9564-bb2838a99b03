import { Service } from "typedi";
import { NextApiResponse } from "next";
import ParticipationsHttpClient from "../opportunities/ParticipationsHttpClient";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
export default class ViewOpportunityParticipationStatusController
  extends AuthenticatedRequestHandler
  implements Controller {
  constructor(private participations: ParticipationsHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = this.identity(req);
    const { opportunityIds }: { opportunityIds: Array<string> } = req.body;

    const participationStatuses = await this.participations.withSubmissionStatus(user.id, opportunityIds);

    this.json(res, participationStatuses);
  }
}
