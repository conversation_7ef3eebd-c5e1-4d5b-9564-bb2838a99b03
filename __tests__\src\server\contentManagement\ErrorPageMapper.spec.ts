import { ErrorPageMapper } from "@src/server/contentManagement/ErrorPageMapper";

describe("ErrorPageMapper", () => {
  const microCopies = {
    "error.accessErrorDescription": "Access Error Description",
    "error.accessErrorSubtitle": "Access Error Subtitle",
    "error.accessErrorTitle": "Access Error Title",
    "error.accountDeactivateSubtitle": "Account Deactivate Subtitle",
    "error.accountDeactivateTitle": "Account Deactivate Title",
    "error.accountDeactivated": "Account Deactivated"
  };

  it("maps error page labels", () => {
    const mapper = new ErrorPageMapper();
    const labels = mapper.map(microCopies).errorLabels;

    expect(labels.accessErrorDescription).toEqual("Access Error Description");
    expect(labels.accessErrorSubtitle).toEqual("Access Error Subtitle");
    expect(labels.accessErrorTitle).toEqual("Access Error Title");
    expect(labels.accountDeactivateSubtitle).toEqual("Account Deactivate Subtitle");
    expect(labels.accountDeactivateTitle).toEqual("Account Deactivate Title");
    expect(labels.accountDeactivated).toEqual("Account Deactivated");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new ErrorPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key error.accountDeactivateTitle is absent");
  });
});
