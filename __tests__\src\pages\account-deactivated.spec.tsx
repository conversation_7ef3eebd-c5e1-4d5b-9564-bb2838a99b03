import "reflect-metadata";
import React from "react";
import { act, screen } from "@testing-library/react";
import { axe } from "jest-axe";
import { useRouter } from "next/router";
import AccountDeactivated from "@src/pages/account-deactivated";
import { renderPage } from "__tests__/helpers/page";
import { ErrorPageLabels } from "@src/server/contentManagement/ErrorPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";

// Mock Next.js Head component to render title elements for testing
jest.mock("next/head", () => {
  return function Head({ children }: { children: React.ReactNode }) {
    return <div data-testid="next-head">{children}</div>;
  };
});

describe("AccountDeactivatedPage", () => {
  const deactivatedAccount = {
    defaultGamerTag: "083123"
  };

  const customPageLabels = {
    errorLabels: {
      accountDeactivateTitle: "Your account has been deactivated",
      accountDeactivateSubtitle: `Please contact your community manager to reactivate this account ${deactivatedAccount.defaultGamerTag} or log in using a different Electronic Arts account.`,
      accountDeactivated: "Account Deactivated"
    }
  } as ErrorPageLabels & CommonPageLabels;

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
  });

  it("shows account deactivated content", async () => {
    renderPage(<AccountDeactivated pageLabels={customPageLabels} deactivatedAccount={deactivatedAccount} />);

    expect(
      screen.getByRole("heading", { name: customPageLabels.errorLabels.accountDeactivateTitle })
    ).toBeInTheDocument();
    expect(screen.getByText(customPageLabels.errorLabels.accountDeactivateSubtitle)).toBeInTheDocument();
  });

  it("shows the correct document title", () => {
    renderPage(<AccountDeactivated pageLabels={customPageLabels} deactivatedAccount={deactivatedAccount} />);

    const titleElement = screen.getByText(customPageLabels.errorLabels.accountDeactivated);
    expect(titleElement).toBeInTheDocument();
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderPage(
      <AccountDeactivated pageLabels={customPageLabels} deactivatedAccount={deactivatedAccount} />
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
