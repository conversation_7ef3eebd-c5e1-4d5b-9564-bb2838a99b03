import {
  NextApiRequestWithSession,
  RequestHandler,
  RequestH<PERSON>lerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { AccountDeactivatedProps } from "@src/pages/account-deactivated";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { ErrorPageLabels } from "@src/server/contentManagement/ErrorPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class AccountDeactivatedPagePropsController
  extends RequestHandler
  implements ServerPropsController<AccountDeactivatedProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<AccountDeactivatedProps>> {
    if (!this.hasSession(req, `${this.program}.deactivatedAccount`)) return { notFound: true };
    const deactivatedAccount = this.hasSession(req, `${this.program}.deactivatedAccount`)
      ? (this.session(req, `${this.program}.deactivatedAccount`) as { defaultGamerTag: string })
      : { defaultGamerTag: "" };
    const pageLabels = (await this.contents.getPageLabels(this.currentLocale, "error")) as CommonPageLabels &
      ErrorPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels,
        deactivatedAccount
      }
    };
  }
}
