import { render, screen } from "@testing-library/react";
import SubCategoryCard from "./SubCategoryCard";
import { aSubCategory } from "__tests__/factories/documentation/documentation";
import { axe } from "jest-axe";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("@src/context/DependencyContext");

describe("SubCategoryCard", () => {
  const subCategoryCardProps = aSubCategory();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        BASE_PATH: "https://example.com"
      }
    });
  });

  it("shows subcategory card with title", () => {
    render(<SubCategoryCard {...subCategoryCardProps} />);

    expect(screen.getByTestId("subcategory-card-container")).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: subCategoryCardProps.title })).toBeInTheDocument();
  });

  it("shows correct number of links", () => {
    render(<SubCategoryCard {...subCategoryCardProps} />);

    expect(screen.getAllByRole("link")).toHaveLength(subCategoryCardProps.links.length);
  });

  it.each(subCategoryCardProps.links)("shows link with correct title", (link) => {
    render(<SubCategoryCard {...subCategoryCardProps} />);

    expect(screen.getByRole("link", { name: link.title })).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<SubCategoryCard {...subCategoryCardProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
