export const submitFileContentTranslations = {
  title: "Upload a file from your device",
  fileUpload: "File Upload",
  chooseFile: "Choose File",
  noFileChoosen: "No file chosen",
  acceptedFormats: "Accepted File Formats",
  maxFileSize: "Max File Size",
  fileSelected: "File Selected",
  fileUploading: "Uploading file...",
  maxLimitMessage: "Please select file within allowed size limits.",
  invalidFileTypeMessage: "Please select file with allowed extensions.",
  success: {
    title: "Your file has been uploaded",
    content:
      "Your file has been uploaded and is being processed. You will receive a notification when it is finished. You can leave this page or add further content while your file is processing."
  },
  error: {
    title: "File Upload Failed",
    content: "There was an error uploading your file. Please try again."
  },
  buttonsLabels: {
    cancel: "Cancel",
    upload: "Upload",
    close: "Close"
  },
  uploadFileProgress: "Upload file progress",
  removeSelectedFile: "Remove selected file"
};
