import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import PaymentInfoPagePropsController from "./controllers/PaymentInfoPagePropsController";
import config from "config";

const paymentInfoProps = (locale: string) =>
  serverPropsControllerFactory(
    new PaymentInfoPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default paymentInfoProps;
