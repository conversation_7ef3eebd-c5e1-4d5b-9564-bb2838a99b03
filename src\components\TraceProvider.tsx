import { useDependency } from "@src/context/DependencyContext";
import React from "react";
import FrontendTracer from "@src/telemetry/FrontendTracer";

export default function TraceProvider({ children }) {
  const {
    configuration: { SERVICE_NAME, APP_ENV, RELEASE_VERSION, FLAG_OBSERVABILITY }
  } = useDependency();

  if (typeof window !== "undefined") {
    FrontendTracer({
      serviceName: SERVICE_NAME,
      environment: APP_ENV,
      version: RELEASE_VERSION,
      enabled: FLAG_OBSERVABILITY
    });
  }

  return <>{children}</>;
}
