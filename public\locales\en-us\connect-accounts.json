{"title": "Connect your Social Media Accounts", "message": "Connect any social media accounts that you want to submit content from.", "subTitle": "Add Accounts", "myAccount": "My Accounts", "connectAccountsPageTitle": "Connect Accounts", "addAccount": "Add Account", "description": "Please connect at least one of your social media accounts with your content. You may also  connect multiple accounts on each social media. This will help our team assess our compatibility.", "accounts": {"youTube": "YouTube", "facebook": "Facebook", "twitch": "Twitch", "instagram": "Instagram", "tiktok": "TikTok"}, "messages": {"removeAccountTitle": "Unable to Remove Account", "removeAccountDescription": "We're unable to remove this account. You need to link at least one account to your profile", "cannotConnectInstaAccount": "Only one Instagram account can be connected to your profile at a time", "cannotConnectInstaAccountHeader": "Unable to connect Instagram account", "youtubeNoChannelError": "Unable to add YouTube account as no channel is associated with it.", "actionTitle": "Action required!", "actionDescription1": "To connect your Instagram account you must first link it to your Facebook profile.", "actionDescription2": "To do this you can follow", "actionDescription3": "these simple steps", "actionDescription4": "from Instagram’s Help Center, you’ll then be able to connect your Instagram account."}, "modalConfirmationTitleFB": "Please select a Facebook page", "confirmationDesc1": "Pressing Yes will quit the registration process.", "confirmationDesc2": "You can begin registration again at any point by visiting The Sims Portal.", "removeAccountTitle": "Remove Channel?", "removeAccountDescription1": "Are you sure you want to remove this Channel?", "removeAccountDescription2": "You must have a social media account connected in order to submit content.", "modalTitle": "Connect Your Account", "modalMessage": "This is a placeholder.A login will appear here allowing you to connect and authenticate your chosen social media account", "subscribers": "subscribers", "removeAccount": "Remove Account", "cancel": "Cancel", "remove": "Remove", "expireAccount": "Account Disconnected", "or": "or", "reconnectAccount": "Click to reconnect this account", "connectNewAccount": "Connect a new account", "connectNewAccountDescription": "Facebook, Instagram, YouTube or Twitch", "connectNewAccountDescriptionWithTikTok": "Facebook, Instagram, YouTube, Twitch or TikTok", "comma": ", ", "verificationPending": "Verification Pending", "reVerifyAccount": "Click to re-verify this account"}