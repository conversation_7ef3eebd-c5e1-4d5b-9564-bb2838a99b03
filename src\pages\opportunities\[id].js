import "reflect-metadata";
import OpportunityPageWithPerks from "@components/pages/OpportunityPageWithPerks";
import { useTranslation } from "next-i18next";
import labelsCommon from "@src/config/translations/common";
import labelsOpportunities from "@src/config/translations/opportunities";
import labelsAddContent from "@src/config/translations/add-content";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import labelsContentSubmission from "@src/config/translations/content-submission";
import labelsFacebookGuide from "@src/config/translations/content-submission-facebook-guide";
import labelsYouTubeGuide from "@src/config/translations/content-submission-youtube-guide";
import labelsInstagramGuide from "@src/config/translations/content-submission-instagram-guide";
import labelsTwitchGuide from "@src/config/translations/content-submission-twitch-guide";
import labelsTikTokGuide from "@src/config/translations/content-submission-tiktok-guide";
import labelsConnectAccounts from "@src/config/translations/connect-accounts";
import labelsWebsiteContent from "@src/config/translations/content-submission-website";
import labelsUploadContent from "@src/config/translations/content-submission-upload";
import labelsProfile from "@src/config/translations/profile";
import labelsMyContent from "@src/config/translations/my-content";
import { labelsPaymentBanner } from "@src/config/translations/payment-information";
import labelsPointOfContact from "@src/config/translations/point-of-contact";
import { useAppContext } from "@src/context";
import { toast } from "react-toastify";
import Error from "../_error";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import Header from "@components/header/header";
import Footer from "@components/footer/ProgramFooter";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import { SHOW_WARNING_FOR_CHANGES_REQUESTED } from "../../utils/constants";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import opportunityProps from "@src/serverprops/OpportunityProps";

export default function Opportunity({
  user,
  WATERMARKS_URL,
  YOUTUBE_HOSTS,
  TWITCH_HOSTS,
  INSTAGRAM_HOSTS,
  FACEBOOK_HOSTS,
  TIKTOK_HOSTS,
  accountConnected,
  error,
  pages,
  referer,
  invalidTikTokScope,
  analytics = new BrowserAnalytics(user)
}) {
  const [connectedAccount, setConnectedAccount] = useState(accountConnected);
  const router = useRouter();
  const { state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const { t } = useTranslation([
    "common",
    "opportunities",
    "notifications",
    "add-content",
    "connect-accounts",
    "content-submission",
    "content-submission-instagram-guide",
    "content-submission-facebook-guide",
    "content-submission-youtube-guide",
    "content-submission-twitch-guide",
    "content-submission-tiktok-guide",
    "content-submission-website",
    "submit-social-media-content",
    "content-submission-upload",
    "my-content",
    "payment-information",
    "point-of-contact"
  ]);
  const {
    layout,
    addContentLabels,
    opportunitiesLabels,
    notificationsLabels,
    connectAccountLabels,
    contentSubmissionLabels,
    facebookGuideLabels,
    youtubeGuideLabels,
    instagramGuideLabels,
    twitchGuideLabels,
    tiktokGuideLabels,
    websiteContentLabels,
    uploadContentLabels,
    myContentLabels,
    paymentBannerLabels,
    pocLabels
  } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      addContentLabels: { ...labelsAddContent(t), ...labelsProfile(t) },
      opportunitiesLabels: labelsOpportunities(t),
      layout: labelsCommon(t),
      connectAccountLabels: labelsConnectAccounts(t),
      notificationsLabels: notificationBellLabels,
      contentSubmissionLabels: labelsContentSubmission(t),
      facebookGuideLabels: labelsFacebookGuide(t),
      youtubeGuideLabels: labelsYouTubeGuide(t),
      instagramGuideLabels: labelsInstagramGuide(t),
      twitchGuideLabels: labelsTwitchGuide(t),
      tiktokGuideLabels: labelsTikTokGuide(t),
      websiteContentLabels: labelsWebsiteContent(t),
      uploadContentLabels: labelsUploadContent(t),
      myContentLabels: labelsMyContent(t),
      paymentBannerLabels: labelsPaymentBanner(t),
      pocLabels: labelsPointOfContact(t)
    };
    labels.layout.footer = { locale: router.locale, labels: labels.layout.footer };
    return labels;
  }, [t, router.locale]);
  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    opportunities,
    documentation,
    myContent,
    signout,
    notifications
  } = layout.header;

  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const commonPageLabels = {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    opportunities,
    myContent,
    documentation,
    myProfile,
    signout,
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy,
    notifications
  };

  const facebookGuideModalLabels = {
    title: facebookGuideLabels.title,
    subTitle: facebookGuideLabels.subTitle,
    contentImages: [
      {
        desc: facebookGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperFacebook1.png",
          "/img/content-submission/helperFacebook2.png",
          "/img/content-submission/helperFacebook4.png"
        ]
      }
    ],
    pointsToNote: [
      facebookGuideLabels.pointsToNote1,
      facebookGuideLabels.pointsToNote2,
      facebookGuideLabels.pointsToNote3,
      facebookGuideLabels.pointsToNote4,
      facebookGuideLabels.pointsToNote5
    ],
    footerLabel: facebookGuideLabels.gotIt,
    pointsToNoteTitle: facebookGuideLabels.pointsToNoteTitle
  };
  const youtubeGuideModalLabels = {
    title: youtubeGuideLabels.title,
    subTitle: youtubeGuideLabels.subTitle,
    contentImages: [
      {
        desc: youtubeGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperYoutube1.png",
          "/img/content-submission/helperYoutube3.png",
          "/img/content-submission/helperYoutube4.png"
        ]
      }
    ],
    pointsToNote: [
      youtubeGuideLabels.pointsToNote1,
      youtubeGuideLabels.pointsToNote2,
      youtubeGuideLabels.pointsToNote3
    ],
    footerLabel: youtubeGuideLabels.gotIt,
    pointsToNoteTitle: youtubeGuideLabels.pointsToNoteTitle
  };

  const instagramGuideModalLabels = {
    title: instagramGuideLabels.title,
    subTitle: instagramGuideLabels.subTitle,
    contentImages: [
      {
        desc: instagramGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperInstagram1.png",
          "/img/content-submission/helperInstagram2.png",
          "/img/content-submission/helperInstagram3.png"
        ]
      }
    ],
    pointsToNote: [
      instagramGuideLabels.pointsToNote1,
      instagramGuideLabels.pointsToNote2,
      instagramGuideLabels.pointsToNote3,
      instagramGuideLabels.pointsToNote4,
      instagramGuideLabels.pointsToNote5
    ],
    footerLabel: instagramGuideLabels.gotIt,
    pointsToNoteTitle: instagramGuideLabels.pointsToNoteTitle
  };

  const twitchGuideModalLabels = {
    title: twitchGuideLabels.title,
    subTitle: twitchGuideLabels.subTitle,
    contentImages: [
      {
        desc: twitchGuideLabels.imageDesc1,
        imageSrc: ["/img/content-submission/helperTwitch1.png"]
      },
      {
        desc: twitchGuideLabels.imageDesc2,
        imageSrc: ["/img/content-submission/helperTwitch2.png"]
      }
    ],
    pointsToNote: [twitchGuideLabels.pointsToNote1, twitchGuideLabels.pointsToNote2, twitchGuideLabels.pointsToNote3],
    footNote: twitchGuideLabels.footNote,
    footerLabel: twitchGuideLabels.gotIt,
    pointsToNoteTitle: twitchGuideLabels.pointsToNoteTitle
  };

  const tiktokGuideModalLabels = {
    title: tiktokGuideLabels.title,
    subTitle: tiktokGuideLabels.subTitle,
    contentImages: [
      {
        desc: tiktokGuideLabels.imageDesc1,
        imageSrc: [
          "/img/content-submission/helperTiktok1.png",
          "/img/content-submission/helperTiktok2.png",
          "/img/content-submission/helperTiktok.png"
        ]
      }
    ],
    pointsToNote: [tiktokGuideLabels.pointsToNote1, tiktokGuideLabels.pointsToNote2],
    footerLabel: tiktokGuideLabels.gotIt,
    pointsToNoteTitle: tiktokGuideLabels.pointsToNoteTitle
  };

  const contentSubmissionTabLabels = {
    contentSubmissionLabels,
    facebookGuideModalLabels,
    youtubeGuideModalLabels,
    instagramGuideModalLabels,
    twitchGuideModalLabels,
    tiktokGuideModalLabels,
    addContentLabels,
    connectAccountLabels,
    websiteContentLabels,
    uploadContentLabels
  };

  useEffect(() => {
    return () => {
      toast.dismiss(SHOW_WARNING_FOR_CHANGES_REQUESTED);
    };
  });

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutHeader
        pageTitle={layout.header.opportunities}
        tabTitle={`${layout.theSims} | ${layout.header.opportunities}`}
      >
        <Header
          user={user}
          labels={{
            commonPageLabels: commonPageLabels,
            notificationsBellLabels: notificationsLabels
          }}
          analytics={analytics}
        />
      </LayoutHeader>
      <LayoutBody showSideNavigation={!!user} className="opportunity-layout">
        <OpportunityPageWithPerks
          key={router.asPath}
          user={user}
          WATERMARKS_URL={WATERMARKS_URL}
          paymentBannerLabels={paymentBannerLabels}
          opportunitiesLabels={opportunitiesLabels}
          layout={layout}
          contentSubmissionTabLabels={contentSubmissionTabLabels}
          pocLabels={pocLabels}
          YOUTUBE_HOSTS={YOUTUBE_HOSTS}
          TWITCH_HOSTS={TWITCH_HOSTS}
          INSTAGRAM_HOSTS={INSTAGRAM_HOSTS}
          FACEBOOK_HOSTS={FACEBOOK_HOSTS}
          TIKTOK_HOSTS={TIKTOK_HOSTS}
          t={t}
          accountConnected={connectedAccount}
          setConnectedAccount={setConnectedAccount}
          error={error}
          pages={pages}
          myContentLabels={myContentLabels}
          referer={referer}
          invalidTikTokScope={invalidTikTokScope}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer
          labels={{
            commonPageLabels: commonPageLabels
          }}
          locale={router.locale}
          analytics={analytics}
        />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();
  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(opportunityProps(locale));

  return await router.run(req, res);
};
