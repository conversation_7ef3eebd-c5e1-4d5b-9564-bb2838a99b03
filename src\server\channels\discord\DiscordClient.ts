import config from "../../../../config";
import { Service } from "typedi";

@Service()
export default class DiscordClient {
  private readonly clientID: string = null;
  private readonly redirectUri: URL;
  private readonly scopes: string;

  constructor() {
    this.clientID = config.DISCORD_CLIENT_ID;
    this.redirectUri = config.DISCORD_CLIENT_REDIRECT_URI;
    this.scopes = config.DISCORD_SCOPES;
  }

  authorizationUrl(): string {
    return `https://discord.com/api/oauth2/authorize?client_id=${this.clientID}&redirect_uri=${
      this.redirectUri
    }&response_type=code&scope=${encodeURIComponent(this.scopes)}`;
  }
}
