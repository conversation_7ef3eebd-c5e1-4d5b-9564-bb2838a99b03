.game-code {
  @apply flex flex-col items-center justify-center self-center px-meas8;
}
.game-code-container {
  @apply my-meas16 flex flex-col items-center  justify-center border-t border-gray-10 border-opacity-[0.33] py-meas16 md:w-[640px] xl:w-[840px];
}
.game-code-subTitle {
  @apply mb-meas7 font-display-regular font-bold text-white xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5 xl:w-full;
}
.game-code-message {
  @apply mb-meas13 font-text-regular  text-white xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large xl:w-full;
}
.game-code-platform {
  @apply font-text-regular text-gray-10 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default xl:w-[125px];
}
.game-code-card-label {
  @apply text-center font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.game-code-region {
  @apply mb-meas2 font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.game-code-region-container {
  @apply mt-meas4 text-warning-50;
}
.game-code-region-container .select-scroll-list {
  @apply overflow-y-auto;
}
.game-code-region-container > .select-box,
.game-code-region-container > .select-header {
  @apply md:w-[405px]  xl:w-[320px];
}
.game-code-region-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.game-code-region-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.game-code-card {
  @apply md:w-[640px] xl:w-[850px];
}
.game-code-card .card-container {
  @apply mt-meas6 gap-meas10 md:grid-cols-2 md:gap-meas0 xl:grid-cols-3;
}

.game-code-card .card-col {
  @apply h-full w-full md:w-[231px];
}

.game-code-card .checkmark {
  @apply md:h-[231px] md:w-[231px];
}
