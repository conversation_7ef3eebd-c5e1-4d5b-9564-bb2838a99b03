import { CheckTermsAndConditionsOutdatedServerPropsMiddleware } from "@eait-playerexp-cn/identity";
import ApiContainer from "@src/ApiContainer";
import { serverPropsMiddlewareFactory } from "@eait-playerexp-cn/server-kernel";
import CachedTermsAndConditions from "@src/server/pactSafe/CachedTermsAndConditions";

const checkTermsAndConditionsOutdated = (locale: string) => {
  return serverPropsMiddlewareFactory(
    new CheckTermsAndConditionsOutdatedServerPropsMiddleware(
      ApiContainer.get("options"),
      ApiContainer.get(CachedTermsAndConditions),
      locale
    )
  );
};

export default checkTermsAndConditionsOutdated;
