import { NextApiResponse } from "next";
import SignedUrlsHttpClient from "./SignedUrlsHttpClient";
import { Service } from "typedi";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewSignedUrlController extends Re<PERSON><PERSON><PERSON>ler implements Controller {
  constructor(private readonly signedUrls: SignedUrlsHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const signedURLRequestBody = req.body;

    const preSignedUrl = await this.signedUrls.preSignedUrlFor(signedURLRequestBody);

    this.json(res, preSignedUrl);
  }
}

export default ViewSignedUrlController;
