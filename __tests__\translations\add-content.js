export const addContentPageTranslations = {
  title: "Add your Content URL(s)",
  description:
    "You can submit content from a connected account on Facebook, Instagram, Youtube, or Twitch and from another website.",
  modalTitle: "Cancel submitting content?",
  modalDescription:
    "Are you sure you want to cancel submitting content? Any submissions entered at this stage will be lost.",
  no: "No, I’ll stay",
  yes: "Yes, I want to leave"
};

export const addContentFormTranslations = {
  addContent: "Add Content",
  addNewContent: "Add New Content",
  addContentInstruction: "Please select where you’d like to submit content from:",
  opportunityHeading: "Submitting content for:",
  urlTitle: "Please enter the Content URL:",
  urlPlaceholder: "Enter a URL...",
  addMoreUrlLabel: "Add another URL",
  accountInformation: "For help submitting the correct Social Media URL, select a network →",
  contentInformation1:
    "Respect the rights of others. Only submit content that you own or that you have written permission to freely distribute. Please read our ",
  contentInformation2: "User Agreement ",
  contentInformation3: "for more information.",
  addAccount: "Add Account",
  connectNewAccount: "Connect a new account",
  connectNewAccountDescription: "Facebook, Instagram, YouTube or Twitch",
  connectNewAccountDescriptionWithTikTok: "Facebook, Instagram, YouTube, Twitch or TikTok",
  facebook: "Facebook",
  instagram: "Instagram",
  youTube: "YouTube",
  twitch: "Twitch",
  tiktok: "TikTok",
  footerLabels: { cancel: "Cancel", next: "Review & Submit" },
  contentSubmissionSucessTitle: "YouTube Successfully Connected!",
  contentSubmissionSucessDescription:
    "You have successfully connected to YouTube. You can submit content from that account.",
  connectAnAccount: "Connect an Account",
  modalConfirmationTitleFB: "Please select a Facebook page",
  accountInformation1: "Need help submitting the correct social media URL?",
  accountInformation2: "Select a network →",
  clickTheIcon: "Click the icon →",
  messages: {
    removeAccountTitle: "Unable to Remove Account",
    removeAccountDescription:
      "We're unable to remove this account. You need to link at least one account to Creator Network.",
    cannotConnectInstaAccount: "Only one Instagram account can be connected to your Creator Network profile at a time",
    cannotConnectInstaAccountHeader: "Unable to connect Instagram account",
    youtubeNoChannelError: "Unable to add YouTube account as no channel is associated with it.",
    actionTitle: "Action required!",
    actionDescription1: "To connect your Instagram account you must first link it to your Facebook profile.",
    actionDescription2: "To do this you can follow",
    actionDescription3: "these simple steps",
    actionDescription4:
      "from Instagram’s Help Center, you’ll then be able to connect your Instagram account to the EA Creator Network."
  },
  removeAccount: "Remove Account",
  expireAccount: "Account Disconnected",
  or: "or",
  reconnectAccount: "Click to reconnect this account"
};

export const addContentTranslations = {
  addContent: "Add Content",
  addNewContent: "Add New Content",
  addContentInstruction: "Please select where you’d like to submit content from:",
  contentInformation1:
    "Respect the rights of others. Only submit content that you own or that you have written permission to freely distribute. Please read our ",
  contentInformation2: "User Agreement ",
  contentInformation3: "for more information.",
  contentSubmissionSucessTitle: "YouTube Successfully Connected!",
  contentSubmissionSucessDescription:
    "You have successfully connected to YouTube. You can submit content from that account.",
  connectAnAccount: "Connect an Account",
  accountInformation1: "Need help submitting the correct social media URL?",
  accountInformation2: "Select a network →",
  clickTheIcon: "Click the icon →"
};

export const contentSubmissionTranslations = {
  contentGuideLine: "Disclosure Policy",
  linkSubmissionGuideLine: "Link Submission",
  accountInformation: "For help submitting the correct Social Media URL, select a network →",
  downloadEaLogo: "Download EA Watermark",
  websiteTitle: "Website",
  websiteDescription: "All Other Websites",
  uploadFileTitle: "Upload a file from your device",
  uploadFileDescription: "Submit video, audio or image files",
  submitContentInformation:
    "You can submit content from a connected account on Facebook, Instagram, Youtube, or Twitch and from another website.",
  submissionOpens: "Submission Opens:",
  submissionCloses: "Submission Closes:",
  submissionOpensWithNoContent:
    "The content submission window for this opportunity is not yet open. You will receive a notification when it is time to submit.",
  submissionClosedWithNoContent:
    "The content submission window for this opportunity is closed and no new content can be submitted.",
  submissionClosedWithContent:
    "The content submission window for this opportunity is closed and no new content can be submitted. Existing submissions may be updated if changes are required.",
  contentRequirements: "Content Submission Requirements",
  availableResources: "Available Resources:",
  downloadAttachments: "Download Attachments",
  disclosureParagraph1:
    "As part of the agreement for EA Creator Network you are required to comply with any applicable regulatory guidelines about endorsements and testimonials in advertising and other applicable consumer laws, and that at a minimum you apply the following disclosures:",
  disclosureParagraph2:
    "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your stream. Use one clearly audible statement in the first 5 seconds of your stream stating “Thanks to EA for Sponsoring this stream” or similar Social content must include #SponsoredByEA Include the following tracking link: TBC",
  linkSubmissionContent:
    "After the content is live on your channel please return to this page and submit the links for any content you produce, content needs to be uploaded for approval before payment can be processed. In case you have any issues, please reach out to your community manager.",
  nonPaid: {
    contentGuideLineSubTitle:
      "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:",
    contentGuideLineDescription1:
      "Prominently include “EA Partner” “Sponsored” “Sponsored by EA” or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #EAPartner, #Sponsored, #SponsoredbyEA, or #Ad are also acceptable.",
    contentGuideLineDescription2: "Creators in UK Only: Ad and #Ad are the only acceptable disclosures.",
    contentGuideLineDescription3:
      "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for inviting me to this early access event/for the game code” (or similar).",
    linkSubmissionGuideLineSubTitle1:
      "If you create content, please return to this page after it is live on your channel and submit the links for content you produce.",
    linkSubmissionGuideLineSubTitle2: "If you have any questions, please reach out to your Community Manager."
  },
  paid: {
    contentGuideLineSubTitle:
      "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:",
    contentGuideLineDescription1:
      "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your video or stream.",
    contentGuideLineDescription2:
      "Prominently include “Sponsored by EA” “Sponsored”, or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #SponsoredbyEA, #Sponsored, or #Ad are also acceptable.",
    contentGuideLineDescription3: "Creators in UK Only: Ad and #Ad are the only acceptable disclosures.",
    contentGuideLineDescription4:
      "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for sponsoring this video.” (or similar).",
    contentGuideLineDescription5:
      "Ensure correct use of content platforms disclosure tools such as YouTube’s “includes paid promotion,” and Instagram’s “Partnership” options, and Twitch’s Branded Content options.",
    linkSubmissionGuideLineSubTitle1:
      "After the content is live on your channel please return to this page and submit the links for any content you produce.",
    linkSubmissionGuideLineSubTitle2: "If you have any questions, please reach out to your Creator Partner Manager."
  },
  needsChanges: " needs changes.",
  changesRequiredTitle: "Changes Required",
  contentDeliverables: "Content Deliverables",
  deliverablesInstruction: "Please join this opportunity to submit content.",
  notYetSubmitted: "Not yet submitted",
  pendingApproval: "Pending approval",
  changesRequired: "Changes Required",
  completed: "Completed",
  rejected: "Rejected",
  notApproved: "Not approved",
  submitUnlimitedContent: "Submit unlimited content",
  submissionWindowClosed: "Submission window closed"
};

export const connectAccountTranslations = {
  accounts: {
    youTube: "YouTube",
    facebook: "Facebook",
    twitch: "Twitch",
    instagram: "Instagram",
    tiktok: "TikTok"
  },
  addAccount: "Add Account",
  connectNewAccount: "Connect a new account",
  connectNewAccountDescription: "Facebook, Instagram, YouTube, Twitch or TikTok",
  connectNewAccountDescriptionWithTikTok: "",
  modalConfirmationTitleFB: "Please select a Facebook page",
  confirmationDesc1: "confirmationDesc1",
  confirmationDesc2: "confirmationDesc2",
  messages: {
    removeAccountTitle: "Unable to Remove Account",
    removeAccountDescription:
      "We're unable to remove this account. You need to link at least one account to Creator Network.",
    cannotConnectInstaAccount: "Only one Instagram account can be connected to your Creator Network profile at a time",
    cannotConnectInstaAccountHeader: "Unable to connect Instagram account",
    youtubeNoChannelError: "Unable to add YouTube account as no channel is associated with it.",
    actionTitle: "Action required!",
    actionDescription1: "To connect your Instagram account you must first link it to your Facebook profile.",
    actionDescription2: "To do this you can follow",
    actionDescription3: "these simple steps",
    actionDescription4:
      "from Instagram’s Help Center, you’ll then be able to connect your Instagram account to the EA Creator Network."
  },
  removeAccount: "Remove Account",
  expireAccount: "Account Disconnected",
  or: "or",
  reconnectAccount: "Click to reconnect this account"
};
