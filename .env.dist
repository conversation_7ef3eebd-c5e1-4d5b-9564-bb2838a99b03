# Documentation
TAG=latest
# EADP/Origin Login
LOGIN_URL=https://accounts.int.ea.com/connect/auth?client_id=CNEAIT_DEMO_SERVER_WEB_APP&response_type=code&redirect_uri=http://localhost:3050/api/authenticate
LOGOUT_URL=https://accounts.int.ea.com/connect/logout?client_id=CNEAIT_DEMO_SERVER_WEB_APP&redirect_uri=http://localhost:3050/api/close-session
CREATE_ACCOUNT_URL=https://accounts.int.ea.com/connect/auth?response_type=code&client_id=CNEAIT_DEMO_SERVER_WEB_APP&display=junoWeb/create&redirect_uri=http://localhost:3050/api/authenticate
CLIENT_ID=CNEAIT_DEMO_SERVER_WEB_APP
LOGIN_REDIRECT_URI=http://localhost:3050/api/authenticate
# Mock API/Java API
CADDY_FILE=./java.Caddyfile
OPERATIONS_SERVICE=operations-api
# Java Operations Service
APP_ENV=local
APP_DEBUG=true
CERTIFICATE_BUCKET=eait-playerexp-cn-certificates-preprod
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
OPERATIONS_API_BASE_URL=https://dev-intrnl-services.cn.ea.com/cn-operations-api
ACCESS_TOKEN_BASE_URL=https://dev-intrnl-services.cn.ea.com/security
CONTENT_SCANNING_API_BASE_URL=https://dev-services.cn.ea.com/cn-content-scanning-api
METADATA_API_BASE_URL=http://localhost:3010/cn-metadata-api
CONTENT_SUBMISSION_BASE_URL=https://dev-services.cn.ea.com/cn-content-submission-api/
CONTENT_MANAGEMENT_API_BASE_URL=https://dev-services.cn.ea.com/cn-content-management-api
LEGAL_API_BASE_URL=https://dev-services.cn.ea.com/cn-legal-api
PAYMENTS_API_BASE_URL=https://dev-services.cn.ea.com/cn-payments-api
COMMUNICATIONS_API_BASE_URL=https://dev-services.cn.ea.com/cn-communications-api
OPPORTUNITIES_API_BASE_URL=https://dev-services.cn.ea.com/cn-opportunities-api
NOTIFICATIONS_MFE_BASE_URL=http://localhost:3001/cn-notifications-mfe
APPLICATIONS_MFE_BASE_URL=http://localhost:3003/cn-applications-mfe
BASE_PATH=http://localhost:3050
API_CLIENT_ID=
API_CLIENT_SECRET=
# Session
COOKIE_PASSWORD=
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax
COOKIE_SECURE=false
COOKIE_DOMAIN=
SESSION_COOKIE_NAME=CNEAIT_LOCAL_SESSION_COOKIE
SESSION_PROXY=false
SESSION_TTL=14400
# Google Analytics
GTM_AUTH=
GTM_PREVIEW=
CACHE_PREFIX=web_dev_
SENTRY_DSN=
ARTIFACTORY=docker.artifactory.ea.com
HTTP_REQUEST_TIMEOUT=15000
SUPPORTED_LOCALES=["en-us","ja-jp","de-de","fr-fr","es-es","it-it"]
REDIS_PORT=6379
REDIS_HOST=localhost
REDIS_SCALE_READ=slave
AMPLITUDE_API_KEY=
AMPLITUDE_ENV=
ANALYTICS_SAMPLE_RATE=
PROGRAM_CODE=sims_creator_program
DEFAULT_FRANCHISE_IMAGE=https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/franchises/ea-no-franchise.png
FLAG_OPPORTUNITIES_API_CLIENT=true
SERVICE_NAME="cn-sims-ugx"
ONBOARDING_MFE_BASE_URL=http://localhost:3002/cn-onboarding-mfe
FLAG_INITIAL_MESSAGE=true
MICROCOPY_DASHBOARD_PAGE=["MICROCOPY: Dashboard"]
SENTRY_DSN=
MICROCOPY_NOTIFICATIONS_PAGE=["MICROCOPY: NotificationsPage","MICROCOPY: Programs", "MICROCOPY: SocialAccounts", "MICROCOPY: Common"]
MICROCOPY_INFORMATION_PAGE=["MICROCOPY: Information", "MICROCOPY: Breadcrumb", "MICROCOPY: CommunicationPreferences", "MICROCOPY: Common", "MICROCOPY: ConnectAccounts"]
INTERESTED_CREATOR=true
INTERESTED_CREATOR_REAPPLY_PERIOD=true
MENUITEM_NAMES='{"sims_creator_program":{"label":"UGX","gradients":["#2E900A","#6FF049"]},"affiliate":{"label":"Support a Creator","gradients":["#6A30D3","#A133DD"]},"creator_network":{"label":"Creator Network","gradients":["#2D2EFE","#4CCEF7"]}}'
ALLOWED_NOTIFICATIONS=PROFILE_INCOMPLETE,PROFILE_COMPLETE
NOTIFICATIONS_MFE_NAME=cn-notifications-mfe
LOG_LEVEL=INFO
MICROCOPY_NO_ACCOUNT_PAGE=["MICROCOPY: NoAccount","MICROCOPY: Common"]
MICROCOPY_AGE_RESTRICTION_PAGE=["MICROCOPY: AgeRestriction","MICROCOPY: Common"]
MICROCOPY_ERROR_PAGE=["MICROCOPY: ErrorPage", "MICROCOPY: Common"]
MICROCOPY_SIGNUP_COMPLETE_PAGE=["MICROCOPY: SignupComplete","MICROCOPY: Common"]
SENTRY_DSN=

# Connected Accounts OAuth
YOUTUBE_CLIENT_ID=
YOUTUBE_CLIENT_SECRET=
YOUTUBE_CLIENT_REDIRECT_URI=http://localhost:3050/api/youtube-connect
YOUTUBE_SCOPES=profile,https://www.googleapis.com/auth/youtube.readonly,https://www.googleapis.com/auth/yt-analytics.readonly
TWITCH_CLIENT_ID=
TWITCH_CLIENT_REDIRECT_URI=http://localhost:3050/api/twitch-connect
TWITCH_SCOPES=user_read+channel_read
FACEBOOK_CLIENT_ID=
FACEBOOK_CLIENT_REDIRECT_URI=http://localhost:3050/api/facebook-connect
FACEBOOK_STATE=
FACEBOOK_SCOPES=email,public_profile,read_insights,pages_show_list,pages_read_engagement,pages_read_user_content
FACEBOOK_API_VERSION=v18.0
INSTAGRAM_CLIENT_ID=
INSTAGRAM_CLIENT_REDIRECT_URI=http://localhost:3050/api/instagram-connect
INSTAGRAM_STATE=
INSTAGRAM_SCOPE=email,public_profile,read_insights,pages_show_list,instagram_basic,instagram_manage_insights,business_management
INSTAGRAM_API_VERSION=v18.0
TIKTOK_CLIENT_ID=
TIKTOK_CLIENT_REDIRECT_URI=http://localhost:3050/api/tiktok-connect
TIKTOK_SCOPES=user.info.profile,user.info.stats,video.list
TIKTOK_AUTH_BASE_URI=https://www.tiktok.com/v2/auth/authorize
# Connected Accounts OAuth
WATERMARKS_URL=https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/watermark/v1/EA+CreatorNetwork+Logoset.zip
RE_APPLY_THRESHOLD_IN_DAYS=90
INITIAL_MESSAGE_TITLE=
FLAG_OBSERVABILITY=false
OTLP_TRACE_EXPORTER_URL=http://localhost:4317/v1/traces
FLAG_COMMUNICATIONS_API_CLIENT=true
YOUTUBE_HOSTS=["youtube.com","m.youtube.com"]
TWITCH_HOSTS=["twitch.tv"]
INSTAGRAM_HOSTS=["instagram.com"]
FACEBOOK_HOSTS=["facebook.com"]
TIKTOK_HOSTS=["tiktok.com"]
DISCORD_CLIENT_ID=
DISCORD_CLIENT_REDIRECT_URI=http://localhost:3050/api/discord-connect
DISCORD_SCOPES='email guilds guilds.join gdm.join identify'
DEFAULT_AVATAR_IMAGE=https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar-sims.svg
PAYMENTS_DEFAULT_START_DATE=2021-09-01
MICROCOPY_PAYMENTS_PAGE=["MICROCOPY: Common", "MICROCOPY: NotificationsPage", "MICROCOPY: SocialAccounts", "MICROCOPY: Programs", "MICROCOPY: TimeRangeFilter", "MICROCOPY: PaymentDetails"]
MICROCOPY_COMMUNICATION_PREFERENCES_PAGE=["MICROCOPY: CommunicationPreferences",  "MICROCOPY: Common", "MICROCOPY: ConnectAccounts", "MICROCOPY: Breadcrumb"]
FLAG_COUNTRIES_BY_TYPE=true
MICROCOPY_TERMS_AND_CONDITION_PAGE=["MICROCOPY: TermsAndConditions", "MICROCOPY: Breadcrumb", "MICROCOPY: Common", "MICROCOPY: Information"]
PACTSAFE_ID=
MICROCOPY_PAYMENT_INFORMATION_PAGE=["MICROCOPY: PaymentInformation", "MICROCOPY: Breadcrumb", "MICROCOPY: Common"]
NOTIFICATION_BASE_URLS='{"creator_network":"https://dev-creatornetwork.ea.com","affiliate":"https://dev-www.ea.com/support-a-creator","sims_creator_program":"https://dev-simsugx.ea.com"}'
SINGLE_PROGRAM_NOTIFICATIONS=false
DEFAULT_NOTIFICATION_PROGRAM=creator_network
MICROCOPY_DOCUMENTATION_PAGE=["MICROCOPY: Common"]
CREATORS_API_BASE_URL=https://dev-services.cn.ea.com/cn-creators-api
TERMS_STATUS_CACHE_TTL=86400
