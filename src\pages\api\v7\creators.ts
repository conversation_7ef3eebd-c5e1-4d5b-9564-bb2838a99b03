import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../../ApiContainer";
import { createRouter } from "next-connect";
import ViewCreatorWithProgramsController from "../../../server/creators/ViewCreatorWithProgramsController";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ViewCreatorWithProgramsController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
