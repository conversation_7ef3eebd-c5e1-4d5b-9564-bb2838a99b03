import { ArticleType } from "@components/ArticlePage/ArticlePage";
import { SystemInformation } from "@components/RichText/RichText";
import Link, { LinkType } from "@components/RichTextComponents/Link/Link";
import React from "react";

export type SubCategoryType = "SubCategory";
export type Link = {
  sys: SystemInformation;
  title: string;
  administrativeTitle: string;
  url: string | null;
  page: {
    slug: string;
    type: ArticleType;
  };
  entryType: LinkType;
  openInNewTab: boolean;
};
export type SubCategory = {
  sys: SystemInformation;
  title: string;
  internalName: string;
  links: Link[];
  entryType: SubCategoryType;
};

const SubCategoryCard = ({ title, links }: SubCategory) => {
  return (
    <div className="subcategory-card-container" data-testid="subcategory-card-container">
      <h2 className="subcategory-card-header">{title}</h2>
      <div className="subcategory-card-links">
        {links.map((link: Link) => (
          <Link key={link.sys.id} {...link}>
            {link.title}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default SubCategoryCard;
