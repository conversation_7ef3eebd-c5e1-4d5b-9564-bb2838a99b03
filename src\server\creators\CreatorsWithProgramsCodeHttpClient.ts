import { Inject, Service } from "typedi";
import CreatorWithCreatorPrograms from "./CreatorWithCreatorPrograms";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import SaveCreatorProfileInput from "./SaveCreatorProfile/SaveCreatorProfileInput";

@Service()
class CreatorsWithProgramsHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithCreatorPrograms> {
    const response = await this.client.get(`/v7/creators/${id}`);
    return Promise.resolve(CreatorWithCreatorPrograms.fromApi(response.data));
  }

  async update(input: SaveCreatorProfileInput): Promise<void> {
    await this.client.put(`/v2/creators/${input.id}`, { body: input });
  }
}

export default CreatorsWithProgramsHttpClient;
