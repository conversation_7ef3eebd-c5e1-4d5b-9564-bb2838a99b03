import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ViewCreatorWithProgramsController from "../../../../src/server/creators/ViewCreatorWithProgramsController";
import CreatorsWithProgramsHttpClient from "../../../../src/server/creators/CreatorsWithProgramsHttpClient";
import { aCreatorWithFlaggedStatus } from "../../../factories/creators/CreatorWithFlaggedStatus";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("ViewCreatorWithProgramsController", () => {
  let controller: ViewCreatorWithProgramsController;

  beforeEach(() => jest.clearAllMocks());

  it("shows a creator with program codes", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/v7/creators`,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const creator = aCreatorWithFlaggedStatus();
    const creators = { withId: jest.fn().mockResolvedValue(creator) };
    controller = new ViewCreatorWithProgramsController((creators as unknown) as CreatorsWithProgramsHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creator);
    expect(creators.withId).toHaveBeenCalledTimes(1);
    expect(creators.withId).toHaveBeenCalledWith(userId);
  });
});
