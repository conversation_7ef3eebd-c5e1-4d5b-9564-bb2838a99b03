import { ArticleType } from "@components/ArticlePage/ArticlePage";
import { useDependency } from "@src/context/DependencyContext";
import React from "react";

export type LinkType = "Link";
interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  id?: string;
  children: React.ReactNode;
  page?: {
    slug: string;
    type: ArticleType;
  };
  url: string;
  openInNewTab?: boolean;
  className?: string;
}

const Link = ({ id, children, className, url, openInNewTab, page }: LinkProps) => {
  const {
    configuration: { BASE_PATH }
  } = useDependency();
  return (
    <a
      href={page?.slug ? `${BASE_PATH}/articles/${page.slug}` : url}
      target={openInNewTab ? "_blank" : "_self"}
      className={className}
      data-testid={id}
    >
      {children}
    </a>
  );
};

export default Link;
