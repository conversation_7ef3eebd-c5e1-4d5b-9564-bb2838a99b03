import "reflect-metadata";
import React from "react";
import Head from "next/head";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { ErrorPageLabels } from "@src/server/contentManagement/ErrorPageMapper";
import Layout, { LayoutBody, LayoutHeader } from "@components/Layout";
import TopNavBar from "@components/header/TopNavBar";
import { useRouter } from "next/router";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import accessErrorPageProps from "@src/serverprops/AccessErrorPageProps";
import { GetServerSidePropsResult } from "next";

export type AccessErrorProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: ErrorPageLabels & CommonPageLabels;
};

export default function AccessError({ pageLabels }: AccessErrorProps) {
  const { errorLabels } = pageLabels;
  const { locale } = useRouter();
  return (
    <>
      <Head>
        <title>{errorLabels.accessErrorTitle}</title>
      </Head>
      <Layout>
        <LayoutHeader pageTitle="" tabTitle={errorLabels.accessErrorTitle}>
          <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
        </LayoutHeader>
        <LayoutBody className="interested-creator-layout">
          <div className="access-error-container">
            <div className="access-error-bg"></div>
            <div className="access-error-main-content access-error-content">
              <div className="horizontal-line top-line"></div>
              <div className="access-error-content-container">
                <h1 className="access-error-title">{errorLabels.accessErrorTitle}</h1>
                <div className="access-error-body">{errorLabels.accessErrorSubtitle}</div>
              </div>
              <div className="horizontal-line bottom-line"></div>
            </div>
          </div>
        </LayoutBody>
      </Layout>
    </>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();

  router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(accessErrorPageProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<AccessErrorProps>;
};
