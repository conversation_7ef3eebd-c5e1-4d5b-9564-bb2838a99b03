import "reflect-metadata";
import AgeRestriction from "@src/pages/interested-creators/age-restriction";
import { AgeRestrictionPageLabels } from "@src/server/contentManagement/AgeRestrictionPageMapper";
import { render, screen } from "@testing-library/react";
import { useRouter } from "next/router";

jest.mock("next/router");

describe("AgeRestriction", () => {
  const router = {
    locale: "en-us",
    push: jest.fn().mockResolvedValue(true)
  };
  const ageRestrictionProps = {
    pageLabels: {
      ageRestrictionLabels: {
        title: "Age Restriction Title",
        subTitle: "Age Restriction SubTitle"
      }
    } as AgeRestrictionPageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("renders the AgeRestriction component correctly", () => {
    render(<AgeRestriction {...ageRestrictionProps} />);
    expect(screen.getByTestId("the-sims-creator-program")).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: "Age Restriction Title" })).toBeInTheDocument();
    expect(screen.getByText("Age Restriction SubTitle")).toBeInTheDocument();
  });

  it("verifies layout elements are present", () => {
    render(<AgeRestriction {...ageRestrictionProps} />);
    expect(screen.getByRole("heading", { name: "Age Restriction Title" })).toBeInTheDocument();
    expect(screen.getByText("Age Restriction SubTitle")).toBeInTheDocument();
    expect(screen.getAllByRole("img")).toHaveLength(1);
  });
});
