import "reflect-metadata";
import { useTranslation } from "next-i18next";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import { memo, useMemo } from "react";
import BrowserAnalytics, { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import PaymentInformationPage from "@components/pages/payment-information/PaymentInformationPage";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import Footer from "@components/footer/ProgramFooter";
import Header from "@components/header/header";
import { NotificationsLabels, TopNavigationPageLabels } from "@components/ProgramTopNavigation";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { TimeRangeFilterPageLabels } from "@src/server/contentManagement/TimeRangeFilterPageMapper";
import { PaymentDetailsPageLabels } from "@src/server/contentManagement/PaymentDetailsPageMapper";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import paymentInformationProps from "@src/serverprops/PaymentInformationProps";

export type PaymentInformationProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  analytics?: BrowserAnalytics;
  PAYMENTS_DEFAULT_START_DATE: string;
  locale: string;
  pageLabels: CommonPageLabels & TimeRangeFilterPageLabels & PaymentDetailsPageLabels;
};

export default memo(function PaymentInformation({
  user,
  analytics = new BrowserAnalytics(user),
  PAYMENTS_DEFAULT_START_DATE,
  locale,
  pageLabels
}: PaymentInformationProps) {
  const { commonPageLabels, timeRangeFilterPageLabels, paymentDetailsPageLabels } = pageLabels;

  const { t } = useTranslation(["common", "payment-information", "notifications", "connect-accounts", "opportunities"]);

  const { layout, paymentLabels } = useMemo(() => {
    const paymentLabels = {
      layout: commonPageLabels,
      paymentLabels: {
        paymentInfoLabels: {
          ...paymentDetailsPageLabels.transaction,
          buttons: {
            next: commonPageLabels.next,
            prev: commonPageLabels.prev,
            close: commonPageLabels.close
          }
        },
        paymentBannerLabels: paymentDetailsPageLabels.banner,
        paymentDetailsLabels: paymentDetailsPageLabels.overview,
        paymentHistoryGridLabels: paymentDetailsPageLabels.transactionHistory,
        paymentFilterLabels: {
          ...timeRangeFilterPageLabels,
          buttons: { ok: commonPageLabels.ok, cancel: commonPageLabels.cancel },
          header: { calendar: commonPageLabels.calendar },
          filters: timeRangeFilterPageLabels.allFilters
        },
        buttonLabel: commonPageLabels.remove
      }
    };
    return paymentLabels;
  }, [commonPageLabels, timeRangeFilterPageLabels, paymentDetailsPageLabels]);

  const notificationsLabels = useMemo(() => mapNotificationsBellLabels(t), [t]);

  const headerLabels = useMemo(() => {
    const {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      myProfile,
      opportunities,
      documentation,
      myContent,
      signout,
      notifications
    } = commonPageLabels;

    return ({
      commonPageLabels: {
        requestToJoin,
        logIn,
        signIn,
        home,
        faqs,
        dashboard,
        opportunities,
        myContent,
        documentation,
        myProfile,
        signout,
        notifications
      },
      notificationsBellLabels: notificationsLabels
    } as unknown) as { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };
  }, [commonPageLabels, notificationsLabels]);

  const footerLabels = useMemo(() => {
    const {
      dashboard,
      opportunities,
      myContent,
      documentation,
      faqs,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy
    } = commonPageLabels;

    return {
      commonPageLabels: {
        dashboard,
        opportunities,
        myContent,
        documentation,
        faq,
        policies,
        legal,
        disclaimer,
        updates,
        terms,
        privacy,
        rights,
        report,
        faqs,
        disclosure,
        policy
      }
    };
  }, [commonPageLabels]);

  return (
    <Layout>
      <LayoutHeader
        pageTitle={commonPageLabels.paymentInformation}
        tabTitle={`${commonPageLabels.theSims} | ${commonPageLabels.paymentInformation}`}
      >
        <Header user={user} labels={headerLabels} />
      </LayoutHeader>
      <LayoutBody className="payment-information-container" showSideNavigation={!!user}>
        <PaymentInformationPage
          {...{
            labels: paymentLabels,
            user,
            unhandledError: layout.unhandledError,
            PAYMENTS_DEFAULT_START_DATE,
            analytics
          }}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer analytics={undefined} locale={locale} labels={footerLabels} />
      </LayoutFooter>
    </Layout>
  );
});

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(paymentInformationProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<PaymentInformationProps>;
};
