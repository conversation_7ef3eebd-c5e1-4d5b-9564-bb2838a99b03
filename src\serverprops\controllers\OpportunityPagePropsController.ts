import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "config";
import { OpportunityPageProps } from "../OpportunityProps";
import { OAuthError } from "@src/server/channels/tiktok/ConnectTikTokAccountController";
import { FacebookPages } from "@src/server/channels/ConnectedAccountsHttpClient";

export default class OpportunityPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<OpportunityPageProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<OpportunityPageProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    const accountConnected = this.hasSession(req, "accountType") ? (this.session(req, "accountType") as boolean) : null;
    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};
    const error = this.hasSession(req, "error") ? (this.session(req, "error") as OAuthError) : null;
    const invalidTikTokScope = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : false;

    await this.removeFromSession(req, "error");
    await this.removeFromSession(req, "INVALID_TIKTOK_SCOPE");

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        WATERMARKS_URL: config.WATERMARKS_URL,
        YOUTUBE_HOSTS: config.YOUTUBE_HOSTS,
        TWITCH_HOSTS: config.TWITCH_HOSTS,
        INSTAGRAM_HOSTS: config.INSTAGRAM_HOSTS,
        FACEBOOK_HOSTS: config.FACEBOOK_HOSTS,
        TIKTOK_HOSTS: config.TIKTOK_HOSTS,
        accountConnected,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "opportunities",
          "notifications",
          "add-content",
          "connect-accounts",
          "content-submission",
          "content-submission-instagram-guide",
          "content-submission-facebook-guide",
          "content-submission-youtube-guide",
          "content-submission-twitch-guide",
          "content-submission-tiktok-guide",
          "content-submission-website",
          "submit-social-media-content",
          "content-submission-upload",
          "my-content",
          "payment-information",
          "point-of-contact"
        ])),
        pages,
        error,
        referer: req.headers?.referer || null,
        invalidTikTokScope
      }
    };
  }
}
