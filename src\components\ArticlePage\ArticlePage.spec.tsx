import { render, screen } from "@testing-library/react";
import ArticlePage from "./ArticlePage";
import Random from "__tests__/factories/Random";
import { aSystemInformation } from "__tests__/factories/documentation/documentation";
import { axe } from "jest-axe";

describe("ArticlePage", () => {
  const articlePageProps = {
    sys: aSystemInformation(),
    title: Random.string(),
    body: {
      richText:
        '{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Sample Text","marks":[],"data":{}}]}',
      embeddedItems: []
    },
    pageBreadCrumbs: [
      { label: "Documentation", link: "/documentation", isActive: false },
      { label: "Articles", link: "/articles", isActive: true }
    ]
  };

  it("shows title and content", () => {
    render(<ArticlePage {...articlePageProps} />);

    expect(screen.getByRole("heading", { name: articlePageProps.title })).toBeInTheDocument();
    expect(screen.getByTestId("richText")).toBeInTheDocument();
  });

  it("shows breadcrumbs with correct labels", () => {
    render(<ArticlePage {...articlePageProps} />);

    expect(screen.getByTestId("breadcrumbs")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<ArticlePage {...articlePageProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
