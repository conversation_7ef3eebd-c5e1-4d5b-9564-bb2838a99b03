import { axe } from "jest-axe";
import Custom401 from "../../../src/pages/401";
import { render, screen } from "@testing-library/react";
import config from "config";
import { mockMatchMedia } from "__tests__/helpers/window";
import { useDependency } from "@src/context/DependencyContext";
import { useRouter } from "next/router";

jest.mock("next/config", () => () => ({
  publicRuntimeConfig: {
    SUPPORTED_LOCALES: JSON.stringify(config.SUPPORTED_LOCALES)
  }
}));

jest.mock("../../../src/context/DependencyContext");
describe("Custom401", () => {
  mockMatchMedia();
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  it("shows unauthorized message", () => {
    const errorProps = {
      code: "401",
      title: "Unauthorized."
    };
    render(<Custom401 />);

    expect(screen.getByRole("heading", { name: "401" })).toBeInTheDocument();
    expect(screen.getByText(errorProps.code)).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<Custom401 />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
