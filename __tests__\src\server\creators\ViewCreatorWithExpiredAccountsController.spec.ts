import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ViewCreatorWithExpiredAccountsController from "../../../../src/server/creators/ViewCreatorWithExpiredAccountsController";
import { aCreator } from "../../../factories/creators/Creator";
import CreatorsWithDisconnectedChannelsHttpClient from "../../../../src/server/creators/CreatorsWithDisconnectedChannelsHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("CreatorWithExpiredAccountsController", () => {
  let controller: ViewCreatorWithExpiredAccountsController;

  beforeEach(() => jest.clearAllMocks());

  it("get the creator with expired accounts", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/v2/creators`,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const creator = aCreator();
    const creators = { withId: jest.fn().mockResolvedValue(creator) };
    controller = new ViewCreatorWithExpiredAccountsController(
      (creators as unknown) as CreatorsWithDisconnectedChannelsHttpClient
    );

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creator);
    expect(creators.withId).toHaveBeenCalledTimes(1);
    expect(creators.withId).toHaveBeenCalledWith(userId);
  });
});
