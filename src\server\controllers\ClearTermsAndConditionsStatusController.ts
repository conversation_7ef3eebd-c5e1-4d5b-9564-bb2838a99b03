import { NextApiResponse } from "next";
import { Service } from "typedi";
import CachedTermsAndConditions from "../pactSafe/CachedTermsAndConditions";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import config from "config";

@Service()
class ClearTermsAndConditionsStatusController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly termsAndConditions: CachedTermsAndConditions) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const creator = this.identity(req);
    const { locale } = req.query;

    await this.termsAndConditions.signedStatusWithProgram(creator.id, locale as string, config.PROGRAM_CODE);

    this.json(res, {});
  }
}

export default ClearTermsAndConditionsStatusController;
