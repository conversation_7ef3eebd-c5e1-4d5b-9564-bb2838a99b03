import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { aParticipationDetails } from "__tests__/factories/opportunities/ParticipationDetails";
import ParticipationsHttpClient from "@src/server/opportunities/ParticipationsHttpClient";
import ParticipationDetailsController from "@src/server/opportunities/ParticipationDetailsController";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";

describe("ParticipationDetailsController", () => {
  let controller: ParticipationDetailsController;
  const session = { save: jest.fn() };

  beforeEach(() => jest.clearAllMocks());

  it("get participation details successfully", async () => {
    const creatorId = "41b16e33";
    const opportunityId = "000000000000Abc";
    const participationDetailsResponse = [aParticipationDetails()];
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: `v1/creator/${creatorId}/participation-details`,
      body: { opportunityId },
      session: {
        ...session,
        identity: Identity.fromStored(({
          id: creatorId
        } as unknown) as StoredIdentity)
      }
    });
    const participations = ({
      withCreatorCode: jest.fn().mockResolvedValue(participationDetailsResponse)
    } as unknown) as ParticipationsHttpClient;
    controller = new ParticipationDetailsController(participations);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(participationDetailsResponse);
    expect(participations.withCreatorCode).toHaveBeenCalledTimes(1);
    expect(participations.withCreatorCode).toHaveBeenCalledWith(creatorId, opportunityId);
  });
});
