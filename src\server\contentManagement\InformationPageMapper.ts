import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type InformationPageLabels = {
  informationLabels: {
    messages: {
      firstName: string;
      lastNameTooLong: string;
      state: string;
      dateOfBirthInvalid: string;
      country: string;
      city: string;
      ageMustBe18OrOlder: string;
      street: string;
      dateOfBirth: string;
      firstNameTooLong: string;
      streetTooLong: string;
      stateTooLong: string;
      zipCode: string;
      zipCodeTooLong: string;
      cityTooLong: string;
      lastName: string;
      preferredNameTooLong: string;
      preferredPronoun: string;
      businessName: string;
      email: string;
    };
    labels: {
      state: string;
      city: string;
      country: string;
      preferredName: string;
      preferredPronouns: string;
      preferredPronoun: string;
      enterPronoun: string;
      selectProunoun: string;
      lastName: string;
      zipCode: string;
      EAID: string;
      eaEmailID: string;
      firstName: string;
      dateOfBirth: string;
      selectCountry: string;
      street: string;
    };
    interestedUserDescription2: string;
    interestedUserDescription1: string;
    basicInformation: string;
    informationPageTitle: string;
    infoTitle: string;
  };
};

export class InformationPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): InformationPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      informationLabels: {
        messages: {
          firstName: microCopy.get("information.messages.firstName"),
          lastNameTooLong: microCopy.get("information.messages.lastNameTooLong"),
          preferredNameTooLong: microCopy.get("information.messages.preferredNameTooLong"),
          preferredPronoun: microCopy.get("information.messages.preferredPronoun"),
          state: microCopy.get("information.messages.state"),
          dateOfBirthInvalid: microCopy.get("information.messages.dateOfBirthInvalid"),
          country: microCopy.get("information.messages.country"),
          city: microCopy.get("information.messages.city"),
          ageMustBe18OrOlder: microCopy.get("information.messages.ageMustBe18OrOlder"),
          street: microCopy.get("information.messages.street"),
          dateOfBirth: microCopy.get("information.messages.dateOfBirth"),
          firstNameTooLong: microCopy.get("information.messages.firstNameTooLong"),
          streetTooLong: microCopy.get("information.messages.streetTooLong"),
          stateTooLong: microCopy.get("information.messages.stateTooLong"),
          zipCode: microCopy.get("information.messages.zipCode"),
          zipCodeTooLong: microCopy.get("information.messages.zipCodeTooLong"),
          cityTooLong: microCopy.get("information.messages.cityTooLong"),
          lastName: microCopy.get("information.messages.lastName"),
          businessName: microCopy.get("information.messages.businessName"),
          email: microCopy.get("information.messages.email")
        },
        labels: {
          state: microCopy.get("information.labels.state"),
          city: microCopy.get("information.labels.city"),
          preferredName: microCopy.get("information.labels.preferredName"),
          preferredPronouns: microCopy.get("information.labels.preferredPronouns"),
          preferredPronoun: microCopy.get("information.labels.preferredPronoun"),
          enterPronoun: microCopy.get("information.labels.enterPronoun"),
          selectProunoun: microCopy.get("information.labels.selectProunoun"),
          country: microCopy.get("information.labels.country"),
          lastName: microCopy.get("information.labels.lastName"),
          zipCode: microCopy.get("information.labels.zipCode"),
          EAID: microCopy.get("information.labels.EAID"),
          eaEmailID: microCopy.get("information.eaEmailID"),
          firstName: microCopy.get("information.labels.firstName"),
          dateOfBirth: microCopy.get("information.labels.dateOfBirth"),
          selectCountry: microCopy.get("information.labels.selectCountry"),
          street: microCopy.get("information.labels.street")
        },
        interestedUserDescription2: microCopy.get("information.interestedUserDescription2"),
        interestedUserDescription1: microCopy.get("information.interestedUserDescription1"),
        basicInformation: microCopy.get("information.basicInformation"),
        informationPageTitle: microCopy.get("information.informationPageTitle"),
        infoTitle: microCopy.get("information.infoTitle")
      }
    };
  }
}
