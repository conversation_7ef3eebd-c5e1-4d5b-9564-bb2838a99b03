import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";
import ApiContainer from "@src/ApiContainer";
import ViewParticipationsWithProgramCodeController from "@src/server/opportunities/ViewParticipationsWithProgramCodeController";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ViewParticipationsWithProgramCodeController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
