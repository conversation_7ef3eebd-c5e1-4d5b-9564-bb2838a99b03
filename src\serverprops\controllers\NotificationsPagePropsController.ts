import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { NotificationsProps } from "@src/pages/notifications";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { NotificationPageLabels } from "@src/server/contentManagement/NotificationsPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export default class NotificationsPagePropController
  extends AuthenticatedRequestHandler
  implements Server<PERSON>ropsController<NotificationsProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<NotificationsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "notifications"
    )) as NotificationPageLabels & CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels,
        user: authenticatedUser,
        referer: req.headers?.referer || null,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "my-content",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
