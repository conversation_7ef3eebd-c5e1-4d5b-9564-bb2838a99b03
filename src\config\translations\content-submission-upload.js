export default function labelsUploadContent(t) {
  return {
    title: t("content-submission-upload:title"),
    fileUpload: t("content-submission-upload:fileUpload"),
    chooseFile: t("content-submission-upload:chooseFile"),
    noFileChoosen: t("content-submission-upload:noFileChoosen"),
    acceptedFormats: t("content-submission-upload:acceptedFormats"),
    maxFileSize: t("content-submission-upload:maxFileSize"),
    maxLimitMessage: t("content-submission-upload:maxLimitMessage"),
    invalidFileTypeMessage: t("content-submission-upload:invalidFileTypeMessage"),
    fileSelected: t("content-submission-upload:fileSelected"),
    fileUploading: t("content-submission-upload:fileUploading"),
    success: {
      title: t("content-submission-upload:success.title"),
      content: t("content-submission-upload:success.content")
    },
    error: {
      title: t("content-submission-upload:error.title"),
      content: t("content-submission-upload:error.content")
    },
    uploadFileProgress: t("content-submission-upload:uploadFileProgress"),
    removeSelectedFile: t("content-submission-upload:removeSelectedFile"),
    contentDescriptionRequired: t("content-submission-upload:contentDescriptionRequired"),
    contentDescriptionLongMessage: t("content-submission-upload:contentDescriptionLongMessage"),
    contentDescription: t("content-submission-upload:contentDescription"),
    contentDescriptionPlaceholder: t("content-submission-upload:contentDescriptionPlaceholder"),
    maxcharacterLimit: t("content-submission-upload:maxcharacterLimit")
  };
}
