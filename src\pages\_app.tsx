import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
import "@eait-playerexp-cn/onboarding-ui/dist/styles/index.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/the-sims-ugx.css";
import "@eait-playerexp-cn/notifications-ui/dist/styles/index.css";
import "../../styles/globals.css";
import { appWithTranslation } from "next-i18next";
import { withToastProvider } from "@eait-playerexp-cn/core-ui-kit";
import BuildInfo from "@src/components/buildInfo/BuildInfo";
import TraceProvider from "@src/components/TraceProvider";
import { AppProps } from "next/app";
import { AppWrapper } from "@src/context";
import { DependencyProvider } from "@src/context/DependencyContext";
import { errorHandlerFactory } from "@src/shared/errorHandling/errorHandler";
import { newBrowserClient } from "@eait-playerexp-cn/http-client";
import { useEffect, useState } from "react";
import { Router } from "next/router";
import Loading from "@components/Loading";

function MyApp({ Component, pageProps }: AppProps) {
  const config = pageProps.runtimeConfiguration || {};
  const { APP_ENV, APP_DEBUG, SUPPORTED_LOCALES, BUILD_VERSION } = config;
  const errorHandler = errorHandlerFactory(APP_DEBUG, SUPPORTED_LOCALES);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const notificationsClient = newBrowserClient({
    baseUrl: config.NOTIFICATIONS_MFE_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const metadataClient = newBrowserClient({
    baseUrl: config.METADATA_API_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const applicationsClient = newBrowserClient({
    baseUrl: config.APPLICATIONS_MFE_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const creatorsClient = newBrowserClient({
    baseUrl: config.CREATORS_API_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const onBoardingClient = newBrowserClient({
    baseUrl: config.ONBOARDING_MFE_BASE_URL,
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME
  });
  const client = newBrowserClient({
    timeoutInMilliseconds: config.HTTP_REQUEST_TIMEOUT,
    serviceName: config.SERVICE_NAME,
    baseUrl: config.SIMS_BASE_URL
  });

  useEffect(() => {
    const startLoading = () => setIsLoading(true);
    const stopLoading = () => setIsLoading(false);

    Router.events.on("routeChangeStart", startLoading);
    Router.events.on("routeChangeComplete", stopLoading);
    Router.events.on("routeChangeError", stopLoading);

    return () => {
      Router.events.off("routeChangeStart", startLoading);
      Router.events.off("routeChangeComplete", stopLoading);
      Router.events.off("routeChangeError", stopLoading);
    };
  }, []);

  return (
    <div className="app-container">
      <DependencyProvider
        metadataClient={metadataClient}
        configuration={config}
        errorHandler={errorHandler}
        notificationsClient={notificationsClient}
        client={client}
        creatorsClient={creatorsClient}
        onBoardingClient={onBoardingClient}
        applicationsClient={applicationsClient}
      >
        <TraceProvider>
          <AppWrapper>
            {isLoading && <Loading />}
            <Component {...pageProps} />
            {APP_ENV !== "PROD" && <BuildInfo version={BUILD_VERSION} />}
          </AppWrapper>
        </TraceProvider>
      </DependencyProvider>
    </div>
  );
}

export default withToastProvider(appWithTranslation(MyApp));
