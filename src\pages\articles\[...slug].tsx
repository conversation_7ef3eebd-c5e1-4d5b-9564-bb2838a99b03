import { EmbeddedItem } from "@components/RichText/RichText";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import Header from "@components/header/header";
import { useRouter } from "next/router";
import { useMemo } from "react";
import { useTranslation } from "next-i18next";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import labelsDashboard from "@src/config/translations/dashboard";
import labelsMyContent from "@src/config/translations/my-content";
import labelsCommon from "@src/config/translations/common";
import { NotificationsLabels, TopNavigationPageLabels } from "@components/ProgramTopNavigation";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import Footer, { FooterPageLabels } from "@src/components/footer/ProgramFooter";
import ArticlePage, { ArticleType } from "@components/ArticlePage/ArticlePage";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import { GetServerSidePropsResult } from "next";
import articleProps from "@src/serverprops/ArticleProps";

export type ArticleRichTextBody = {
  richText: string;
  embeddedItems: EmbeddedItem[];
};

export type Article = {
  sys: {
    id: string;
    publishedAt: string;
    firstPublishedAt: string;
    publishedVersion: number;
  };
  title: string;
  slug: string;
  body: ArticleRichTextBody;
  entryType: ArticleType;
};

export type ArticleProps = {
  runtimeConfiguration?: Record<string, unknown>;
  article: Article;
  slug: string[];
  user: AuthenticatedUser | null;
  locale: string;
};

export default function Article({ article, slug, user, locale }: ArticleProps) {
  const { sys, title, body } = article;
  const router = useRouter();
  const { t } = useTranslation([
    "common",
    "dashboard",
    "my-content",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { layout, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      dashboardLabels: labelsDashboard(t),
      layout: { ...labelsCommon(t), footer: { locale: router.locale, labels: labelsCommon(t).footer } },
      notificationsLabels: notificationBellLabels,
      myContentLabels: labelsMyContent(t)
    };
    return labels;
  }, [t]);

  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    signout,
    notifications,
    opportunities,
    myContent,
    documentation
  } = layout.header;
  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;
  const labels = ({
    commonPageLabels: {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      myProfile,
      signout,
      how,
      reward,
      perks,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy,
      notifications
    },
    notificationsBellLabels: notificationsLabels
  } as unknown) as { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };

  const footerLabels: FooterPageLabels = {
    commonPageLabels: {
      dashboard: dashboard,
      opportunities: opportunities,
      myContent: myContent,
      documentation: documentation,
      faq: faq,
      legal: legal,
      disclaimer: disclaimer,
      updates: updates,
      terms: terms,
      privacy: privacy,
      rights: rights,
      report: report,
      faqs: faqs,
      disclosure: disclosure,
      policy: policy,
      policies: policies
    }
  };

  const pageBreadCrumbs = [
    { label: documentation, link: "/documentation", isActive: false },
    { label: title, link: `/articles/${slug}`, isActive: true }
  ];

  return (
    <Layout>
      <LayoutHeader pageTitle={title} tabTitle={`${layout.theSims} | ${title}`}>
        <Header labels={labels} user={user} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={!!user}>
        <ArticlePage {...{ sys, title, body, pageBreadCrumbs }} />
      </LayoutBody>
      <LayoutFooter>
        <Footer locale={locale} labels={footerLabels} analytics={undefined} />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale, query }) => {
  const { slug } = query;
  const router = createRouter();
  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(articleProps(locale, slug));

  return (await router.run(req, res)) as GetServerSidePropsResult<ArticleProps>;
};
