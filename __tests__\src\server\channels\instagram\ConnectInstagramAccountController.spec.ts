import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectInstagramAccountController from "@src/server/channels/instagram/ConnectInstagramAccountController";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";

describe("ConnectInstagramAccountController", () => {
  let controller: ConnectInstagramAccountController;
  const redirectUrl = new URL("http://localhost:3040/api/instagram-connect");
  let connectedAccounts;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    connectedAccounts = ({ connectInstagramAccount: jest.fn() } as unknown) as ConnectedAccountsHttpClient;
    controller = new ConnectInstagramAccountController(connectedAccounts, redirectUrl);
  });

  it("connects an Instagram account for a creator", async () => {
    const code = "41b16e33-7fcc-4ec2-abf3-2d04c2662ece";
    const creatorId = "8c4db184-9735-4a07-8ebc-9f7f6d8b46c4";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/instagram-connect?code=${code}`,
      session: {
        ...session,
        identity: Identity.fromStored({
          type: "CREATOR",
          id: creatorId
        } as StoredIdentity)
      }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
    expect(req.session.accountType).toEqual("Instagram");
    expect(req.session.save).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectInstagramAccount).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectInstagramAccount).toHaveBeenCalledWith({
      code,
      creatorId,
      nucleusId: undefined,
      redirectUri: redirectUrl.toString()
    });
  });
});
