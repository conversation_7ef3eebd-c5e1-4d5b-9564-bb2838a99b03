import "reflect-metadata";
import { useTranslation } from "next-i18next";
import { useAppContext } from "../../src/context";
import Link from "next/link";
import { Fragment, memo, useCallback, useEffect, useMemo, useState } from "react";
import { ContentCard, OpportunityCardV2, Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import classNames from "classnames";
import SupportACreatorModal from "@components/supportACreatorModal/SupportACreatorModal";
import EventDetailsModal from "@components/eventDetailsModal/EventDetailsModal";
import GameCodeModal from "@components/gameCodeModal/GameCodeModal";
import MorePerksModal from "@components/morePerksModal/MorePerksModal";
import Pagination from "../components/pagination/Pagination";
import labelsCommon from "../../src/config/translations/common";
import labelsDashboard from "../../src/config/translations/dashboard";
import labelsOpportunities from "../../src/config/translations/opportunities";
import { useRouter } from "next/router";
import {
  ACTIVE_CONTENT_SUBMISSION,
  ACTIVE_GAME_CODE,
  CREATOR_CODE_DETAILS,
  ERROR,
  GET_PLATFORMS,
  LOADING,
  onToastClose,
  SESSION_USER,
  toastContent,
  useIsMounted,
  VALIDATION_ERROR
} from "../utils";
import {
  COMPLETED as COMPLETED_STATUS,
  INVITED as INVITED_STATUS,
  JOINED as JOINED_STATUS,
  PAST as PAST_STATUS
} from "../utils/constants";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { useDependency } from "../context/DependencyContext";
import OpportunityService from "../../src/services/OpportunityService";
import OperationsService from "../../src/services/OperationsService";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Loading from "../components/loading/Loading";
import ErrorPage from "./_error";
import CreatorCodeCard from "../components/creatorCodeCard/CreatorCodeCard";
import Header from "../components/header/header";
import { mapNotificationsBellLabels } from "../config/translations/mappers/notifications";
import Footer from "../components/footer/ProgramFooter";
import labelsMyContent from "../../src/config/translations/my-content";
import SubmittedContentService from "../../src/services/SubmittedContentService";
import errorLogger from "../../src/serverprops/middleware/ErrorLogger";
import initializeSession from "../../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../../src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "../../src/serverprops/middleware/VerifyAccessToProgram";
import dashboardProps from "../../src/serverprops/DashboardProps";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "../../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import { createRouter } from "next-connect";

const JOINED = "Joined";
const PAST = "Past";
const INVITED = "Invited";
const PAGE_SIZE = 5;
const PAGE = 1;
const DEFAULT_PAGE_NUMBER = 1;
const SUBMITTED_CONTENT_PAGE_SIZE = 3;

let activeGameCodes;

export const getPlatforms = async (metadataService, stableDispatch, errorHandler) => {
  try {
    return await metadataService.getPlatformsMatching({ type: "ALL" });
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export const getGameCodes = async (participationIds, stableDispatch, errorHandler, operationsService) => {
  try {
    const response = await operationsService.viewAssignedGameCodes(participationIds);
    return response.data;
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export const mapGameCodeToOpportunity = async (mappingInformation, errorHandler, operationsService) => {
  const {
    opportunities,
    participationIds,
    platforms,
    dispatch: stableDispatch,
    storedActiveGameCode
  } = mappingInformation;
  activeGameCodes = storedActiveGameCode;
  const gameCodes = await getGameCodes(participationIds, stableDispatch, errorHandler, operationsService);
  opportunities?.forEach((opportunity) => {
    gameCodes?.forEach(({ participationId, gameCode, platformId }) => {
      if (opportunity.participationId === participationId) {
        const selectedPlatform = platforms?.find((platform) => platform.value === platformId);
        if (selectedPlatform) {
          opportunity.gameCode = {
            platform: selectedPlatform.label.toUpperCase(),
            code: gameCode?.code,
            status: gameCode?.status
          };
        }
        activeGameCodes = {
          ...activeGameCodes,
          [opportunity.id]: opportunity?.gameCode?.status === "ASSIGNED"
        };
      }
    });
  });
  stableDispatch({ type: ACTIVE_GAME_CODE, data: activeGameCodes });
  return opportunities;
};

export const claimGameCode = async (participationId, stableDispatch, errorHandler, operationsService) => {
  try {
    await operationsService.claimGameCode(participationId);
  } catch (e) {
    errorHandler(stableDispatch, e);
  }
};

export const getActiveStatus = (opportunity, hasChangesRequested, hasNotSubmittedContent) => {
  return (
    hasChangesRequested ||
    (!hasChangesRequested && hasNotSubmittedContent && !opportunity?.contentSubmissionWindowHasEnded())
  );
};

export const formatEventAddress = (opportunity, opportunitiesLabels) => {
  const eventAddress = [
    opportunity.eventVenue(),
    opportunity.eventStreetAddress(),
    opportunity.eventAddress(),
    opportunity.eventCountryName()
  ]
    .filter((address) => address)
    .join(", ");
  return {
    label: opportunitiesLabels.eventDetails.eventLocation,
    value: eventAddress
  };
};

export const formatRemoteEvent = (opportunity, opportunitiesLabels) => {
  if (opportunity.event.meetingLink && opportunity.event.meetingPassword) {
    return {
      label: opportunitiesLabels.eventDetails.password,
      value: opportunity.event.meetingPassword
    };
  }
  if (!opportunity.event.meetingLink && !opportunity.event.meetingPassword) {
    return {
      label: opportunitiesLabels.eventDetails.info,
      value: opportunitiesLabels.remoteEvent.NoMeetingLinkInfo
    };
  }
};

export const formatEventWindow = (eventWindow, opportunitiesLabels) => {
  return [
    {
      label: opportunitiesLabels.eventDetails.eventStartTime,
      value: eventWindow.startDate
    },
    {
      label: opportunitiesLabels.eventDetails.eventEndTime,
      value: eventWindow.endDate
    }
  ];
};

export default function Dashboard({ user, locale }) {
  const router = useRouter();
  const {
    dispatch,
    state: {
      exceptionCode = null,
      isError,
      isLoading,
      isValidationError,
      creatorCodeDetails,
      platformDetails,
      activeGameCode,
      activeContentSubmission,
      sessionUser = null
    } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, [dispatch]);
  const { errorHandler, metadataClient, client } = useDependency();

  const isMounted = useIsMounted();
  const { error: errorToast } = useToast();
  const [pages, setPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [pastPages, setPastPages] = useState(null);
  const [pastCurrentPage, setPastCurrentPage] = useState(0);
  const [invitedPages, setInvitedPages] = useState(null);
  const [submittedContent, setSubmittedContent] = useState(null);
  const [showMyContentButton, setShowMyContentButton] = useState(false);
  const [contentsFeedback, setContentsFeedback] = useState(null);
  const [selectedContentId, setSelectedContentId] = useState("");
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [invitedCurrentPage, setInvitedCurrentPage] = useState(0);
  const [joinedTotal, setJoinedTotal] = useState(0);
  const [pastTotal, setPastTotal] = useState(0);
  const [invitedTotal, setInvitedTotal] = useState(0);
  const [joinedOpportunities, setJoinedOpportunities] = useState({});
  const [pastOpportunities, setPastOpportunities] = useState({});
  const [invitedOpportunities, setInvitedOpportunities] = useState({});
  const [currentPageJoinedOpportunities, setCurrentPageJoinedOpportunities] = useState([]);
  const [currentPagePastOpportunities, setCurrentPagePastOpportunities] = useState([]);
  const [currentPageInvitedOpportunities, setCurrentPageInvitedOpportunities] = useState([]);
  const [tabSelected, setTabSelected] = useState(INVITED);
  const [showMorePerksForInvited, setShowMorePerksForInvited] = useState(null);
  const [showMorePerksForPast, setShowMorePerksForPast] = useState(null);
  const [showSupportACreatorModal, setShowSupportACreatorModal] = useState(null);
  const [showEventDetailsModal, setShowEventDetailsModal] = useState(null);
  const [creatorCodeLoading, setCreatorCodeLoading] = useState(null);
  const [showGameCodeModal, setShowGameCodeModal] = useState(null);
  const [gameCodeLoading, setGameCodeLoading] = useState(null);
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const operationsService = useMemo(() => new OperationsService(client), [client]);
  const submittedContentService = useMemo(() => new SubmittedContentService(client), [client]);
  const opportunityService = useMemo(() => new OpportunityService(client), [client]);

  const { t } = useTranslation([
    "common",
    "dashboard",
    "my-content",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { layout, dashboardPageLabels, opportunitiesLabels, notificationsLabels, myContentLabels } = useMemo(() => {
    const labels = {
      layout: labelsCommon(t),
      dashboardPageLabels: labelsDashboard(t),
      opportunitiesLabels: labelsOpportunities(t),
      notificationsLabels: mapNotificationsBellLabels(t),
      myContentLabels: labelsMyContent(t)
    };
    labels.layout.contentCard.rejected = labels.layout.contentCard.notApproved;
    labels.layout.footer = { locale, labels: labels.layout.footer };
    return labels;
  }, [t]);

  const {
    main: { unhandledError }
  } = layout;

  let contentSubmissionActiveStatus = activeContentSubmission; // storing the data from context
  const statusLabel = {
    [PAST_STATUS]: dashboardPageLabels.past,
    [COMPLETED_STATUS]: opportunitiesLabels.completed
  };
  const formatSubmittedContent = useCallback(
    (content) => {
      let formattedContent = content;
      formattedContent.contents.forEach((content) => (content.contentTypeLabel = myContentLabels[content.contentType]));
      setSubmittedContent(formattedContent);
    },
    [myContentLabels]
  );

  useEffect(() => {
    if (isMounted()) {
      dispatch({ type: LOADING, data: true });
      submittedContentService
        .getSubmittedContentsWithProgramCode(SUBMITTED_CONTENT_PAGE_SIZE, DEFAULT_PAGE_NUMBER)
        .then((res) => {
          setShowMyContentButton(res.data.total > SUBMITTED_CONTENT_PAGE_SIZE);
          formatSubmittedContent(res.data);
        })
        .catch((e) => {
          errorHandler(dispatch, e);
        })
        .finally(() => {
          dispatch({ type: LOADING, data: false });
        });
    }
  }, [isMounted]);

  useEffect(() => {
    if (submittedContent && currentPageJoinedOpportunities && currentPagePastOpportunities) {
      dispatch({ type: LOADING, data: false });
    }
  }, [currentPageJoinedOpportunities, currentPagePastOpportunities]);

  const pageCalculation = (total) => {
    const pages = [];
    for (let i = 0; i < Math.ceil(total / PAGE_SIZE); i++) {
      pages.push(i + 1);
    }
    return pages;
  };

  const updateOpportunitiesStatus = async (data) => {
    const opportunitiesIds = [];
    data.forEach((element) => {
      opportunitiesIds.push(element.id);
    });
    if (opportunitiesIds.length === 0) return data;
    try {
      const res = await opportunityService.getParticipationStatusWithSubmissionInformation(opportunitiesIds);
      if (res?.data?.length === 0) {
        dispatch({ type: LOADING, data: false });
        return data;
      }
      data.map((opportunity, index) => {
        if (res.data[index].status === "JOINED") {
          opportunity.status = "JOINED";
          opportunity.isCompleted() && (opportunity.status = "COMPLETED");
          opportunity.participationId = res.data[index].participationId;
          opportunity.hasChangesRequested = res.data[index].hasChangesRequested;
          opportunity.hasNotSubmittedContent = res.data[index].hasNotSubmittedContent;
          contentSubmissionActiveStatus = {
            ...contentSubmissionActiveStatus,
            [opportunity.id]: getActiveStatus(
              opportunity,
              opportunity.hasChangesRequested,
              opportunity.hasNotSubmittedContent
            )
          };
        } else if (res.data[index].status === INVITED_STATUS) {
          opportunity.status = opportunity.isPastOpportunity() ? PAST_STATUS : INVITED_STATUS;
        }
      });
      dispatch({
        type: ACTIVE_CONTENT_SUBMISSION,
        data: contentSubmissionActiveStatus
      });
      dispatch({ type: LOADING, data: false });
    } catch (e) {
      errorHandler(dispatch, e);
    }
    return data;
  };

  const getMetaInformationForGameCodeOpportunities = (opportunities, platforms) => {
    return {
      opportunities,
      participationIds: opportunities
        .filter((opportunity) => opportunity.hasGameCodes && opportunity.participationId)
        .map((opportunity) => opportunity.participationId),
      platforms,
      dispatch,
      storedActiveGameCode: activeGameCode
    };
  };

  useEffect(() => {
    async function getOpportunityPages() {
      const platforms = await getPlatforms(metadataService, stableDispatch, errorHandler);
      const pastCurrentPage = PAGE;
      const pastCriteria = {
        page: pastCurrentPage,
        size: PAGE_SIZE,
        status: PAST_STATUS
      };
      // TODO: remove this once submiited content api is also added
      dispatch({ type: LOADING, data: true });
      // Past Opportunities
      opportunityService
        .matchingWithProgramDetails(pastCriteria)
        .then(async (res) => {
          if (res.data.total > 0) {
            let opportunities = res.data.opportunities;
            opportunities = await updateOpportunitiesStatus(opportunities);
            const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platforms);
            const { participationIds } = mappingInformation;
            if (participationIds.length > 0) {
              opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler, operationsService);
            }
            setCurrentPagePastOpportunities(opportunities);
            setPastOpportunities({
              ...pastOpportunities,
              [pastCurrentPage]: opportunities
            });
            setPastPages(pageCalculation(res.data.total));
            setPastTotal(res.data.total);
            setPastCurrentPage(pastCurrentPage);
            dispatch({ type: GET_PLATFORMS, data: platforms });
          }
        })
        .catch((e) => errorHandler(dispatch, e));

      const currentPage = PAGE;
      const joinedCriteria = {
        page: currentPage,
        size: PAGE_SIZE,
        status: "JOINED"
      };

      // Joined Opportunities
      opportunityService
        .matchingWithProgramDetails(joinedCriteria)
        .then(async (res) => {
          if (res.data.total > 0) {
            let opportunities = res.data.opportunities;
            opportunities = await updateOpportunitiesStatus(opportunities);
            const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platforms);
            const { participationIds } = mappingInformation;
            if (participationIds.length > 0) {
              opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
            }
            setCurrentPageJoinedOpportunities(opportunities);
            setJoinedOpportunities({
              ...joinedOpportunities,
              [currentPage]: opportunities
            });
            setPages(pageCalculation(res.data.total));
            setJoinedTotal(res.data.total);
            setCurrentPage(currentPage);
            dispatch({ type: GET_PLATFORMS, data: platforms });
          }
        })
        .catch((e) => errorHandler(dispatch, e));

      const invitedCurrentPage = PAGE;
      const invitedCriteria = {
        page: invitedCurrentPage,
        size: PAGE_SIZE,
        status: INVITED_STATUS
      };
      opportunityService
        .matchingWithProgramDetails(invitedCriteria)
        .then((res) => {
          if (res.data.total > 0) {
            const data = res.data.opportunities;
            setCurrentPageInvitedOpportunities(data);
            setInvitedOpportunities({
              ...pastOpportunities,
              [invitedCurrentPage]: data
            });
            setInvitedPages(pageCalculation(res.data.total));
            setInvitedTotal(res.data.total);
            setInvitedCurrentPage(invitedCurrentPage);
          }
        })
        .catch((e) => errorHandler(dispatch, e));
      dispatch({ type: LOADING, data: false });
    }
    getOpportunityPages();
  }, []);

  useEffect(() => {
    const criteria = {
      page: currentPage,
      size: PAGE_SIZE,
      status: "JOINED"
    };

    if (currentPage > 0) {
      if (joinedOpportunities && joinedOpportunities[currentPage] && joinedOpportunities[currentPage].length > 0) {
        setCurrentPageJoinedOpportunities(joinedOpportunities[currentPage]);
      } else {
        // Joined Opportunities
        opportunityService
          .matchingWithProgramDetails(criteria)
          .then(async (res) => {
            if (res.data.total > 0) {
              let opportunities = res.data.opportunities;
              opportunities = await updateOpportunitiesStatus(opportunities);
              const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platformDetails);
              const { participationIds } = mappingInformation;
              if (participationIds.length > 0) {
                opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
              }
              setJoinedOpportunities({
                ...joinedOpportunities,
                [currentPage]: opportunities
              });
              setCurrentPageJoinedOpportunities(opportunities);
            }
          })
          .catch((e) => errorHandler(dispatch, e));
      }
    }
  }, [currentPage]);

  useEffect(() => {
    const criteria = {
      page: pastCurrentPage,
      size: PAGE_SIZE,
      status: PAST_STATUS
    };

    if (pastCurrentPage > 0) {
      if (pastOpportunities && pastOpportunities[pastCurrentPage] && pastOpportunities[pastCurrentPage].length > 0) {
        setCurrentPagePastOpportunities(pastOpportunities[pastCurrentPage]);
      } else {
        // Past Opportunities
        opportunityService
          .matchingWithProgramDetails(criteria)
          .then(async (res) => {
            if (res.data.total > 0) {
              let opportunities = res.data.opportunities;
              opportunities = await updateOpportunitiesStatus(opportunities);
              const mappingInformation = getMetaInformationForGameCodeOpportunities(opportunities, platformDetails);
              const { participationIds } = mappingInformation;
              if (participationIds.length > 0) {
                opportunities = await mapGameCodeToOpportunity(mappingInformation, errorHandler);
              }
              setPastOpportunities({
                ...pastOpportunities,
                [pastCurrentPage]: opportunities
              });
              setCurrentPagePastOpportunities(opportunities);
            }
          })
          .catch((e) => errorHandler(dispatch, e));
      }
    }
  }, [pastCurrentPage]);

  useEffect(() => {
    const criteria = {
      page: invitedCurrentPage,
      size: PAGE_SIZE,
      status: INVITED_STATUS
    };

    if (invitedCurrentPage > 0) {
      if (
        invitedOpportunities &&
        invitedOpportunities[invitedCurrentPage] &&
        invitedOpportunities[invitedCurrentPage].length > 0
      ) {
        setCurrentPageInvitedOpportunities(invitedOpportunities[invitedCurrentPage]);
      } else {
        // Invited Opportunities
        opportunityService
          .matchingWithProgramDetails(criteria)
          .then((res) => {
            if (res.data.total > 0) {
              const data = res.data.opportunities;
              setInvitedOpportunities({
                ...invitedOpportunities,
                [invitedCurrentPage]: data
              });
              setCurrentPageInvitedOpportunities(data);
            }
          })
          .catch((e) => errorHandler(dispatch, e));
      }
    }
  }, [invitedCurrentPage]);

  useEffect(() => {
    if (user) dispatch({ type: SESSION_USER, data: user });
  }, [user]);
  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, dispatch)
        }
      );
    }
  }, [isError, isValidationError, unhandledError]);

  if (exceptionCode) {
    return <ErrorPage statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const noOpportunities = (noOpportunitiesTitle, noOpportunitiesDesc) => {
    return (
      <div className="dashboard-opportunities-no-content">
        <div className="dashboard-opportunities-no-content-row1">
          <img src="/img/dashboard/console.png" alt="icon" className="dashboard-opportunities-icon" />
          <div>
            <div className="dashboard-opportunities-no-content-title">{noOpportunitiesTitle}</div>
            <div className="dashboard-opportunities-no-content-desc">{noOpportunitiesDesc}</div>
          </div>
        </div>
        <Link href="/opportunities" className="btn btn-primary btn-md">
          {dashboardPageLabels.viewAllOpportunities}
        </Link>
      </div>
    );
  };

  const modalLabels = {
    title: dashboardPageLabels.allPerks,
    close: layout.buttons.close
  };

  const opportunityClickHandler = (opportunityId) => {
    router.push(`/opportunities/${opportunityId}`);
  };

  const onToggleFeedback = async (collapsed, contentId, status, cardType) => {
    setSelectedContentId(contentId);
    if (collapsed == false) return;
    if (status === "REJECTED" || status === "APPROVED") return;
    const currentFeedback = contentsFeedback?.[contentId]
      ? contentsFeedback?.[contentId]
      : await getFeedbacks({ contentId }, cardType);
    setContentsFeedback({ ...contentsFeedback, [contentId]: currentFeedback });
    setFeedbackLoading(false);
  };

  const getFeedbacks = async (criteria, type) => {
    setFeedbackLoading(true);
    try {
      const response = await submittedContentService.getContentsFeedback(criteria);
      return response.data.contentsFeedback.slice(0, 1).map((feedback) => ({
        ...feedback,
        ...layout.contentCard,
        sentOnLabel: layout.contentCard.sentOn,
        fromLabel: layout.contentCard.from,
        title: layout.contentCard.changesRequired,
        contentVersion: feedback.contentVersion,
        lastUpdateDate: `${feedback.formattedSubmittedDate(locale)}`,
        content: feedback.description,
        note: t("common:additionalDescription", {
          ...{
            contentType: type === "FILE" ? type.toLowerCase() : layout.contentCard.url?.toLowerCase(),
            updateType:
              type === "FILE" ? layout.buttons.upload?.toLowerCase() : layout.contentCard.update?.toLowerCase()
          }
        })
      }));
    } catch (e) {
      setFeedbackLoading(false);
      errorHandler(stableDispatch, e);
    }
  };

  const handleCreatorCode = async (opportunityId) => {
    if (creatorCodeDetails?.[opportunityId]) {
      setShowSupportACreatorModal({ [opportunityId]: true });
      return;
    }
    setCreatorCodeLoading({ [opportunityId]: true });
    try {
      const response = await opportunityService.getParticipationDetails(opportunityId);
      const opportunityCreatorCodeDetails = response.data.find((opportunity) => opportunity);
      dispatch({
        type: CREATOR_CODE_DETAILS,
        data: { [opportunityId]: opportunityCreatorCodeDetails }
      });
      setCreatorCodeLoading({ [opportunityId]: false });
      setShowSupportACreatorModal({ [opportunityId]: true });
    } catch (e) {
      setCreatorCodeLoading({ [opportunityId]: false });
      errorHandler(dispatch, e);
    }
  };

  const handleEventDetails = (opportunityId) => {
    [...currentPageJoinedOpportunities, ...currentPagePastOpportunities].find(
      (opportunity) => opportunity.id === opportunityId
    );
    setShowEventDetailsModal({ [opportunityId]: true });
  };
  const handleGameCode = async (opportunityId, participationId, hasGameCodesAssigned) => {
    if (hasGameCodesAssigned) {
      setGameCodeLoading({ [opportunityId]: true });
      await claimGameCode(participationId, stableDispatch, errorHandler, operationsService);
      setGameCodeLoading({ [opportunityId]: false });
      dispatch({
        type: ACTIVE_GAME_CODE,
        data: { ...activeGameCode, [opportunityId]: false }
      });
    }
    setShowGameCodeModal({ [opportunityId]: true });
  };

  const handleContentSubmission = (opportunitiesId, hasDeliverables) => {
    router.push(
      `/opportunities/${opportunitiesId}?tab=${hasDeliverables ? "content-deliverables" : "content-submission"}`
    );
  };

  // ToDo: handleContentSubmission will be added once opportunity details are added
  const navigationOptionHandlers = {
    handleCreatorCode,
    handleGameCode,
    handleEventDetails,
    handleContentSubmission
  };

  const activeStatusHandlers = {
    handleGameCode: (opportunityId) => activeGameCode?.[opportunityId] ?? false,
    handleContentSubmission: (opportunityId) => activeContentSubmission?.[opportunityId] ?? false
  };

  const loadingStatusHandlers = {
    handleCreatorCode: (opportunityId) => creatorCodeLoading?.[opportunityId] ?? false,
    handleGameCode: (opportunityId) => gameCodeLoading?.[opportunityId] ?? false
  };

  const formatCodeActivationWindow = (codeWindow) => {
    return [
      {
        label: opportunitiesLabels.creatorCode.codeActivationStartTime,
        value: codeWindow.startDate
      },
      {
        label: opportunitiesLabels.creatorCode.codeActivationEndTime,
        value: codeWindow.endDate
      }
    ];
  };

  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    opportunities,
    documentation,
    myContent,
    signout,
    notifications
  } = layout.header;

  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const labels = {
    commonPageLabels: {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      opportunities,
      myContent,
      documentation,
      myProfile,
      signout,
      how,
      reward,
      perks,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy,
      notifications
    },
    notificationsBellLabels: notificationsLabels
  };

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.dashboard} tabTitle={`${layout.theSims} | ${layout.header.dashboard}`}>
        <Header user={user} labels={labels} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={!!user} className="dashboard-container">
        <CreatorCodeCard username={user?.username} label={layout.heroCard.welcome} />
        {(isLoading && <Loading />) || (
          <div className="dashboard-wrapper">
            <h3 className="dashboard-sub-title">{dashboardPageLabels.myOpportunities}</h3>
            <div className="dashboard-opportunities-container">
              <div className="dashboard-opportunities-tabs-container">
                <span
                  className={classNames(
                    {
                      "dashboard-opportunities-tabs-item-selected": tabSelected === INVITED
                    },
                    "dashboard-opportunities-tabs-item"
                  )}
                  onClick={() => {
                    setTabSelected(INVITED);
                    setCurrentPage(PAGE);
                    setPastCurrentPage(PAGE);
                  }}
                  data-testid="dashboard-opportunities-invited-tab"
                >
                  {dashboardPageLabels.invited} ({invitedTotal})
                </span>
                <span
                  className={classNames(
                    {
                      "dashboard-opportunities-tabs-item-selected": tabSelected === JOINED
                    },
                    "dashboard-opportunities-tabs-item"
                  )}
                  onClick={() => {
                    setTabSelected(JOINED);
                    setPastCurrentPage(PAGE);
                    setInvitedCurrentPage(PAGE);
                  }}
                  data-testid="dashboard-opportunities-joined-tab"
                >
                  {dashboardPageLabels.joined} ({joinedTotal})
                </span>
                <span
                  className={classNames(
                    {
                      "dashboard-opportunities-tabs-item-selected": tabSelected === PAST
                    },
                    "dashboard-opportunities-tabs-item"
                  )}
                  onClick={() => {
                    setTabSelected(PAST);
                    setCurrentPage(PAGE);
                    setInvitedCurrentPage(PAGE);
                  }}
                  data-testid="dashboard-opportunities-past-tab"
                >
                  {dashboardPageLabels.past} ({pastTotal})
                </span>
              </div>

              <div className={"dashboard-opportunities-list-with-perks"}>
                {tabSelected === INVITED &&
                  (invitedTotal === 0 ? (
                    <>
                      {noOpportunities(
                        dashboardPageLabels.noInvitedOpportunities,
                        dashboardPageLabels.noInvitedOpportunitiesDesc
                      )}
                    </>
                  ) : (
                    <>
                      {(currentPageInvitedOpportunities || []).map((opportunity, index) => (
                        <Fragment key={`opportunity-card-${index}`}>
                          <OpportunityCardV2
                            title={opportunity.title}
                            description={opportunity.description}
                            opportunityId={opportunity.id}
                            heroImage={opportunity.heroImage}
                            perksTitle={opportunitiesLabels.perks}
                            perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                            settings={opportunity.settings(opportunitiesLabels)}
                            morePerksLabel={opportunitiesLabels.more}
                            pillStatus={{
                              status: INVITED_STATUS,
                              label: dashboardPageLabels.invited
                            }}
                            href={`/opportunities/${opportunity.id}`}
                            handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                            details={opportunity.settingDetails(opportunitiesLabels, router.locale, INVITED_STATUS)}
                            handleMoreButtonClick={() => setShowMorePerksForInvited({ [index]: true })}
                          />
                          {showMorePerksForInvited?.[index] && (
                            <MorePerksModal
                              labels={modalLabels}
                              onClose={() => setShowMorePerksForInvited({ [index]: false })}
                              perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                            />
                          )}
                        </Fragment>
                      ))}
                      {invitedPages && invitedPages.length > 1 && (
                        <div className="dashboard-pagination-container">
                          <Pagination
                            next={layout.buttons.next}
                            prev={layout.buttons.prev}
                            pages={invitedPages}
                            currentPage={invitedCurrentPage}
                            onPageChange={(page) => setInvitedCurrentPage(page)}
                          />
                        </div>
                      )}
                    </>
                  ))}
                {tabSelected === JOINED &&
                  (joinedTotal === 0 ? (
                    <>
                      {noOpportunities(
                        dashboardPageLabels.noJoinedOpportunities,
                        dashboardPageLabels.noJoinedOpportunitiesDesc
                      )}
                    </>
                  ) : (
                    <>
                      {(currentPageJoinedOpportunities || []).map((opportunity, index) => (
                        <Fragment key={`opportunity-card-${index}`}>
                          <OpportunityCardV2
                            title={opportunity.title}
                            opportunityId={opportunity.id}
                            heroImage={opportunity.heroImage}
                            perksTitle={opportunitiesLabels.perks}
                            perks={[]}
                            settings={opportunity.settings(opportunitiesLabels)}
                            settingsAction={opportunity.settingActions({
                              opportunitiesLabels,
                              status: JOINED_STATUS,
                              navigationOptionHandlers,
                              loadingStatusHandlers,
                              activeStatusHandlers,
                              platform: opportunity?.gameCode?.platform,
                              participationId: opportunity?.participationId,
                              hasGameCodesAssigned: opportunity?.gameCode?.status === "ASSIGNED"
                            })}
                            morePerksLabel={opportunitiesLabels.more}
                            pillStatus={{
                              status: JOINED_STATUS,
                              label: dashboardPageLabels.joined
                            }}
                            href={`/opportunities/${opportunity.id}`}
                            handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                            details={opportunity.settingDetails(opportunitiesLabels, router.locale, JOINED_STATUS)}
                          />
                          {showSupportACreatorModal?.[opportunity.id] && (
                            <SupportACreatorModal
                              labels={{
                                title: opportunitiesLabels.creatorCode.title,
                                close: modalLabels.close,
                                copied: opportunitiesLabels.copied,
                                copyText: opportunitiesLabels.creatorCode.copyText
                              }}
                              onClose={() =>
                                setShowSupportACreatorModal({
                                  [opportunity.id]: false
                                })
                              }
                              codeWindow={formatCodeActivationWindow(opportunity.activationWindow(locale))}
                              gameTitle={opportunity.gameTitle}
                              code={creatorCodeDetails?.[opportunity.id]?.creatorCode?.code}
                            />
                          )}
                          {showEventDetailsModal?.[opportunity.id] && (
                            <EventDetailsModal
                              labels={{
                                title: opportunity.isInPerson()
                                  ? opportunitiesLabels.inPersonEvent
                                  : opportunitiesLabels.remote,
                                joinEvent: opportunitiesLabels.remoteEvent.joinEvent,
                                close: modalLabels.close,
                                copied: opportunitiesLabels.copied,
                                copy: opportunity.isInPerson()
                                  ? opportunitiesLabels.eventDetails.copyAddress
                                  : opportunitiesLabels.eventDetails.copyPassword
                              }}
                              onClose={() =>
                                setShowEventDetailsModal({
                                  [opportunity.id]: false
                                })
                              }
                              eventWindow={formatEventWindow(opportunity.eventWindow(locale), opportunitiesLabels)}
                              eventAddress={
                                opportunity.isInPerson() && formatEventAddress(opportunity, opportunitiesLabels)
                              }
                              isInPerson={opportunity.isInPerson()}
                              eventPeriodEnded={
                                opportunity?.eventPeriodHasEnded()
                                  ? {
                                      label: opportunitiesLabels.eventDetails.info,
                                      value: opportunitiesLabels.remoteEvent.description
                                    }
                                  : null
                              }
                              remoteEventDetails={
                                !opportunity.isInPerson() && formatRemoteEvent(opportunity, opportunitiesLabels)
                              }
                              meetingLink={!opportunity.isInPerson() && opportunity.event.meetingLink}
                              isMeetingLinkDisabled={
                                (!opportunity.event.meetingLink && !opportunity.event.meetingPassword) ||
                                opportunity?.eventPeriodHasEnded()
                              }
                            />
                          )}
                          {showGameCodeModal?.[opportunity.id] && (
                            <GameCodeModal
                              labels={{
                                title: opportunitiesLabels.getGameCode,
                                close: modalLabels.close,
                                copied: opportunitiesLabels.copied,
                                copyText: opportunitiesLabels.getGameCode
                              }}
                              onClose={() =>
                                setShowGameCodeModal({
                                  [opportunity.id]: false
                                })
                              }
                              platform={opportunity?.gameCode?.platform}
                              gameTitle={opportunity.gameTitle}
                              code={opportunity?.gameCode?.code || opportunitiesLabels.checkBackSoon}
                              hasCode={!!opportunity?.gameCode?.code}
                            />
                          )}
                        </Fragment>
                      ))}
                      {pages && pages.length > 1 && (
                        <div className="dashboard-pagination-container">
                          <Pagination
                            next={layout.buttons.next}
                            prev={layout.buttons.prev}
                            pages={pages}
                            currentPage={currentPage}
                            onPageChange={(page) => setCurrentPage(page)}
                          />
                        </div>
                      )}
                    </>
                  ))}
                {tabSelected === PAST &&
                  (pastTotal === 0 ? (
                    <>
                      {noOpportunities(
                        dashboardPageLabels.noPastOpportunities,
                        dashboardPageLabels.noPastOpportunitiesDesc
                      )}
                    </>
                  ) : (
                    <>
                      {(currentPagePastOpportunities || []).map((opportunity, index) => (
                        <Fragment key={`opportunity-card-${index}`}>
                          {[PAST_STATUS, COMPLETED_STATUS].includes(opportunity.status) && (
                            <>
                              <OpportunityCardV2
                                title={opportunity.title}
                                description={opportunity.description}
                                opportunityId={opportunity.id}
                                heroImage={opportunity.heroImage}
                                perksTitle={opportunitiesLabels.perks}
                                perks={
                                  opportunity.status === PAST_STATUS
                                    ? opportunity.formattedPerks(opportunitiesLabels.perksLabels)
                                    : []
                                }
                                settings={opportunity.settings(opportunitiesLabels)}
                                settingsAction={
                                  opportunity.status === PAST_STATUS
                                    ? []
                                    : opportunity.settingActions({
                                        opportunitiesLabels,
                                        status: COMPLETED_STATUS,
                                        navigationOptionHandlers,
                                        platform: opportunity?.gameCode?.platform,
                                        activeStatusHandlers,
                                        participationId: opportunity?.participationId,
                                        hasGameCodesAssigned: opportunity?.gameCode?.status === "ASSIGNED"
                                      })
                                }
                                morePerksLabel={opportunitiesLabels.more}
                                pillStatus={{
                                  status: opportunity.status,
                                  label: statusLabel[opportunity.status]
                                }}
                                href={`/opportunities/${opportunity.id}`}
                                handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                                details={opportunity.settingDetails(
                                  opportunitiesLabels,
                                  router.locale,
                                  opportunity.status
                                )}
                                handleMoreButtonClick={() => setShowMorePerksForPast({ [index]: true })}
                              />
                              {showMorePerksForPast?.[index] && (
                                <MorePerksModal
                                  labels={modalLabels}
                                  onClose={() =>
                                    setShowMorePerksForPast({
                                      [index]: false
                                    })
                                  }
                                  perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                                />
                              )}
                              {showSupportACreatorModal?.[opportunity.id] && (
                                <SupportACreatorModal
                                  labels={{
                                    title: opportunitiesLabels.creatorCode.title,
                                    close: modalLabels.close,
                                    copied: opportunitiesLabels.copied,
                                    copyText: opportunitiesLabels.creatorCode.copyText
                                  }}
                                  onClose={() =>
                                    setShowSupportACreatorModal({
                                      [opportunity.id]: false
                                    })
                                  }
                                  codeWindow={formatCodeActivationWindow(opportunity.activationWindow(locale))}
                                  gameTitle={opportunity.gameTitle}
                                  code={creatorCodeDetails?.[opportunity.id]?.creatorCode?.code}
                                />
                              )}
                              {showEventDetailsModal?.[opportunity.id] && (
                                <EventDetailsModal
                                  labels={{
                                    title: opportunity.isInPerson()
                                      ? opportunitiesLabels.inPersonEvent
                                      : opportunitiesLabels.remote,
                                    close: modalLabels.close,
                                    joinEvent: opportunitiesLabels.remoteEvent.joinEvent
                                  }}
                                  onClose={() =>
                                    setShowEventDetailsModal({
                                      [opportunity.id]: false
                                    })
                                  }
                                  eventWindow={formatEventWindow(opportunity.eventWindow(locale), opportunitiesLabels)}
                                  eventPeriodEnded={{
                                    label: opportunitiesLabels.eventDetails.info,
                                    value: opportunitiesLabels.remoteEvent.description
                                  }}
                                  isMeetingLinkDisabled={true}
                                  isInPerson={opportunity.isInPerson()}
                                />
                              )}
                              {showGameCodeModal?.[opportunity.id] && (
                                <GameCodeModal
                                  labels={{
                                    title: opportunitiesLabels.getGameCode,
                                    close: modalLabels.close,
                                    copied: opportunitiesLabels.copied,
                                    copyText: opportunitiesLabels.getGameCode
                                  }}
                                  onClose={() =>
                                    setShowGameCodeModal({
                                      [opportunity.id]: false
                                    })
                                  }
                                  platform={opportunity?.gameCode?.platform}
                                  gameTitle={opportunity.gameTitle}
                                  code={opportunity?.gameCode?.code || opportunitiesLabels.checkBackSoon}
                                  hasCode={!!opportunity?.gameCode?.code}
                                />
                              )}
                            </>
                          )}
                        </Fragment>
                      ))}
                      {pastPages && pastPages.length > 1 && (
                        <div className="dashboard-pagination-container">
                          <Pagination
                            next={layout.buttons.next}
                            prev={layout.buttons.prev}
                            pages={pastPages}
                            currentPage={pastCurrentPage}
                            onPageChange={(page) => setPastCurrentPage(page)}
                          />
                        </div>
                      )}
                    </>
                  ))}
              </div>
            </div>
            <div className="dashboard-my-content-container">
              <div className="dashboard-sub-title-my-content">{dashboardPageLabels.myContent}</div>
              <div className="dashboard-tab-content-my-content">
                {!submittedContent || submittedContent?.contents.length === 0 ? (
                  <div className="dashboard-opportunities-no-content">
                    <div className="dashboard-opportunities-no-content-row1">
                      <img src="/img/dashboard/light-bulb.svg" alt="icon" className="dashboard-opportunities-icon" />
                      <div>
                        <div className="dashboard-opportunities-no-content-title">
                          {dashboardPageLabels.noSubmittedContent}
                        </div>
                        <div className="dashboard-opportunities-no-content-desc">
                          {dashboardPageLabels.noSubmittedContentDesc}{" "}
                          <Link href="/opportunities" className="dashboard-opportunities-no-content-desc-link">
                            {layout.header.opportunities}
                          </Link>
                          .
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <ContentCardsWithFeedback
                    submittedContent={submittedContent}
                    layout={layout}
                    handleToggleFeedback={onToggleFeedback}
                    opportunityClickHandler={opportunityClickHandler}
                    locale={router.locale}
                    contentsFeedback={contentsFeedback}
                    selectedContentId={selectedContentId}
                    feedbackLoading={feedbackLoading}
                  />
                )}
              </div>
              {showMyContentButton && (
                <div className="dashboard-content-button">
                  <Link href="/my-content" className="dashboard-content-button btn btn-primary btn-md">
                    {dashboardPageLabels.viewMyContent}
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </LayoutBody>
      <LayoutFooter>
        <Footer labels={labels} analytics={undefined} locale={locale} />
      </LayoutFooter>
    </Layout>
  );
}

const ContentCardsWithFeedback = memo(function ContentCardsWithFeedback({
  submittedContent,
  layout,
  handleToggleFeedback,
  opportunityClickHandler,
  locale,
  contentsFeedback,
  selectedContentId,
  feedbackLoading
}) {
  return (
    <div className="mt-meas2">
      {submittedContent?.contents.map((card) => (
        <div key={card.id}>
          <ContentCard
            content={{
              ...card,
              reviewFinalRemark: card.reviewFinalRemark
                ? {
                    ...card.reviewFinalRemark,
                    date: card.formattedReviewFinalRemarkDate(locale)
                  }
                : card.reviewFinalRemark
            }}
            labels={layout.contentCard}
            accountType={
              card.type
                ? card.type
                : card.sourceType === "USER_DEVICE" || card.sourceType === "IN_GAME_LISTING"
                ? "UPLOAD"
                : card.sourceType
            } /* type will be present only when SOURCE_TYPE is 'SOCIAL'.
          When it's a website or file content, type will be null and Content Card we'll have to rely on sourceType attribute instead of type. */
            opportunityClickHandler={() => {
              opportunityClickHandler(card.opportunityId);
            }}
            submittedDate={`${card.formattedSubmittedDate(locale)}`}
            changesRequested={card.requiresChanges()}
            handleToggleFeedback={(collapsed) =>
              handleToggleFeedback(
                collapsed,
                card.id,
                card.status,
                card.type
                  ? card.type
                  : card.sourceType === "USER_DEVICE" || card.sourceType === "IN_GAME_LISTING"
                  ? "FILE"
                  : card.sourceType
              )
            }
            feedback={{
              ...contentsFeedback?.[card.id]?.[0]
            }}
            isLoading={card.id === selectedContentId ? feedbackLoading : false}
            isMcrEnabled={true}
          />
        </div>
      ))}
    </div>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(dashboardProps(locale));

  return await router.run(req, res);
};
