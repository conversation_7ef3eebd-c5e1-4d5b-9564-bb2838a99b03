import { render, screen, waitFor } from "@testing-library/react";
import MigrationLayout from "@components/migrations/migration-layout/MigrationLayout";
import { useAppContext } from "@src/context";
import { ONBOARDING_STEPS } from "../../../../../src/utils";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";

jest.mock("../../../../../src/context");

describe("MigrationLayout", () => {
  const migrationLayoutProps = {
    onClose: jest.fn(),
    labels: { back: "Back", title: "Title", close: "Close" },
    migration: {
      information: "Information",
      connectAccounts: "Connect Accounts",
      contract: "Contract",
      paymentInfo: "Payment Info",
      preferences: "Preferences"
    },
    stableDispatch: jest.fn()
  };
  const router = {
    push: jest.fn(),
    pathname: "/onboarding/information",
    locale: "en-us"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {
        onboardingSteps: []
      }
    });
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("shows migration layout", async () => {
    render(<MigrationLayout {...migrationLayoutProps}>Child Content</MigrationLayout>);

    await waitFor(() => {
      expect(screen.getByText("Child Content")).toBeInTheDocument();
      expect(migrationLayoutProps.stableDispatch).toHaveBeenCalledWith({
        type: ONBOARDING_STEPS,
        data: [
          { icon: expect.any(Function), title: "Information", href: "/onboarding/information", isCompleted: false },
          { icon: expect.any(Function), title: "Connect Accounts", href: "/connect-accounts", isCompleted: false },
          { icon: expect.any(Function), title: "Preferences", href: "/communication-preferences", isCompleted: false },
          { icon: expect.any(Function), title: "Contract", href: "/terms-and-conditions", isCompleted: false },
          { icon: expect.any(Function), title: "Payment Info", href: "/payment-info", isCompleted: false }
        ]
      });
    });
  });

  it("shows close button is clicked", async () => {
    render(<MigrationLayout {...migrationLayoutProps} />);

    await userEvent.click(screen.getByRole("button", { name: /close/i }));

    expect(migrationLayoutProps.onClose).toHaveBeenCalled();
  });

  it("shows Loading", async () => {
    render(<MigrationLayout {...migrationLayoutProps} isLoading />);

    expect(screen.getByAltText("Loading...")).toHaveAttribute("src", "/img/Loading.gif");
    expect(screen.getByTestId("loading-icon")).toHaveClass("loading-icon");
  });
});
