import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import onError from "@src/shared/headers/AuthErrorHandler";
import { RedirectToLogoutController } from "@eait-playerexp-cn/authentication";
import ApiContainer from "@src/ApiContainer";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(RedirectToLogoutController);
  await controller.handle(req, res);
});

export default router.handler({ onError });
