import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/router";
import ProgramSideNavigation from "@components/ProgramSideNavigation";
import userEvent from "@testing-library/user-event";
import { useDetectScreen } from "@src/utils";
import { mockMatchMedia } from "../../helpers/window";

jest.mock("../../../src/utils");

describe("ProgramSideNavigation", () => {
  mockMatchMedia();
  const router = {
    push: jest.fn(),
    pathname: "/dashboard",
    locale: "en-us"
  };
  const menuLinks = [
    { id: "dashboard", buttonText: /dashboard/i, expectedPath: "/dashboard" },
    { id: "faqs", buttonText: /faqs/i, expectedPath: "/faq" },
    { id: "my_content", buttonText: /my content/i, expectedPath: "/my-content" },
    { id: "opportunities", buttonText: /opportunities/i, expectedPath: "/opportunities" },
    { id: "documentation", buttonText: /documentation/i, expectedPath: "/documentation" }
  ];
  const mockPush = jest.fn();
  const mockUseDetectScreen = useDetectScreen as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("shows side navigation buttons", () => {
    render(<ProgramSideNavigation />);

    expect(screen.getByText(/dashboard/i)).toBeInTheDocument();
    expect(screen.getByText(/faqs/i)).toBeInTheDocument();
  });

  it.each(menuLinks)("shows $id button with correct id and active state when path is $path", ({ id, expectedPath }) => {
    (useRouter as jest.Mock).mockImplementation(() => ({
      pathname: expectedPath,
      push: mockPush
    }));

    render(<ProgramSideNavigation />);

    const button = screen.getByTestId(`sidenav-icon-${id}`);
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute("data-testid", `sidenav-icon-${id}`);
  });

  it.each(menuLinks)("shows inactive state when not on $expectedPath route", ({ expectedPath }) => {
    (useRouter as jest.Mock).mockImplementation(() => ({
      pathname: expectedPath,
      push: mockPush
    }));

    render(<ProgramSideNavigation />);

    menuLinks.forEach((menuItem) => {
      const isInActive = menuItem.expectedPath !== expectedPath;
      if (isInActive) {
        const button = screen.getByText(menuItem.buttonText).closest("div");
        expect(button).not.toHaveClass("sidepanel-button-active");
      }
    });
  });

  it.each([
    ...menuLinks,
    { id: "opportunities-child", buttonText: /opportunities/i, expectedPath: "/opportunities/oppId" }
  ])("shows active state only for $expectedPath when current route matches", ({ expectedPath, buttonText }) => {
    (useRouter as jest.Mock).mockImplementation(() => ({
      pathname: expectedPath,
      push: mockPush
    }));

    render(<ProgramSideNavigation />);

    menuLinks.forEach((menuItem) => {
      const button = screen.getByText(buttonText).closest("div");
      let isActive;
      if (menuItem.id === "opportunities") {
        isActive = expectedPath.startsWith("/opportunities");
      } else {
        isActive = menuItem.expectedPath === expectedPath;
      }
      if (isActive) {
        expect(button).toHaveClass("sidepanel-button-active");
        expect(button).toHaveClass("sidepanel-button-container");
      } else {
        expect(button).toHaveClass("sidepanel-button-container");
      }
    });
  });

  it.each(menuLinks)("navigates to $id when clicked", async ({ buttonText, expectedPath }) => {
    render(<ProgramSideNavigation />);

    await userEvent.click(screen.getByText(buttonText));

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith(expectedPath);
    });
  });

  it("verifies all navigation items have unique ids", () => {
    const ids = menuLinks.map((item) => item.id);
    const uniqueIds = new Set(ids);
    expect(ids.length).toBe(uniqueIds.size);
  });

  it("only shows side navigation on desktop", () => {
    mockUseDetectScreen.mockImplementation((width) => width === 1024);

    render(<ProgramSideNavigation />);

    expect(screen.queryByText(/dashboard/i)).not.toBeInTheDocument();
  });
});
