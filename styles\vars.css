:root {
  --migration-background: url("../public/img/migrations/migration-background.svg");
  --migration-background-tablet: url("../public/img/migrations/migration-background-tablet.svg");
  --down-arrow-icon-background: url("../public/img/icons/down-arrow.png");
  --min-height-tab: calc(100vh - 336px);
  --min-height-mobile: calc(100vh - 321px);
  --tooltip-background: url("../public/img/icons/alert-icon.png");
  --migration-background: url("../public/img/migrations/migration-background.svg");
  --error-desktop-background: url("../public/img/error-desktop.png");
  --error-mobile-background: url("../public/img/error-mobile.png");
  --error-desktop-background-image: url("../public/img/error-desktop.png");
  --login-error-desktop-background-image: url("../public/img/login-error-desktop.png");
  --error-tablet-background: url("../public/img/error-tablet.png");
  --error-desktop-background-image: url("../public/img/login-error-desktop.png");
  --hero-background-image: url("../public/img/dashboard/hero-background.svg");
  --error-desktop-character-background: url("../public/img/error-desktop-character.png");
  --error-mobile-character-background: url("../public/img/error-mobile-character.png");
  --error-tablet-character-background: url("../public/img/error-tablet-character.png");
  --error-page-background: url("../public/img/error-bg.png");
}
