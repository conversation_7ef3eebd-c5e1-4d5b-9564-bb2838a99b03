import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import OpportunitiesHttpClient from "@src/server/opportunities/OpportunitiesHttpClient";
import ViewParticipationsWithProgramCodeController from "@src/server/opportunities/ViewParticipationsWithProgramCodeController";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import config from "config";
import Random from "__tests__/factories/Random";

describe("ViewParticipationsWithProgramCodeController", () => {
  let controller: ViewParticipationsWithProgramCodeController;

  beforeEach(() => jest.clearAllMocks());

  it("finds a creator participations filtered by status", async () => {
    config.PROGRAM_CODE = "sims_creator_program";
    const criteria = { page: 1, size: 10, status: "JOINED", programCode: "sims_creator_program" };
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/v4/dashboard",
      query: criteria,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const opportunitiesWithDetails = {
      count: 0,
      opportunities: [],
      total: 0
    };
    const opportunities = {
      matchingWithProgramDetails: jest.fn().mockResolvedValue(opportunitiesWithDetails)
    };
    controller = new ViewParticipationsWithProgramCodeController((opportunities as unknown) as OpportunitiesHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(opportunitiesWithDetails);
    expect(opportunities.matchingWithProgramDetails).toHaveBeenCalledTimes(1);
    expect(opportunities.matchingWithProgramDetails).toHaveBeenCalledWith(userId, criteria);
  });
});
