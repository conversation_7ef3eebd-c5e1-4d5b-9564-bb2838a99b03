import { screen } from "@testing-library/react";
import { renderPage } from "__tests__/helpers/page";
import ContentGuidelines, { ContentGuidelinesProps } from "@components/joinOpportunitySteps/ContentGuidelines";
import { useDependency } from "@src/context/DependencyContext";
import { OpportunityWithDeliverables } from "@src/services/OpportunityService";
import { useRouter } from "next/router";

jest.mock("@src/context/DependencyContext", () => ({
  useDependency: jest.fn()
}));

beforeEach(() => {
  jest.clearAllMocks();
  (useDependency as jest.Mock).mockReturnValue({ errorHandler: jest.fn() });
  (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en_us" }));
});

describe("Content Guidelines", () => {
  let contentGuideLinesProps = ({
    layout: {
      main: {},
      buttons: []
    },
    opportunitiesLabels: {
      contentGuidelines: "Content Submission Requirements",
      downloadEaLogo: "Download Electronic Arts Watermark"
    }
  } as unknown) as ContentGuidelinesProps;
  it("renders guidelines HTML when opportunity.contentSubmission.guidelines is present", () => {
    const guidelinesHtml = "<strong>Follow these rules</strong>";
    contentGuideLinesProps = {
      ...contentGuideLinesProps,
      opportunity: {
        ...contentGuideLinesProps.opportunity,
        contentSubmission: { guidelines: guidelinesHtml },
        hasAttachments: false
      } as OpportunityWithDeliverables
    };
    renderPage(<ContentGuidelines {...contentGuideLinesProps} />);
    expect(screen.getByText(contentGuideLinesProps.opportunitiesLabels.contentGuidelines)).toBeInTheDocument();
    expect(screen.getByText("Follow these rules")).toBeInTheDocument();
    expect(screen.getByRole("heading", { level: 5 })).toHaveClass("content-guidelines-title");
  });
});
