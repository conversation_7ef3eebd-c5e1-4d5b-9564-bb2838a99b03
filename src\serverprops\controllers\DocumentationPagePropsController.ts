import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { DocumentationPageProps } from "@src/pages/documentation";
import IndexesHttpClient from "@src/server/contentModal/IndexesHttpClient";
import ContentManagementService from "@src/services/ContentManagementService";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export default class DocumentationPagePropsController
  extends AuthenticatedRequestH<PERSON>ler
  implements ServerPropsController<DocumentationPageProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly document: IndexesHttpClient,
    private readonly contentManagement: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<DocumentationPageProps>> {
    try {
      const authenticatedUser = this.hasIdentity(req)
        ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.program, this.defaultAvatar)
        : null;
      const { data: indexPage } = await this.document.matching("documentation", {
        locale: this.currentLocale
      });
      const pageLabels = await this.contentManagement.getPageLabels(this.currentLocale, "documentation");

      return {
        props: {
          runtimeConfiguration: runtimeConfiguration(authenticatedUser),
          user: authenticatedUser,
          indexPage,
          locale: this.currentLocale,
          pageLabels,
          ...(await serverSideTranslations(this.currentLocale, [
            "common",
            "dashboard",
            "my-content",
            "notifications",
            "connect-accounts",
            "opportunities",
            "faq",
            "documentation"
          ]))
        }
      };
    } catch (e) {
      if (e instanceof ArticleNotFound) return { notFound: true };
      throw e;
    }
  }
}
