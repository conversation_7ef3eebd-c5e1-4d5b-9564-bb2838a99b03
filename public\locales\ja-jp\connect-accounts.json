{"title": "ソーシャルメディアアカウントに接続", "message": "Connect any social media accounts that you want to submit content from.", "subTitle": "アカウントの追加", "myAccount": "マイアカウント", "connectAccountsPageTitle": "Connect Accounts", "addAccount": " アカウントの追加", "description": "Please connect at least one of your social media accounts with your content. You may also  connect multiple accounts on each social media. This will help our team assess our compatibility.", "accounts": {"youTube": "YouTube", "facebook": "Facebook", "twitch": "Twitch", "instagram": "Instagram", "tiktok": "TikTok"}, "messages": {"removeAccountTitle": "アカウントを削除できませんでした", "youtubeNoChannelError": "チャンネルが関連付けられていないため、YouTubeアカウントを追加できません。", "removeAccountDescription": "We're unable to remove this account. You need to link at least one account to your profile", "cannotConnectInstaAccount": "Only one Instagram account can be connected to your profile at a time", "cannotConnectInstaAccountHeader": "Instagramアカウントに接続できません", "actionTitle": "アクションが必要です！", "actionDescription1": "Instagramアカウントを接続するには、まずFacebookプロフィールにリンクする必要があります。", "actionDescription2": "これを行うには、", "actionDescription3": "Instagramのヘルプセンターから", "actionDescription4": "from Instagram’s Help Center, you’ll then be able to connect your Instagram account."}, "modalConfirmationTitleFB": "Facebookのページを選択してください", "confirmationDesc1": "このアカウントを削除してもよろしいですか？", "confirmationDesc2": "You can begin registration again at any point by visiting The Sims Portal.", "removeAccountTitle": "チャンネルを削除しますか？", "removeAccountDescription1": "このアカウントを削除してもよろしいですか？", "removeAccountDescription2": "You must have a social media account connected in order to submit content.", "modalTitle": "アカウントを接続する", "modalMessage": "これはプレースホルダーです。ログインがここに表示され、選択したソーシャルメディアアカウントに接続して認証できます。", "subscribers": "加入者", "removeAccount": "チャンネルを削除", "cancel": "キャンセル", "remove": "削除", "expireAccount": "アカウント接続済み", "or": "または", "reconnectAccount": "ここをクリックして、アカウントをもう一度接続する", "connectNewAccount": "新しいアカウントをリンクする", "connectNewAccountDescription": "Facebook、Instagram、YouTube、Twitch", "connectNewAccountDescriptionWithTikTok": "Facebook、Instagram、YouTube、Twitch、TikTok", "comma": "、", "verificationPending": "Verification Pending", "reVerifyAccount": "Click to re-verify this account"}