import {
  documentation,
  dollarPayment,
  faqs,
  grid,
  logout,
  MenuItems,
  myContent,
  opportunities,
  TopNavigation,
  useDetectScreen,
  user as userIcon
} from "@eait-playerexp-cn/core-ui-kit";
import React, { ComponentType, memo } from "react";
import { useRouter } from "next/router";
import MenuDropdown from "./header/MenuDropdown";
import dynamic from "next/dynamic";
import Loading from "./loading/Loading";
import Link from "next/link";
import BrowserAnalytics, { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";

const UnauthenticatedLinks = ({ labels: { signIn } }: { labels: { signIn: string } }) => {
  return (
    <div className="topnav-signup-button-container">
      <Link
        aria-label="Log in"
        title="Log in"
        href="/api/login"
        className="topnav-signup-page-apply-button btn btn-primary btn-sm"
      >
        {signIn}
      </Link>
    </div>
  );
};

const NotificationsBell: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("notifications/NotificationsBell"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);
export type NotificationsLabels = {
  viewAllNotification: string;
  noNewNotifications: string;
  viewNotifications: string;
  cardLabels: {
    labels: {
      OPPORTUNITY_INVITATION: {
        title: string;
        description: string;
      };
      CHANNEL_EXPIRY: {
        title: string;
        description: string;
      };
      CONTENT_SUBMISSION_START: {
        title: string;
        description: string;
      };
      CONTENT_SUBMISSION_END: {
        title: string;
        description: string;
      };
      CONTENT_REJECTED: {
        title: string;
        description: string;
      };
      CONTENT_APPROVED: {
        title: string;
        description: string;
      };
      CREATOR_PAYMENT: {
        title: string;
        description: string;
      };
      CONTENT_FEEDBACK: {
        title: string;
        description: string;
      };
      PROFILE_INCOMPLETE: {
        title: string;
        description: string;
      };
      PROFILE_COMPLETE: {
        title: string;
        description: string;
      };
    };
    accounts: {
      FACEBOOK: string;
      TWITCH: string;
      YOUTUBE: string;
      INSTAGRAM: string;
      TIKTOK: string;
    };
    supportACreator: string;
  };
};

export type TopNavigationPageLabels = {
  signIn: string;
  signout: string;
  myProfile: string;
  logIn: string;
  home: string;
  faqs: string;
  dashboard: string;
  notifications: string;
  opportunities: string;
  myContent: string;
  documentation: string;
};

type ProgramTopNavigationProps = {
  analytics: BrowserAnalytics;
  user?: AuthenticatedUser;
  pageLabels?: { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };
};

export default memo(function ProgramTopNavigation({
  analytics,
  user: authenticatedUser,
  pageLabels
}: ProgramTopNavigationProps) {
  const router = useRouter();
  const isMobile = useDetectScreen(767);
  const {
    notificationsClient,
    configuration: {
      NOTIFICATION_BASE_URLS,
      SINGLE_PROGRAM_NOTIFICATIONS,
      PROGRAM_CODE,
      DEFAULT_NOTIFICATION_PROGRAM,
      MENU_ITEMS
    }
  } = useDependency();
  const isMobileOrTab = useDetectScreen(1209);
  const defaultMenuItem: MenuItems[] = [
    {
      label: MENU_ITEMS[PROGRAM_CODE].label,
      value: PROGRAM_CODE,
      gradients: MENU_ITEMS[PROGRAM_CODE].gradients,
      image: "/img/sims-logo-dark.svg"
    }
  ];
  // const menuItems =
  //   config.FLAG_CREATORS_API_WITH_PROGRAM && config.user?.programs
  //     ? config.user.programs.map((program) => ({
  //         label: config.MENU_ITEMS[program].label,
  //         value: program,
  //         gradients: config.MENU_ITEMS[program].gradients
  //       }))
  //     : defaultMenuItem;

  // TODO: uncomment above remove when FLAG_CREATORS_API_WITH_PROGRAM is integrated
  const menuItems = defaultMenuItem;

  const { commonPageLabels, notificationsBellLabels } = pageLabels;

  const header = {
    signout: commonPageLabels.signout,
    myProfile: commonPageLabels.myProfile
  };

  const onMenuSelect = () => {};

  const onNotificationBellClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    router.push("/notifications");
  };

  const NotificationBellContainer = () => {
    return isMobile ? (
      <div className="notificaion-bell-parent-container">
        <NotificationsBell
          labels={{ ...notificationsBellLabels, notifications: commonPageLabels.notifications }}
          locale={router.locale}
          configuration={{
            client: notificationsClient,
            programHosts: NOTIFICATION_BASE_URLS,
            program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
            defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
          }}
          navigateToNotificationsPage={onNotificationBellClick}
        />
      </div>
    ) : (
      <NotificationsBell
        labels={{ ...notificationsBellLabels, notifications: commonPageLabels.notifications }}
        locale={router.locale}
        configuration={{
          client: notificationsClient,
          programHosts: NOTIFICATION_BASE_URLS,
          program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
          defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
        }}
        navigateToNotificationsPage={onNotificationBellClick}
      />
    );
  };

  const AvatarContainer = () => {
    return !isMobileOrTab ? (
      <div className="topnav-profile-avatar-container">
        <MenuDropdown labels={header} analytics={analytics}>
          <img
            alt=""
            data-testid="profile-image"
            src={authenticatedUser.avatar}
            className="topnav-navigation-button-image"
          />
        </MenuDropdown>
      </div>
    ) : null;
  };

  const additionalLabels = {
    signIn: commonPageLabels.signIn
  };

  const additionalLinkContainer = !isMobileOrTab
    ? [
        {
          children: <UnauthenticatedLinks labels={additionalLabels} />,
          active: false
        }
      ]
    : [];

  const topNavButtons = authenticatedUser
    ? [
        {
          button: {
            title: "Payment",
            onClick: () => {
              router.push("/payment-information");
            },
            asset: dollarPayment
          },
          active: router.pathname === "/payment-information"
        },
        {
          children: <NotificationBellContainer />,
          active: router.pathname === "/notifications"
        },
        {
          children: <AvatarContainer />,
          active: router.pathname === "/profile"
        }
      ]
    : additionalLinkContainer;

  const affiliateButtons = !authenticatedUser
    ? [
        {
          id: "sign-in",
          label: commonPageLabels.signIn,
          icon: logout,
          onClick: () => router.push("/api/login"),
          active: false
        }
      ]
    : [
        {
          id: "dashboard",
          label: commonPageLabels.dashboard,
          icon: grid,
          onClick: () => router.push("/dashboard"),
          active: router.pathname === "/dashboard"
        },
        {
          id: "opportunities",
          label: commonPageLabels.opportunities,
          icon: opportunities,
          onClick: () => router.push("/opportunities"),
          active: router.pathname === "/opportunities"
        },
        {
          id: "my_content",
          label: commonPageLabels.myContent,
          icon: myContent,
          onClick: () => router.push("/my-content"),
          active: router.pathname === "/my-content"
        },
        {
          id: "documentation",
          label: commonPageLabels.documentation,
          icon: documentation,
          onClick: () => router.push("/documentation"),
          active: router.pathname === "/documentation"
        },
        {
          id: "faqs",
          label: commonPageLabels.faqs,
          icon: faqs,
          onClick: () => router.push("/faq"),
          active: router.pathname === "/faq"
        },
        {
          id: "myProfile",
          label: header.myProfile,
          icon: userIcon,
          onClick: () => router.push("/profile"),
          active: router.pathname === "/profile"
        },
        {
          id: "logout",
          label: header.signout,
          icon: logout,
          onClick: () => router.push("/api/logout"),
          active: false
        }
      ];

  return (
    <div className="header-topnav-parent-container">
      <TopNavigation
        dropdownMenu={{
          dropdownLabel: menuItems.find((item) => item.value === PROGRAM_CODE)?.label,
          onMenuItemSelect: onMenuSelect,
          menuItems
        }}
        rightNavigationButtons={topNavButtons}
        sideNavButtons={affiliateButtons}
        leftNavigationButtons={[]}
      />
    </div>
  );
});
