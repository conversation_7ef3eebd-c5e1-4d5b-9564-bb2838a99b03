export const facebookGuideTranslations = {
  title: "Submitting content from Facebook",
  subTitle: "When submitting content from Facebook please follow the guidelines below.",
  contentImages: [
    { desc: "Creator Network only accepts full length URLs from Facebook:", imageSrc: [] },
    { desc: "Short URLs, like the example below, are not supported by the Creator Network:", imageSrc: [] }
  ],
  pointsToNote: [
    "Videos should not be private.",
    "Videos should belong to the channel connected to the Creator Network.",
    "If your username has changed since your account was last connected to Creator Network, please go to My Profile and reconnect the account.",
    "Live stream URLs where the stream is not finished are not supported.",
    "Live streams which are finished and converted to videos are supported."
  ],
  footerLabel: "Got it!",
  pointsToNoteTitle: "Other important points to note:",
  onClose: jest.fn(),
  closeLabel: "Close"
};

export const youtubeGuideTranslations = {
  title: "Submitting content from YouTube",
  subTitle: "When submitting content from YouTube please follow the guidelines below.",
  contentImages: [
    { desc: "Creator Network only accepts full length URLs from YouTube (video and shorts):", imageSrc: [] },
    {
      desc: "Short form URLs, like the example below, are not supported by the Creator Network:",
      imageSrc: []
    }
  ],
  pointsToNote: [
    "Videos should not be private.",
    "Videos should belong to the channel connected to the Creator Network.",
    "If your username has changed since your account was last connected to Creator Network, please go to My Profile and reconnect the account."
  ],
  footerLabel: "Got it!",
  pointsToNoteTitle: "Other important points to note:",
  onClose: jest.fn(),
  closeLabel: "Close"
};

export const instagramGuideTranslations = {
  title: "Submitting content from Instagram",
  subTitle: "When submitting content from Instagram please follow the guidelines below.",
  contentImages: [{ desc: "The following URLs formats are supported:", imageSrc: [] }],
  pointsToNote: [
    "Videos should belong to the channel connected to the Creator Network.",
    "If your username has changed since your account was last connected to Creator Network, please go to My Profile and reconnect the account.",
    "Creator Network does not support content submitted from IG Live.",
    "The connected Instagram account must be in professional mode and linked to a Facebook page.",
    "Content older than 30 days can not be submitted to Creator Network.",
    "Instagram photos are not supported."
  ],
  footerLabel: "Got it!",
  pointsToNoteTitle: "Other important points to note:",
  onClose: jest.fn(),
  closeLabel: "Close"
};

export const twitchGuideTranslations = {
  title: "Submitting content from Twitch",
  subTitle: "When submitting content from Twitch please follow the guidelines below.",
  contentImages: [
    { desc: "Creator Network accepts URLS to live streams that have been converted to VODs*:", imageSrc: [] },
    { desc: "URLs from Clips, like the example below, are not supported by the Creator Network:", imageSrc: [] }
  ],
  pointsToNote: [
    "Videos should not be private.",
    "Videos should belong to the channel connected to the Creator Network.",
    "If your username has changed since your account was last connected to Creator Network, please go to My Profile and reconnect the account."
  ],
  footNote: "*VOD stands for Video on demand.",
  footerLabel: "Got it!",
  pointsToNoteTitle: "Other important points to note:",
  onClose: jest.fn(),
  closeLabel: "Close"
};

export const tiktokGuideTranslations = {
  title: "Submitting content from TikTok",
  subTitle: "When submitting content from TikTok please follow the guidelines below.",
  contentImages: [
    { desc: "Creator Network accepts URLS to live streams that have been converted to VODs*:", imageSrc: [] }
  ],
  pointsToNote: [
    "Videos should not be private.",
    "Videos should belong to the channel connected to the Creator Network."
  ],
  footNote: "*VOD stands for Video on demand.",
  footerLabel: "Got it!",
  pointsToNoteTitle: "Other important points to note:",
  onClose: jest.fn(),
  closeLabel: "Close"
};
