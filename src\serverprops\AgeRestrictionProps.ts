import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import AgeRestrictionPagePropsController from "./controllers/AgeRestrictionPagePropsController";

const ageRestrictionProps = (locale: string) =>
  serverPropsControllerFactory(
    new AgeRestrictionPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale
    )
  );

export default ageRestrictionProps;
