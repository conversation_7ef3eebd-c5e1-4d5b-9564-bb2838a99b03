export function labelsPaymentInformation(t) {
  return {
    paymentSettings: t("payment-information:paymentSettings"),
    invoicesHistory: t("payment-information:invoicesHistory"),
    paymentsHistory: t("payment-information:paymentsHistory"),
    paymentSettingsDescription: t("payment-information:paymentSettingsDescription"),
    paymentInvoicesDescription: t("payment-information:paymentInvoicesDescription"),
    paymentHistoryDescription: t("payment-information:paymentHistoryDescription"),
    pageTitle: t("payment-information:pageTitle"),
    payableStatus: t("payment-information:payableStatus"),
    nonPayableStatus: t("payment-information:nonPayableStatus"),
    nonPayableStatusHelp: t("payment-information:nonPayableStatusHelp"),
    nonPayableStatusClickHere: t("payment-information:nonPayableStatusClickHere"),
    nonPayableStatusStart: t("payment-information:nonPayableStatusStart"),
    yourPayments: t("payment-information:yourPayments")
  };
}

export function labelsPaymentBanner(t) {
  return {
    paymentInformationBannerWarning: t("payment-information:paymentInformationBannerWarning"),
    paymentInformationBannerDescription: t("payment-information:paymentInformationBannerDescription"),
    paymentInformationBannerDescription_2: t("payment-information:paymentInformationBannerDescription_2"),
    paymentInformationClick: t("payment-information:paymentInformationClick"),
    paymentInformationBannerDescription_3: t("payment-information:paymentInformationBannerDescription_3")
  };
}

export function labelsPaymentDetails(t) {
  return {
    paymentOverview: t("payment-information:paymentOverview"),
    paymentTotalPaid: t("payment-information:paymentTotalPaid"),
    paymentPendingPayments: t("payment-information:paymentPendingPayments"),
    transactionHistory: t("payment-information:transactionHistory"),
    transactionHistoryDetails: t("payment-information:transactionHistoryDetails"),
    paymentInformationClick: t("payment-information:paymentInformationClick"),
    filteredBy: t("payment-information:filteredBy")
  };
}

export function labelsPaymentHistoryGrid(t) {
  return {
    paymentGridDescription: t("payment-information:paymentGridDescription"),
    paymentGridType: t("payment-information:paymentGridType"),
    paymentGridStatus: t("payment-information:paymentGridStatus"),
    paymentGridAmountDue: t("payment-information:paymentGridAmountDue"),
    paymentGridDate: t("payment-information:paymentGridDate"),
    paymentGridDateHelp: t("payment-information:paymentGridDateHelp"),
    paymentGridContract: t("payment-information:paymentGridContract"),
    statusPending: t("payment-information:statusPending"),
    statusProcessed: t("payment-information:statusProcessed"),
    downloadContract: t("payment-information:downloadContract"),
    typeCreatorCode: t("payment-information:typeCreatorCode"),
    typeOpportunity: t("payment-information:typeOpportunity"),
    noPayments: t("payment-information:noPayments"),
    noPaymentsDescription: t("payment-information:noPaymentsDescription"),
    noProcessedPayments: t("payment-information:noProcessedPayments"),
    noProcessedPaymentsDescription: t("payment-information:noProcessedPaymentsDescription"),
    noPaymentsLink: t("payment-information:noPaymentsLink"),
    downloadContractLabel: t("payment-information:downloadContractLabel"),
    opportunityImageLabel: t("payment-information:noPaymentsLink:opportunityImageLabel"),
    filteredBy: t("payment-information:filteredBy"),
    noPendingPayments: t("payment-information:noPendingPayments"),
    noPendingPaymentsDescription: t("payment-information:noPendingPaymentsDescription"),
    descriptionLabel: t("payment-information:descriptionLabel"),
    paymentPeriodLabel: t("payment-information:paymentPeriodLabel")
  };
}
