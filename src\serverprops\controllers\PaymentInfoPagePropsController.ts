import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { PaymentInfoProps } from "@src/pages/payment-info";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { PaymentInformationPageLabels } from "@src/server/contentManagement/PaymentInformationPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class PaymentInfoPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<PaymentInfoProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<PaymentInfoProps>> {
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "paymentInformation"
    )) as PaymentInformationPageLabels & BreadcrumbPageLabels & CommonPageLabels;
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels,
        user: authenticatedUser
      }
    };
  }
}
