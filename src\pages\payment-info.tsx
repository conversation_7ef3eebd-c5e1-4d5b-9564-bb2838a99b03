import "reflect-metadata";
import React, { useCallback, useEffect, useState } from "react";
import MigrationLayout from "@components/migrations/migration-layout/MigrationLayout";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { useAppContext } from "@src/context";
import { useRouter } from "next/router";
import PaymentIFrameTab from "@components/payment-information/PaymentIFrameTab";
import { usePayableStatus } from "@components/pages/payment-information/usePayableStatus";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import Error from "./_error";
import { PaymentInformationPageLabels } from "@src/server/contentManagement/PaymentInformationPageMapper";
import { USER_NAVIGATED } from "@src/utils";
import { CancelRegistrationModal } from "@eait-playerexp-cn/onboarding-ui";
import { useDependency } from "@src/context/DependencyContext";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import verifyIncompleteRegistration from "@src/serverprops/middleware/VerifyIncompleteRegistration";
import { GetServerSidePropsResult } from "next";
import paymentInfoProps from "@src/serverprops/PaymentInfoProps";

export type PageLabels = {
  pageLabels: PaymentInformationPageLabels & BreadcrumbPageLabels & CommonPageLabels;
};

export type PaymentInfoProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user?: AuthenticatedUserFactory;
} & PageLabels;

export default function PaymentInfo({ pageLabels }: Readonly<PageLabels>) {
  const { commonPageLabels } = pageLabels;
  const {
    dispatch,
    state: { exceptionCode = null, sessionUser = null, isLoading = false, userNavigated } = {}
  } = useAppContext();
  const { client, errorHandler } = useDependency();

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showMigration, setShowMigration] = useState(false);

  const stableDispatch = useCallback(dispatch, [dispatch]);
  const router = useRouter();
  const { paymentsIframe = false, creator } = usePayableStatus(
    "PAYEE_ONBOARDING",
    router.locale,
    stableDispatch,
    null,
    null
  );
  const [navigateToPage, setNavigateToPage] = useState("");

  const handleModalClose = useCallback(() => setShowConfirmation(false), []);

  const handleCancelRegistration = useCallback(() => {
    router.push("/api/logout");
  }, [router]);

  /**
   * User can navigate with back or using stepper links
   * - disable back navigation
   * - else handle the back navigation
   */
  useEffect(() => {
    if (userNavigated) {
      dispatch && dispatch({ type: USER_NAVIGATED, data: false });
    }
  }, [router, userNavigated, navigateToPage, dispatch]);

  useEffect(() => {
    if (!router.isReady) return;

    // Block browser back button
    const handlePopState = () => {
      history.pushState(null, "", window.location.href);
    };

    history.pushState(null, "", window.location.href);
    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  const onClose = useCallback(() => {
    setShowConfirmation(!showConfirmation);
  }, [showConfirmation]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };

  const cancelRegistrationModalLabels = {
    title: commonPageLabels.modalConfirmationTitle,
    yes: commonPageLabels.yes,
    no: commonPageLabels.no,
    close: commonPageLabels.close,
    confirmationDesc1: commonPageLabels.confirmationDesc1,
    confirmationDesc2: commonPageLabels.confirmationDesc2
  };

  return (
    <MigrationLayout
      pageTitle={commonPageLabels.paymentInformation}
      tabTitle={`${commonPageLabels.theSims} | ${commonPageLabels.paymentInfo}`}
      theSims={commonPageLabels.theSims}
      className="onboarding-creator"
      onClose={onClose}
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        information: commonPageLabels.information,
        contract: commonPageLabels.contract,
        paymentInfo: commonPageLabels.paymentInfo,
        connectAccounts: commonPageLabels.connectedAccounts,
        preferences: commonPageLabels.preferences
      }}
      stableDispatch={stableDispatch}
      setShowMigration={setShowMigration}
      isRegistrationFlow={true}
      onGoBack={onGoBack}
      isOnboardingFlow={true}
      isLoading={isLoading}
      setNavigateToPage={setNavigateToPage}
      completed={commonPageLabels.completed}
    >
      <PaymentIFrameTab
        {...{
          ...paymentsIframe,
          pageLabels,
          isLoading,
          client,
          creator,
          stableDispatch,
          errorHandler,
          showMigration,
          onClose
        }}
      />
      {showConfirmation && (
        <CancelRegistrationModal
          {...{
            labels: cancelRegistrationModalLabels,
            handleModalClose,
            handleCancelRegistration
          }}
        />
      )}
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(verifyIncompleteRegistration)
    .use(addLocaleCookie(locale))
    .get(paymentInfoProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<PaymentInfoProps>;
};
