import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewOpportunityDetailsWithPaymentController from "@src/server/opportunities/ViewOpportunityDetailsWithPaymentController";
import OpportunityWithPaymentDetails from "@src/server/opportunities/OpportunityWithPaymentDetails";
import { anOpportunityWithPaymentDetails } from "__tests__/factories/opportunities/OpportunityWithPerks";
import Random from "__tests__/factories/Random";
import OpportunitiesHttpClient from "@src/server/opportunities/OpportunitiesHttpClient";
import CreatorsWithFlaggedStatusHttpClient from "@src/server/creators/CreatorsWithFlaggedStatusHttpClient";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import { aCreatorResponse } from "@eait-playerexp-cn/creator-test-fixtures";

describe("ViewOpportunityDetailsWithPaymentController", () => {
  let controller: ViewOpportunityDetailsWithPaymentController;

  beforeEach(() => jest.clearAllMocks());

  it("get opportunity with payment details", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/v10/opportunities",
      query: { id: "a0MK000000BsCweMAF" },
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const opportunityWithPayment: OpportunityWithPaymentDetails = OpportunityWithPaymentDetails.fromApi(
      anOpportunityWithPaymentDetails({
        payment: { percentage: Random.number(), currencyName: Random.string(), amount: Random.string() }
      })
    );
    const opportunities = { withOpportunityPayment: jest.fn().mockResolvedValue(opportunityWithPayment) };
    const creatorData = aCreatorResponse();
    const creators = { withIdForProgramProfile: jest.fn().mockResolvedValue(creatorData) };
    controller = new ViewOpportunityDetailsWithPaymentController(
      (opportunities as unknown) as OpportunitiesHttpClient,
      (creators as unknown) as CreatorsWithFlaggedStatusHttpClient
    );

    await controller.handle(req, res);

    const { opportunity, creator } = JSON.parse(res._getData());
    expect(res._getStatusCode()).toBe(200);
    expect(opportunity).toEqual(opportunityWithPayment);
    expect(creator).toEqual(creatorData);
    expect(opportunities.withOpportunityPayment).toHaveBeenCalledTimes(1);
    expect(opportunities.withOpportunityPayment).toHaveBeenCalledWith("a0MK000000BsCweMAF", userId);
    expect(creators.withIdForProgramProfile).toHaveBeenCalledTimes(1);
  });
});
