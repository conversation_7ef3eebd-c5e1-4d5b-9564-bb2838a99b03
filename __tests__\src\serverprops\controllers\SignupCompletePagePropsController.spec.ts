import SignupCompletePagePropsController from "@src/serverprops/controllers/SignupCompletePagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");

describe("SignupCompletePagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {
      title: "Signup Complete",
      description: "Your signup is complete",
      returnToDashboard: "Return to Dashboard"
    };
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new SignupCompletePagePropsController(options, contents, currentLocale);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "signupComplete");
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels: labels
      }
    });
  });
});
