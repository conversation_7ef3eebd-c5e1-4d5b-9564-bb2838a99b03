export default function labelsPaymentsFilter(t) {
  return {
    filters: t("payments-filter:filters"),
    dateRange: t("payments-filter:dateRange"),
    startDate: t("payments-filter:startDate"),
    endDate: t("payments-filter:endDate"),
    paymentStatus: t("payments-filter:paymentStatus"),
    opportunityType: t("payments-filter:opportunityType"),
    applyFilters: t("payments-filter:applyFilters"),
    startDateRequired: t("payments-filter:startDateRequired"),
    endDateRequired: t("payments-filter:endDateRequired"),
    startDateError: t("payments-filter:startDateError"),
    endDateError: t("payments-filter:endDateError"),
    programCode: t("payments-filter:programCode"),
    range: {
      allTime: t("payments-filter:range.allTime"),
      thisMonth: t("payments-filter:range.thisMonth"),
      past30Days: t("payments-filter:range.past30Days"),
      past90Days: t("payments-filter:range.past90Days"),
      past6Months: t("payments-filter:range.past6Months"),
      yearToDate: t("payments-filter:range.yearToDate"),
      lastYear: t("payments-filter:range.lastYear"),
      custom: t("payments-filter:range.custom")
    },
    status: {
      all: t("payments-filter:status.all"),
      paid: t("payments-filter:status.paid"),
      pending: t("payments-filter:status.pending")
    },
    program: {
      all: t("payments-filter:type.all"),
      creatorNetwork: t("payments-filter:type.creatorNetwork"),
      affiliate: t("payments-filter:type.affiliate")
    }
  };
}
