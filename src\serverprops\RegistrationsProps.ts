import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import RegistrationsPagePropsController from "./controllers/RegistrationsPagePropsController";
import config from "config";

export type RegistrationsPageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  WATERMARKS_URL: string;
};

const registrationsProps = (locale: string) =>
  serverPropsControllerFactory(
    new RegistrationsPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default registrationsProps;
