import "reflect-metadata";
import { NextApiRequestWithSession, sessionFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import { NextApiResponse } from "next";
import { NextHandler } from "next-connect";

const initializeSession = async (
  req: NextApiRequestWithSession,
  res: NextApiResponse,
  next: NextHandler
): Promise<void> => {
  await sessionFactory(ApiContainer.get("sessionOptions"))(req, res, () => void 0);
  return next();
};

export default initializeSession;
