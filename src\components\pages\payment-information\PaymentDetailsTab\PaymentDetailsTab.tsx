import React, { FC, memo, useCallback } from "react";
import Loading from "../../../Loading";
import PaymentBanner, { PaymentBannersLabelsProps } from "./PaymentBanner";
import PaymentStatusAmounts from "./PaymentStatusAmounts";
import { DefaultPaymentDateRange, PaymentsCriteria } from "@src/services/paymentsStatistics/PaymentsService";
import TransactionGrid from "./TransactionHistory/TransactionGrid";
import MobileTransactionGrid from "./TransactionHistory/MobileTransactionGrid";
import { PaymentFilterLabelsProps } from "../PaymentInformationPage";
import { FilterPill } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "@src/context";
import { DEFAULT_PAGE } from "@components/pagination/Pagination";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useRouter } from "next/router";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import TimeRangeFilterForm from "@components/forms/timeRangeFilterForm/TimeRangeFilterForm";
import PaymentHistoryDetails from "@src/services/paymentInformation/PaymentHistoryDetails";
import { DollarAmount } from "@src/services/paymentInformation/DollarAmount";
import { FormattedAmount } from "@src/services/paymentInformation/FormattedAmount";
import { useDependency } from "@src/context/DependencyContext";

const FILTER_TYPE = {
  RANGE: "range",
  STATUS: "status",
  TYPE: "programCode"
};

export type TransactionHistoryLabelProps = {
  paymentGridDescription: string;
  paymentGridType: string;
  paymentGridStatus: string;
  paymentGridAmountDue: string;
  paymentGridDate: string;
  paymentGridDateHelp: string;
  paymentGridContract: string;
  statusPending: string;
  statusProcessed: string;
  downloadContract: string;
  typeCreatorCode: string;
  typeOpportunity: string;
  noPayments: string;
  noPaymentsDescription: string;
  noPaymentsLink: string;
  downloadContractLabel?: string;
  opportunityImageLabel?: string;
  noProcessedPayments: string;
  noProcessedPaymentsDescription: string;
  noPendingPayments: string;
  noPendingPaymentsDescription: string;
  descriptionLabel: string;
  paymentPeriodLabel: string;
};

export type PaymentDetailsLabelsProps = {
  paymentOverview: string;
  paymentTotalPaid: string;
  paymentPendingPayments: string;
  transactionHistory: string;
  transactionHistoryDetails: string;
  paymentInformationClick: string;
  filteredBy: string;
};

export type PaginationProps = {
  next: string;
  prev: string;
  pages: number[];
  currentPage: number;
  onPageChange: (number) => void;
};

export type PaymentDetailsProps = {
  isMobile?: boolean;
  paymentsHistory: PaymentHistoryDetails;
  setPaymentsHistory?: React.Dispatch<React.SetStateAction<PaymentHistoryDetails>>;
  isPayable: boolean;
  labels: {
    paymentBannerLabels: PaymentBannersLabelsProps;
    paymentDetailsLabels: PaymentDetailsLabelsProps;
    paymentHistoryGridLabels: TransactionHistoryLabelProps;
    paymentFilterLabels: PaymentFilterLabelsProps;
    buttonLabel: string;
  };
  locale: string;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  onPaymentBannerClick?: () => void;
  paginationProps: PaginationProps | null;
  isShowingPagination: boolean;
  selectedCriteria: PaymentsCriteria;
  setSelectedCriteria?: React.Dispatch<React.SetStateAction<PaymentsCriteria>>;
  selectedFilters: PaymentFilterProps[];
  setSelectedFilters?: React.Dispatch<React.SetStateAction<PaymentFilterProps[]>>;
  resetDateRangeFields?: () => void;
  initializePagination?: (totalTransactions: number) => void;
  PAYMENTS_DEFAULT_START_DATE: string;
  isCreatorCodeAssigned: boolean;
  analytics: BrowserAnalytics;
};

export type PaymentFilterProps = {
  label: string;
  value: string;
  code: string;
};

export type ProgramCodeLabelProps = {
  creatorNetwork: string;
  affiliate: string;
  theSims: string;
};

const PaymentDetailsTab: FC<PaymentDetailsProps> = ({
  paymentsHistory,
  isPayable,
  labels,
  isMobile = false,
  locale,
  defaultPaymentDateRange,
  onPaymentBannerClick,
  isShowingPagination,
  paginationProps,
  selectedCriteria,
  setSelectedCriteria,
  resetDateRangeFields,
  selectedFilters,
  setSelectedFilters,
  PAYMENTS_DEFAULT_START_DATE,
  isCreatorCodeAssigned,
  analytics
}) => {
  const { paymentBannerLabels, paymentDetailsLabels, paymentFilterLabels } = labels;
  let { paymentHistoryGridLabels } = labels;
  const {
    configuration: { PROGRAM_CODE }
  } = useDependency();
  if (selectedCriteria.status === "PAID") {
    paymentHistoryGridLabels = {
      ...paymentHistoryGridLabels,
      noPayments: paymentHistoryGridLabels.noProcessedPayments,
      noPaymentsDescription: paymentHistoryGridLabels.noProcessedPaymentsDescription,
      noPaymentsLink: null
    };
  }

  const {
    state: { isLoading }
  } = useAppContext() || {};
  const router = useRouter();

  if (selectedCriteria.status === "PENDING") {
    paymentHistoryGridLabels = {
      ...paymentHistoryGridLabels,
      noPayments: paymentHistoryGridLabels.noPendingPayments,
      noPaymentsDescription: paymentHistoryGridLabels.noPendingPaymentsDescription,
      noPaymentsLink: null
    };
  }

  const updatePaymentsFilterDetails = useCallback(
    (formData) => {
      const newSelectedFilters = [] as PaymentFilterProps[];
      const criteria = {
        page: DEFAULT_PAGE
      } as PaymentsCriteria;

      if (formData?.range.value !== "allTime") {
        newSelectedFilters.push({
          label: formData?.range.label,
          value: formData?.range.value,
          code: FILTER_TYPE.RANGE
        });
        criteria.startDate = formData?.startDate;
        criteria.endDate = formData?.endDate;
      } else {
        criteria.startDate = defaultPaymentDateRange?.startDate;
        criteria.endDate = defaultPaymentDateRange?.endDate;
      }

      if (formData?.status.value !== "ALL") {
        newSelectedFilters.push({
          label: formData?.status.value === "PAID" ? paymentFilterLabels.status.paid : formData?.status.label,
          value: formData?.status.value,
          code: FILTER_TYPE.STATUS
        });
        criteria.status = formData?.status.value;
      }

      const getProgramCodeLabel = (programCode: string) => {
        switch (programCode) {
          case "creator_network":
            return paymentFilterLabels.program.creatorNetwork;
          case "affiliate":
            return paymentFilterLabels.program.affiliate;
          case PROGRAM_CODE:
            return paymentFilterLabels.program.theSims;
        }
      };

      if (formData?.programCode && formData?.programCode?.value !== "ALL") {
        newSelectedFilters.push({
          label: getProgramCodeLabel(formData?.programCode.value),
          value: formData?.programCode.value,
          code: FILTER_TYPE.TYPE
        });
        criteria.programCode = formData?.programCode.value;
      }

      analytics.appliedAllPaymentFilters({
        locale: router.locale,
        selectedPaymentStatus: formData?.status?.value || "",
        selectedPaymentType: formData?.programCode?.value || "",
        selectedDateRange: formData?.range?.value || ""
      });

      setSelectedCriteria(criteria);
      setSelectedFilters(newSelectedFilters);
    },
    [paymentsHistory]
  );

  const removeFilter = (removedFilter: PaymentFilterProps) => {
    analytics.removedPaymentFilter({
      locale: router.locale,
      removedFilteredValue: removedFilter.value,
      removedFilterType: removedFilter.code
    });

    setSelectedFilters((selectedFilters) => {
      return selectedFilters.filter((selectedItem) => selectedItem.label !== removedFilter.label);
    });

    if (removedFilter.code === "status" || removedFilter.code === "programCode") {
      setSelectedCriteria((criteria) => {
        return {
          ...criteria,
          [removedFilter.code]: "ALL",
          page: DEFAULT_PAGE
        };
      });
    }
    if (removedFilter.code === "range") {
      resetDateRangeFields();
    }
  };

  const paymentsFilterOptions = {
    filterLabels: {
      filters: paymentFilterLabels.filters,
      dateRange: paymentFilterLabels.dateRange,
      startDate: paymentFilterLabels.startDate,
      endDate: paymentFilterLabels.endDate,
      paymentStatus: paymentFilterLabels.paymentStatus,
      programCode: paymentFilterLabels.programCode,
      applyFilters: paymentFilterLabels.applyFilters,
      startDateRequired: paymentFilterLabels.startDateRequired,
      endDateRequired: paymentFilterLabels.endDateRequired,
      startDateError: paymentFilterLabels.startDateError,
      endDateError: paymentFilterLabels.endDateError,
      ok: paymentFilterLabels.buttons.ok,
      cancel: paymentFilterLabels.buttons.cancel,
      calendar: paymentFilterLabels.header.calendar
    },
    dateRangeOptions: [
      { label: paymentFilterLabels.range.allTime, value: "allTime" },
      { label: paymentFilterLabels.range.thisMonth, value: "thisMonth" },
      { label: paymentFilterLabels.range.pastMonth, value: "past30Days" },
      { label: paymentFilterLabels.range.past90Days, value: "past90Days" },
      { label: paymentFilterLabels.range.past6Months, value: "past6Months" },
      { label: paymentFilterLabels.range.yearToDate, value: "yearToDate" },
      { label: paymentFilterLabels.range.pastYear, value: "lastYear" },
      { label: paymentFilterLabels.range.customDateRange, value: "custom" }
    ],
    paymentStatusOptions: [
      { label: paymentFilterLabels.status.all, value: "ALL" },
      { label: paymentFilterLabels.status.paid, value: "PAID" },
      { label: paymentFilterLabels.status.pending, value: "PENDING" }
    ],
    programCodeOptions: [
      { label: paymentFilterLabels.program.all, value: "ALL" },
      { label: paymentFilterLabels.program.creatorNetwork, value: "creator_network" },
      { label: paymentFilterLabels.program.affiliate, value: "affiliate" },
      { label: paymentFilterLabels.program.theSims, value: PROGRAM_CODE }
    ]
  };

  return isLoading ? (
    <div className="loader">
      <Loading />
    </div>
  ) : (
    <div className="payment-settings payments-details">
      {!isPayable && (
        <PaymentBanner
          {...{
            labels: paymentBannerLabels,
            onPaymentBannerClick
          }}
        />
      )}

      <section className="payment-details-overview">
        <header className="payment-details-overview-header">
          <h4 className="payment-details-heading payment-details-overview-heading">
            {paymentDetailsLabels.paymentOverview}
          </h4>
          <TimeRangeFilterForm
            {...{
              ...paymentsFilterOptions,
              updatePaymentsFilterDetails,
              defaultPaymentDateRange,
              PAYMENTS_DEFAULT_START_DATE,
              selectedFilters,
              isCreatorCodeAssigned,
              selectedCriteria,
              analytics,
              isStatusRequired: true,
              isProgramCodeRequired: true
            }}
          />
        </header>

        {selectedFilters.length > 0 && (
          <div className="payment-details-filters">
            <p className="payments-filter-title">{paymentDetailsLabels.filteredBy} </p>

            {selectedFilters.map((selectedItem) => (
              <FilterPill
                key={selectedItem.value}
                filterLabel={selectedItem.label}
                data-testid={`opportunities-selected-perks-item-label-${selectedItem.value}`}
                onClick={() => {
                  removeFilter(selectedItem);
                }}
              />
            ))}
          </div>
        )}

        <p className="payment-details-date-range">
          {(selectedCriteria.startDate as LocalizedDate).formatWithEpoch("MMM DD, YYYY", locale)} -{" "}
          {(selectedCriteria.endDate as LocalizedDate).formatWithEpoch("MMM DD, YYYY", locale)}
        </p>

        {
          <PaymentStatusAmounts
            paidAmountProps={{
              isDisabled: !isPayable || selectedCriteria?.status === "PENDING",
              payment:
                (paymentsHistory?.totalPaidAmount?.abbreviateOn("M") as FormattedAmount) ||
                (new DollarAmount("0").abbreviateOn("M") as FormattedAmount),
              title: paymentDetailsLabels.paymentTotalPaid,
              isAmountZero: paymentsHistory?.totalPaidAmount?.isZero()
            }}
            pendingAmountProps={{
              variant: "pending-payment",
              isDisabled: !isPayable || selectedCriteria?.status === "PAID",
              payment:
                (paymentsHistory?.totalPendingAmount?.abbreviateOn("M") as FormattedAmount) ||
                (new DollarAmount("0").abbreviateOn("M") as FormattedAmount),
              title: paymentDetailsLabels.paymentPendingPayments,
              isAmountZero: paymentsHistory?.totalPendingAmount?.isZero()
            }}
          />
        }
      </section>

      <section className="payment-details-transaction-history">
        <header>
          <h4 className="payment-details-heading payment-details-transaction-heading">
            {paymentDetailsLabels.transactionHistory}
          </h4>
          <p className="payment-details-transaction-history-text">{paymentDetailsLabels.transactionHistoryDetails}</p>
        </header>

        {(!isMobile || paymentsHistory?.paymentsHistory?.length === 0) && (
          <TransactionGrid
            {...{
              paymentsHistory,
              isShowingPagination,
              labels: paymentHistoryGridLabels,
              paginationProps,
              programCodeLabels: {
                affiliate: paymentFilterLabels.program.affiliate,
                creatorNetwork: paymentFilterLabels.program.creatorNetwork,
                theSims: paymentFilterLabels.program.theSims
              }
            }}
          />
        )}
        {isMobile && paymentsHistory?.paymentsHistory?.length > 0 && (
          <MobileTransactionGrid
            {...{
              paymentsHistory,
              isShowingPagination,
              labels: paymentHistoryGridLabels,
              paginationProps,
              programCodeLabels: {
                affiliate: paymentFilterLabels.program.affiliate,
                creatorNetwork: paymentFilterLabels.program.creatorNetwork,
                theSims: paymentFilterLabels.program.theSims
              }
            }}
          />
        )}
      </section>
    </div>
  );
};

export default memo(PaymentDetailsTab);
