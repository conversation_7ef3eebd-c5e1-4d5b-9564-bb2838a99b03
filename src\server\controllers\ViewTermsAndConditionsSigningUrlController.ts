import { NextApiResponse } from "next";
import TermsAndConditionsHttpClient from "../pactSafe/TermsAndConditionsHttpClient";
import { Service } from "typedi";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewTermsAndConditionsSigningUrlController extends RequestHandler implements Controller {
  constructor(private readonly termsAndConditions: TermsAndConditionsHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const signingUrlResponse = await this.termsAndConditions.signerUrl(req.body);
    this.json(res, signingUrlResponse);
  }
}

export default ViewTermsAndConditionsSigningUrlController;
