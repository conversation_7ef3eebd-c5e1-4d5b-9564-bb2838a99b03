import { Service } from "typedi";
import { NextApiResponse } from "next";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import ConnectedAccountCredentials from "../ConnectedAccountCredentials";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ConnectTwitchAccountController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly connectedAccounts: ConnectedAccountsHttpClient, private readonly redirectUrl: URL) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const isInterestedCreator =
      this.identity(req).type === "INTERESTED_CREATOR" ? this.hasSession(req, "nucleusId") : false;
    const code = this.query(req, "code") as string;
    let credentials: ConnectedAccountCredentials;

    if (isInterestedCreator) {
      credentials = ConnectedAccountCredentials.forInterestedCreator(
        this.session(req, "nucleusId") as number,
        code,
        this.redirectUrl
      );
    } else {
      const creator = this.identity(req);
      credentials = ConnectedAccountCredentials.forCreator(creator.id, code, this.redirectUrl);
    }

    await this.connectedAccounts.connectTwitchAccount(credentials);

    await this.addToSession(req, "accountType", "Twitch");

    this.html(res, "<script>window.close();</script>");
  }
}

export default ConnectTwitchAccountController;
