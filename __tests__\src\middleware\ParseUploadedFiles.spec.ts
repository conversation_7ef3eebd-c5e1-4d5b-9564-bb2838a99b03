import parseUploadedFiles from "@src/middleware/ParseUploadedFiles";
import formidable from "formidable";
import { NextApiResponse } from "next";
import { NextHandler } from "next-connect";
import { NextApiRequestWithMultipartFile, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";

jest.mock("formidable");

describe("ParseUploadedFiles", () => {
  let req: NextApiRequestWithSession;
  let res: NextApiResponse;
  let next: NextHandler;

  beforeEach(() => {
    req = {} as NextApiRequestWithSession;
    res = {} as NextApiResponse;
    next = jest.fn();
  });

  it("parses uploaded files and calls next", async () => {
    const uploadedFile = {
      file1: { filepath: "path/to/file", originalFilename: "file.jpg", mimetype: "image/jpeg", size: 1234 }
    };
    (formidable as jest.Mock).mockImplementation(() => ({
      parse: (_: NextApiRequestWithSession, callback: Function) => {
        callback(null, {}, uploadedFile);
      }
    }));

    await parseUploadedFiles(req, res, next);

    expect((req as NextApiRequestWithMultipartFile).files).toEqual(uploadedFile);
    expect(next).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith();
  });

  it("adds parsing errors to the request and does not call next", async () => {
    const mockError = { message: "Parsing error" };
    (formidable as jest.Mock).mockImplementation(() => ({
      parse: (_: NextApiRequestWithSession, callback: Function) => {
        callback(mockError, {}, {});
      }
    }));

    await expect(parseUploadedFiles(req, res, next)).rejects.toEqual({ err: mockError });

    expect((req as NextApiRequestWithMultipartFile).error).toEqual(mockError);
    expect(next).not.toHaveBeenCalled();
  });
});
