import { act, render, screen, waitFor } from "@testing-library/react";
import { axe } from "jest-axe";
import React from "react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";
import { getComputedStyle } from "../../../../helpers/window";
import { clearDateFor, enterDateFor } from "../../../../helpers/forms";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import config from "config";
import TimeRangeFilterForm, {
  TimeRangeFilterFormProps
} from "@components/forms/timeRangeFilterForm/TimeRangeFilterForm";
import { DefaultPaymentDateRange } from "@src/services/paymentsStatistics/PaymentsService";

describe("TimeRangeFilterForm", () => {
  let windowSpy;
  const locale = "en-us";
  config.PAYMENTS_DEFAULT_START_DATE = "2021-09-01";
  const defaultPaymentDateRange = new DefaultPaymentDateRange(config.PAYMENTS_DEFAULT_START_DATE);
  (useRouter as jest.Mock).mockImplementation(() => ({ locale }));
  const updatePaymentsFilterDetails = jest.fn();
  const filterProps: TimeRangeFilterFormProps = {
    filterLabels: {
      filters: "Filters",
      dateRange: "Date Range",
      startDate: "Start date",
      endDate: "End date",
      paymentStatus: "Payment Status",
      applyFilters: "Apply",
      startDateRequired: "Start date is required",
      endDateRequired: "End date is required",
      startDateError: "Start date must be before the end date",
      endDateError: "End date must be after the start date",
      sameDateError: "Start date and end date cannot be the same",
      ok: "Ok",
      cancel: "Cancel",
      calendar: "Calender"
    },
    dateRangeOptions: [
      { label: "All Time", value: "allTime" },
      { label: "This Month", value: "thisMonth" },
      { label: "Past 30 Days", value: "past30Days" },
      { label: "Past 90 Days", value: "past90Days" },
      { label: "Past 6 Months", value: "past6Months" },
      { label: "Year-to-Date", value: "yearToDate" },
      { label: "Last Year", value: "lastYear" },
      { label: "Custom", value: "custom" }
    ],
    updatePaymentsFilterDetails,
    defaultPaymentDateRange: new DefaultPaymentDateRange(config.PAYMENTS_DEFAULT_START_DATE),
    PAYMENTS_DEFAULT_START_DATE: config.PAYMENTS_DEFAULT_START_DATE,
    selectedFilters: [],
    analytics: ({} as unknown) as BrowserAnalytics,
    selectedCriteria: {
      startDate: defaultPaymentDateRange.startDate,
      endDate: defaultPaymentDateRange.endDate,
      page: 1,
      size: 10
    },
    format: "MM/dd/yy"
  };

  beforeEach(() => {
    windowSpy = jest.spyOn(window, "getComputedStyle");
    windowSpy.mockImplementation(getComputedStyle);
  });

  afterEach(() => windowSpy.mockRestore());

  it("is accessible", async () => {
    let results;
    const { container } = render(<TimeRangeFilterForm {...filterProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("shows payment filter form elements", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });

    await userEvent.click(filterButton);

    await waitFor(() => {
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
      expect(screen.getByText(filterProps.filterLabels.dateRange)).toBeInTheDocument();
      expect(screen.getByText(filterProps.filterLabels.startDate)).toBeInTheDocument();
      expect(screen.getByText(filterProps.filterLabels.endDate)).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /Apply/i })).toBeEnabled();
    });
  });

  it("logs 'Opened Payments Filters Form' event on 'Filters' click", async () => {
    const analytics = ({ openedPaymentsFiltersForm: jest.fn() } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });

    await userEvent.click(filterButton);

    await waitFor(() => {
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    });
  });

  it("sets default values for date range and payment status", async () => {
    const analytics = ({ openedPaymentsFiltersForm: jest.fn() } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });

    await userEvent.click(filterButton);

    await waitFor(() => {
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
      expect(screen.getByText("All Time")).toBeInTheDocument();
    });
  });

  it("sets previously selected values in filter form", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn(),
      appliedPaymentStatusFilter: jest.fn(),
      appliedPaymentTypeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    const selectedFilters = [{ label: "This Month", value: "thisMonth", code: "range" }];
    const selectedCriteria = {
      startDate: LocalizedDate.fromFormattedDate("2023-02-01"),
      endDate: LocalizedDate.fromFormattedDate("2023-02-17"),
      page: 1,
      size: 10
    };
    render(
      <TimeRangeFilterForm
        {...filterProps}
        selectedFilters={selectedFilters}
        analytics={analytics}
        selectedCriteria={selectedCriteria}
      />
    );
    const filterButton = screen.getByRole("button", { name: /Filters/i });

    await userEvent.click(filterButton);

    await waitFor(() => {
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
      expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
      expect(screen.getByText("This Month")).toBeInTheDocument();
      expect(screen.getByDisplayValue("02/01/23")).toBeInTheDocument();
      expect(screen.getByDisplayValue("02/17/23")).toBeInTheDocument();
    });
  });

  it("logs 'Applied Date Range Filter' event when 'Date Range' is selected", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByText(filterProps.filterLabels.dateRange)).toBeInTheDocument();
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangeThisMonthElement = screen.queryByText(/This Month/i);
    expect(dateRangeThisMonthElement).toBeInTheDocument();

    await userEvent.click(dateRangeThisMonthElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "thisMonth"
      });
    });
  });

  it("date range selection should update to 'Custom' when user enter the date manually", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();

    await clearDateFor(/Start date/i);
    await enterDateFor(/Start date/i, new LocalizedDate(LocalizedDate.epochMinusDays(5)).format("MM/DD/yy"));

    expect(await screen.findByText("Custom")).toBeInTheDocument();
  });

  it("updates date range to 'Custom' everytime user enters the date manually ", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();
    await clearDateFor(/Start date/i);
    await enterDateFor(/Start date/i, new LocalizedDate(LocalizedDate.epochMinusDays(10)).format("MM/DD/yy"));
    await userEvent.click(await screen.findByText(/^Custom$/));
    await userEvent.click(screen.getByText(/This Month/i));
    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "thisMonth"
      });
    });

    await clearDateFor(/Start date/i);
    await enterDateFor(/Start date/i, new LocalizedDate(LocalizedDate.epochMinusDays(10)).format("MM/DD/yy"));

    expect(await screen.findByText("Custom")).toBeInTheDocument();
  }, 12_000);

  it("shows error message when end date is lesser than start date", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();
    expect(await screen.findByLabelText(/End date/i)).toBeInTheDocument();
    await clearDateFor(/Start date/i);
    await enterDateFor(/Start date/i, new LocalizedDate(LocalizedDate.epochMinusDays(5)).format("MM/DD/yy"));
    await clearDateFor(/End date/i);

    await enterDateFor(/End date/i, new LocalizedDate(LocalizedDate.epochMinusDays(10)).format("MM/DD/yy"));

    await waitFor(
      () => {
        expect(screen.getByText(filterProps.filterLabels.endDateError)).toBeInTheDocument();
      },
      { timeout: 1_500 }
    );
    expect(screen.getByRole("button", { name: /Apply/i })).toBeDisabled();
  }, 12_000);

  it("shows error message when start date is greater than end date", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();
    expect(await screen.findByLabelText(/End date/i)).toBeInTheDocument();
    await clearDateFor(/End date/i);
    await enterDateFor(/End date/i, new LocalizedDate(LocalizedDate.epochMinusDays(10)).format("MM/DD/yy"));
    await clearDateFor(/Start date/i);

    await enterDateFor(/Start date/i, new LocalizedDate(LocalizedDate.epochMinusDays(5)).format("MM/DD/yy"));

    await waitFor(
      () => {
        expect(screen.getByText(filterProps.filterLabels.startDateError)).toBeInTheDocument();
      },
      { timeout: 1_500 }
    );
    expect(screen.getByRole("button", { name: /Apply/i })).toBeDisabled();
  }, 12_000);

  it("shows error message when start date is blank", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();

    await clearDateFor(/Start date/i);

    await waitFor(() => {
      expect(screen.getByText(filterProps.filterLabels.startDateRequired)).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /Apply/i })).toBeDisabled();
    });
  }, 12_000);

  it("shows error message when end date is blank", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/End date/i)).toBeInTheDocument();

    await clearDateFor(/End date/i);

    await waitFor(() => {
      expect(screen.getByText(filterProps.filterLabels.endDateRequired)).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /Apply/i })).toBeDisabled();
    });
  });

  it("shows error message when start date and end date are the same in custom range", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    const sameDate = new LocalizedDate(LocalizedDate.epochMinusDays(5)).format("MM/DD/yy");
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });

    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();
    expect(await screen.findByLabelText(/End date/i)).toBeInTheDocument();
    await clearDateFor(/Start date/i);
    await enterDateFor(/Start date/i, sameDate);
    await clearDateFor(/End date/i);
    await enterDateFor(/End date/i, sameDate);

    await waitFor(
      () => {
        expect(screen.getByText(filterProps.filterLabels.sameDateError)).toBeInTheDocument();
      },
      { timeout: 1_500 }
    );
    expect(screen.getByRole("button", { name: /Apply/i })).toBeDisabled();
  }, 12_000);

  it("submits filter form", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);
    const filterButton = screen.getByRole("button", { name: /Filters/i });
    await userEvent.click(filterButton);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledTimes(1);
    expect(analytics.openedPaymentsFiltersForm).toHaveBeenCalledWith({ locale: "en-us" });
    expect(await screen.findByLabelText(/Start date/i)).toBeInTheDocument();
    expect(await screen.findByLabelText(/End date/i)).toBeInTheDocument();
    await clearDateFor(/Start date/i);
    await enterDateFor(/Start date/i, new LocalizedDate(LocalizedDate.epochMinusMonths(2)).format("MM/DD/YYYY"));
    await clearDateFor(/End date/i);
    await enterDateFor(/End date/i, new LocalizedDate(LocalizedDate.epochMinusMonths(1)).format("MM/DD/YYYY"));

    await userEvent.click(screen.getByRole("button", { name: /Apply/i }));

    await waitFor(() => {
      expect(updatePaymentsFilterDetails).toHaveBeenCalledTimes(1);
    });
  }, 10_000);

  it("updates date range to 'All Time' when we select manully from dropdown", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    const selectedFilters = [{ label: "This Month", value: "thisMonth", code: "range" }];
    render(<TimeRangeFilterForm {...filterProps} selectedFilters={selectedFilters} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    await userEvent.click(await screen.findByText(/^This Month$/));
    const dateRangeAlltimeElement = screen.queryByText(/All Time/i);
    await userEvent.click(dateRangeAlltimeElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "allTime"
      });
    });
  });

  it("updates date range to 'Past 30 Days' when we select manully from dropdown", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangePast30DaysElement = screen.queryByText(/Past 30 Days/i);
    await userEvent.click(dateRangePast30DaysElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "past30Days"
      });
    });
  });

  it("updates date range to 'Past 90 Days' when we select from manully dropdown", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangePast90DaysElement = screen.queryByText(/Past 90 Days/i);
    await userEvent.click(dateRangePast90DaysElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "past90Days"
      });
    });
  });

  it("updates date range to 'Past 6 Months' when we select manully from dropdown", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangePast6MonthsElement = screen.queryByText(/Past 6 Months/i);
    await userEvent.click(dateRangePast6MonthsElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledTimes(1);
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "past6Months"
      });
    });
  });

  it("updates date range to 'Year-to-Date' when we select from manully dropdown", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangeYearToDateElement = screen.queryByText(/Year-to-Date/i);
    await userEvent.click(dateRangeYearToDateElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "yearToDate"
      });
    });
  });

  it("updates date range to 'Last Year' when we select from manully dropdown", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn()
    } as unknown) as BrowserAnalytics;
    render(<TimeRangeFilterForm {...filterProps} analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Filters/i }));
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangeLastYearElement = screen.queryByText(/Last Year/i);
    await userEvent.click(dateRangeLastYearElement);

    await waitFor(() => {
      expect(analytics.appliedDateRangeFilter).toHaveBeenCalledWith({
        locale: "en-us",
        selectedDateRange: "lastYear"
      });
    });
  });
});
