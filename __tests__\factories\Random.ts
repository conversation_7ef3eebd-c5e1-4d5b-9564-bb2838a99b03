import { faker } from "@faker-js/faker";

const {
  location,
  company,
  datatype,
  date,
  git,
  helpers,
  image,
  lorem,
  number,
  person,
  internet,
  string,
  phone,
  system
} = faker;

export default class Random {
  static nonProductionEnvironment(): string {
    return helpers.arrayElement(["dev", "qa", "uat", "regress"]);
  }

  static userStatus(): string {
    return helpers.arrayElement(["UNREGISTERED", "INACTIVE", "ACTIVE"]);
  }

  static country(): string {
    return helpers.arrayElement(["Canada", "India", "United States", "Ukraine"]);
  }

  static tShirtSize(): string {
    return helpers.arrayElement(["S", "M", "XL", "XXL"]);
  }

  static accountType(): string {
    return helpers.arrayElement(["TWITCH", "FACEBOOK", "INSTAGRAM", "YOUTUBE", "TIKTOK"]);
  }

  static creatorTypeLabel(): string {
    return helpers.arrayElement(["Youtuber", "Live Streamer", "Podcaster", "Blogger"]);
  }

  static creatorType(): string {
    return helpers.arrayElement(["YOUTUBER", "LIVE_STREAMER", "PODCASTER", "BLOGGER"]);
  }

  static entityType(): string {
    return helpers.arrayElement(["INDIVIDUAL", "BUSINESS"]);
  }

  static franchiseType(): string {
    return helpers.arrayElement(["PRIMARY", "SECONDARY"]);
  }

  static platformType(): string {
    return helpers.arrayElement(["PRIMARY", "SECONDARY"]);
  }

  static franchise(): string {
    return helpers.arrayElement(["Need for Speed", "Apex Legends", "Madden NFL", "The Sims", "FIFA"]);
  }

  static region(): string {
    return helpers.arrayElement(["Asia", "Europe", "America"]);
  }

  static applicationStatus(): string {
    return helpers.arrayElement(["UNREVIEWED", "APPROVED", "REJECTED"]);
  }

  static applicationType(): string {
    return helpers.arrayElement(["Applicant", "Creator"]);
  }

  static opportunityType(): string {
    return helpers.arrayElement(["support_a_creator", "marketing_opportunity"]);
  }

  static opportunityVisibility(): string {
    return helpers.arrayElement(["PRIVATE", "PUBLIC"]);
  }

  static participationStatus(): string {
    return helpers.arrayElement(["INVITED", "JOINED", "REJECTED", "PAST"]);
  }

  static contentType(): string {
    return helpers.arrayElement(["video", "image", "blog"]);
  }

  static fileType(): string {
    return helpers.arrayElement([".mov", ".png", ".txt", ".jpeg"]);
  }

  static transactionStatus(): string {
    return helpers.arrayElement(["PENDING", "PAID"]);
  }

  static contentStatus(): string {
    return helpers.arrayElement(["APPROVED", "REJECTED", "UNREVIEWED", "IN_SCAN", "CHANGE_REQUESTED"]);
  }

  static perkCode(): string {
    return helpers.arrayElement(["VIP_EVENT", "TRAVEL", "SWAG", "COLLAB"]);
  }

  static perkLabel(): string {
    return helpers.arrayElement(["VIP Event", "Travel", "Swag", "Collab"]);
  }

  static platform(): string {
    return helpers.arrayElement(["Nintendo Switch", "XBox", "PlayStation", "Mobile", "Mac"]);
  }

  static notificationType(): string {
    return helpers.arrayElement([
      "OPPORTUNITY_INVITATION",
      "CHANNEL_EXPIRY",
      "CONTENT_SUBMISSION_START",
      "CONTENT_SUBMISSION_END",
      "CONTENT_REJECTED",
      "CONTENT_APPROVED",
      "CREATOR_PAYMENT",
      "CONTENT_FEEDBACK",
      "PROFILE_INCOMPLETE",
      "PROFILE_COMPLETE"
    ]);
  }

  static notificationStatus(): string {
    return helpers.arrayElement(["READ", "NEW", "DISMISSED"]);
  }

  static gitBranch(): string {
    return git.branch();
  }

  static url(): string {
    return internet.url();
  }

  static password(): string {
    return internet.password();
  }

  static address(): string {
    return location.streetAddress();
  }

  static boolean(): boolean {
    return datatype.boolean();
  }

  static string(): string {
    return string.alphanumeric();
  }

  static phone(): string {
    return phone.number();
  }

  static uuid(): string {
    return string.uuid();
  }

  static userName(): string {
    return internet.username();
  }

  static sentence(wordCount: number): string {
    return lorem.sentence(wordCount);
  }

  static avatar(): string {
    return image.avatar();
  }

  static imageUrl(): string {
    return image.url();
  }

  static firstName(): string {
    return person.firstName();
  }

  static lastName(): string {
    return person.lastName();
  }

  static fullName(): string {
    return person.fullName();
  }

  static contentVersion(): string {
    return system.semver();
  }

  static email(): string {
    return internet.email();
  }

  static number(range?: { min: number; max: number }): number {
    return number.int(range);
  }

  static countryCode(): string {
    return location.countryCode();
  }

  static state(): string {
    return location.state();
  }

  static city(): string {
    return location.city();
  }

  static streetAddress(): string {
    return location.streetAddress();
  }

  static zipCode(): string {
    return location.zipCode();
  }

  static date(): string {
    return date.recent.toString();
  }

  static locale(): string {
    return helpers.arrayElement(["es-mx", "en-us", "fr-ca", "it-it", "ja-jp"]);
  }

  static nucleusId(): number {
    return +string.numeric(13);
  }

  static subscribersCount(): number {
    return number.int({ min: 3_000_000 });
  }

  static companyName(): string {
    return company.name();
  }

  static amount(): string {
    return string.numeric(13);
  }

  static deliverableStatus(): string {
    return helpers.arrayElement([
      "APPROVED",
      "REJECTED",
      "PENDING",
      "CHANGE_REQUESTED",
      "AWAITING_SUBMISSION",
      "UNLIMITED_CONTENT"
    ]);
  }

  static deliverableFormat(): string {
    return helpers.arrayElement(["SOCIAL", "WEBSITE", "FILE"]);
  }

  static deliverableType(): string {
    return helpers.arrayElement(["SINGLE", "UNLIMITED"]);
  }

  static accessToken(): string {
    return string.alphanumeric(20);
  }

  static scale(): string {
    return helpers.arrayElement(["DAYS", "MONTHS", "YEARS"]);
  }

  static creatorCodeStatus(): string {
    return helpers.arrayElement(["ACTIVE", "INACTIVE", "PENDING", "READYFORAPPROVAL"]);
  }

  static programCode() {
    return helpers.arrayElement(["creator_network", "affiliate", "the_sims"]);
  }
}
