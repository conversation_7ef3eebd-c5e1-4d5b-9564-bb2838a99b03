import { SaveInitialPageServerPropsMiddleware } from "@eait-playerexp-cn/identity";
import { serverPropsMiddlewareFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";

const saveInitialPage = (locale: string) => {
  return serverPropsMiddlewareFactory(new SaveInitialPageServerPropsMiddleware(ApiContainer.get("options"), locale));
};

export default saveInitialPage;
