import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { TermsAndConditionsProps } from "@src/pages/terms-and-conditions";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { InformationPageLabels } from "@src/server/contentManagement/InformationPageMapper";
import { TermsAndConditonsPageLabels } from "@src/server/contentManagement/TermsAndConditionsPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import flags from "@src/utils/feature-flags";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class TermsAndConditionsPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<TermsAndConditionsProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<TermsAndConditionsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    const opportunityId = this.hasSession(req, "opportunityId") ? (this.session(req, "opportunityId") as string) : null;
    const initialPage = this.hasSession(req, `${this.program}.initialPage`)
      ? (this.session(req, `${this.program}.initialPage`) as string)
      : null;
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "termsAndConditions"
    )) as TermsAndConditonsPageLabels & InformationPageLabels & BreadcrumbPageLabels & CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        opportunityId,
        initialPage,
        pageLabels,
        user: authenticatedUser,
        locale: this.currentLocale,
        urlLocale: this.localePathSegment(req, this.currentLocale),
        FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled()
      }
    };
  }
}
