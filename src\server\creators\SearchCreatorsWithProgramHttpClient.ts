import { Inject, Service } from "typedi";
import CreatorCriteria from "./CreatorCriteria";
import CreatorsHttpClient from "./CreatorsHttpClient";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CreatorWithCreatorPrograms from "./CreatorWithCreatorPrograms";

@Service()
class SearchCreatorsWithProgamCodeHttpClient extends CreatorsHttpClient {
  constructor(@Inject("operationsClient") client: TraceableHttpClient) {
    super(client);
  }

  async matching(criteria: CreatorCriteria): Promise<Array<CreatorWithCreatorPrograms>> {
    const response = await this.client.get("/v4/creators", { query: criteria });
    return response.data
      .filter((c) => c.accountInformation.nucleusId === criteria.nucleusId)
      .map((data) => CreatorWithCreatorPrograms.fromApi(data));
  }
}

export default SearchCreatorsWithProgamCodeHttpClient;
