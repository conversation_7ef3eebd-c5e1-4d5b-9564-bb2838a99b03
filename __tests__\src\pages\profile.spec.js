import "reflect-metadata";
import Profile from "../../../src/pages/profile";
import { mockMatchMedia } from "../../helpers/window";
import { act, render, screen, waitFor } from "@testing-library/react";
import { aCreatorWithPayableStatus } from "../../factories/creators/CreatorWithPayableStatus";
import { aCreatorWithFlaggedStatus } from "../../factories/creators/CreatorWithFlaggedStatus";
import { anAccountInformationWithPayableStatus } from "../../factories/creators/AccountInformationWithPayableStatus";
import { useRouter } from "next/router";
import { renderPage } from "../../helpers/page";
import { aConnectedAccount } from "../../factories/creators/ConnectedAccounts";
import "next/config";
import userEvent from "@testing-library/user-event";
import CreatorsService, {
  CreatorWithFlaggedStatusProfile,
  CreatorWithPayableStatusProfile
} from "../../../src/services/CreatorsService";
import ConnectedAccountsService from "../../../src/services/ConnectedAccountsService";
import { useDependency } from "../../../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { axe } from "jest-axe";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../src/context/DependencyContext");
jest.mock("../../../src/utils", () => ({
  ...jest.requireActual("../../../src/utils"),
  useDetectScreen: jest.fn().mockImplementation((width) => width === 10000)
}));
jest.mock("../../../src/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../../src/services/CreatorsService"),
    getCreatorWithCreatorCode: jest.fn(),
    getCreatorWithPayableStatus: jest.fn(),
    getCreatorWithExpiredAccounts: jest.fn(),
    register: jest.fn(),
    update: jest.fn(),
    sendEmailToPOC: jest.fn(),
    getCreatorWithTier: jest.fn(),
    getCreatorWithMultiplePOC: jest.fn()
  };
});
jest.mock("../../../src/services/ConnectedAccountsService");
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

xdescribe("profile", () => {
  mockMatchMedia();
  const analytics = { clickedFooterLink: jest.fn() };
  const footerLinks = [
    ["how", "/#how-it-works"],
    ["perks", "/opportunities-rewards"],
    ["faqs", "/faq"]
  ];
  const router = { locale: "en-us", query: {}, push: jest.fn() };
  const creatorName = "Jane";
  const profileProps = { user: {} };
  const metadataService = {
    getHardwarePartners: jest.fn().mockResolvedValue([]),
    getFranchises: jest.fn().mockResolvedValue([]),
    getPlatformsMatching: jest.fn().mockResolvedValue([]),
    getCreatorTypes: jest.fn().mockResolvedValue([]),
    getCountries: jest.fn().mockResolvedValue([])
  };
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useDependency.mockReturnValue({
      notificationsClient: {},
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "affiliate",
        MENU_ITEMS: {
          sims: { label: "UGX", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        }
      },
      errorHandler
    });
    MetadataService.mockReturnValue(metadataService);
    CreatorsService.getCreatorWithMultiplePOC.mockResolvedValue({
      data: new CreatorWithFlaggedStatusProfile(
        aCreatorWithFlaggedStatus({
          accountInformation: anAccountInformationWithPayableStatus({ firstName: creatorName })
        })
      )
    });
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: new CreatorWithPayableStatusProfile(
        aCreatorWithPayableStatus({
          accountInformation: anAccountInformationWithPayableStatus({ firstName: creatorName })
        })
      )
    });
    ConnectedAccountsService.clearFbPages.mockImplementation(() => Promise.resolve());
  });

  it("shows a creator profile information", async () => {
    const { unmount } = render(<Profile {...profileProps} />);

    await waitFor(() => {
      // expect(screen.getByText(creatorName)).toBeInTheDocument();
      expect(CreatorsService.getCreatorWithMultiplePOC).toHaveBeenCalledTimes(1);
      expect(metadataService.getHardwarePartners).toHaveBeenCalledTimes(1);
      expect(metadataService.getFranchises).toHaveBeenCalledTimes(1);
      expect(metadataService.getPlatformsMatching).toHaveBeenCalledTimes(1);
      expect(metadataService.getCreatorTypes).toHaveBeenCalledTimes(1);
      expect(metadataService.getCountries).toHaveBeenCalledTimes(2);
    });

    // Facebook pages values are cleared from session when the page unloads
    unmount();
    await waitFor(() => expect(ConnectedAccountsService.clearFbPages).toHaveBeenCalledTimes(1));
  });

  it("shows TikTok account card", async () => {
    useRouter.mockImplementation(() => ({ query: { section: "connected-accounts" } }));
    const creatorWith3Accounts = aCreatorWithPayableStatus({
      connectedAccounts: [aConnectedAccount({ type: "FACEBOOK" }, { type: "INSTAGRAM" }, { type: "YOUTUBE" })]
    });
    CreatorsService.getCreatorWithMultiplePOC.mockResolvedValue({
      data: {
        creator: creatorWith3Accounts
      }
    });

    render(<Profile {...profileProps} />);

    expect(await screen.findAllByText("connect-accounts:addAccount")).toHaveLength(5);
    expect(await screen.findByText(/TikTok/i)).toBeInTheDocument();
  });

  xit.each(footerLinks)("logs 'Link Clicked' event when clicking on '%s' marketing page", async (name, url) => {
    const locale = "en-us";

    const { unmount } = renderPage(<Profile {...profileProps} analytics={analytics} />);

    await userEvent.click(await screen.findByRole("button", { name }));

    await waitFor(async () => {
      expect(analytics.clickedFooterLink).toHaveBeenCalledWith({ locale, url }); // Updated the expected arguments
      expect(analytics.clickedFooterLink).toHaveBeenCalledTimes(1);
      expect(router.push).toHaveBeenCalledWith(url);
    });
    unmount();
  });

  xit("is accessible", async () => {
    let results;
    const { container } = renderPage(<Profile {...profileProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
