import { AxiosResponse } from "axios";
import { OpportunityWithPerks } from "./OpportunityService";
import { NextRouter } from "next/router";
import AssignedGameCodeWithStatus from "@src/server/opportunities/AssignedGameCodeWithStatus";
import JoinOpportunityInput from "@src/server/JoinOpportunity/JoinOpportunityInput";
import Participation from "@src/server/opportunities/Participation";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export default class OperationsService {
  constructor(private readonly client: TraceableHttpClient) {}

  async viewAssignedGameCode(id: string): Promise<AxiosResponse<AssignedGameCodeWithStatus[]>> {
    return (await this.client.get(`/api/participations/${id}/game-codes`)) as AxiosResponse<
      AssignedGameCodeWithStatus[]
    >;
  }

  async saveParticipation(
    participation: JoinOpportunityInput,
    opportunity: OpportunityWithPerks
  ): Promise<AxiosResponse<Participation>> {
    return (await this.client.post(`/api/opportunities/${opportunity.id}/participations`, {
      body: participation
    })) as AxiosResponse<Participation>;
  }

  async viewAssignedGameCodes(participationIds: Array<string>): Promise<AxiosResponse<AssignedGameCodeWithStatus>> {
    return (await this.client.post("/api/assigned-game-codes", {
      body: participationIds
    })) as AxiosResponse<AssignedGameCodeWithStatus>;
  }

  async claimGameCode(participationId: string): Promise<void> {
    await this.client.put("/api/claimed-game-codes", { body: { participationId } });
  }

  async navigateToJoinOpportunityNextSteps(opportunity: OpportunityWithPerks, router: NextRouter): Promise<void> {
    if (router.pathname === "/opportunities/[id]/registrations") {
      if (opportunity.hasGameCodes && (router.query.step === "criteria" || router.query.step === undefined)) {
        router.push(`/opportunities/${opportunity.id}/registrations?step=game-code`);
      } else if (
        opportunity.hasDeliverables &&
        (router.query.step === "criteria" || router.query.step === undefined || router.query.step === "game-code")
      ) {
        router.push(`/opportunities/${opportunity.id}/registrations?step=content-guidelines`);
      } else {
        router.push(`/opportunities/${opportunity.id}/registrations?step=thanks`);
      }
    }
  }
}
