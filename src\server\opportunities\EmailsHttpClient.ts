import config from "config";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Inject, Service } from "typedi";
import { SendPOCEmail } from "@src/actions/Creators/SendOpportunityPOCEmail/SendPOCEmail";

export const communicationsApiClient = () => {
  return config.FLAG_COMMUNICATIONS_API_CLIENT ? "communicationsClient" : "operationsClient";
};

@Service()
class EmailsHttpClient {
  constructor(@Inject(communicationsApiClient()) private client: TraceableHttpClient) {}
  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/communications/communications-api/docs/api.html#tag/Emails/operation/sendEmailWithCreatorProgram}
   */
  async sendEmailToPOC(email: SendPOCEmail): Promise<void> {
    await this.client.post("/v2/emails", { body: email });
  }
}
export default EmailsHttpClient;
