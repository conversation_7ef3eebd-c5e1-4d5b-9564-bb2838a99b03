@media screen and (max-width: 767px) {
  .rich-text-table {
    @apply max-w-[294px] p-meas10;
  }
  .rich-text-table-section {
    @apply mb-meas8;
  }
  .rich-text-table-section:last-child {
    @apply mb-meas0;
  }
  .rich-text-table-header > .rich-text-paragraph {
    @apply p-meas0;
  }
  .rich-text-table-row {
    @apply border-none;
  }
  .rich-text-table-cell > .rich-text-paragraph {
    @apply py-meas0 pl-meas0;
  }
}

.rich-text-table-wrapper {
  @apply my-meas16 overflow-hidden rounded-[4px] border-[2px] border-card-border bg-white;
}
.rich-text-table {
  @apply w-[294px] font-text-bold text-desktop-caption1 md:w-[728px] md:text-desktop-body-default;
}
.rich-text-table-header > p {
  @apply mb-meas0 items-start py-meas12 pl-meas10 text-start text-desktop-body-default font-bold;
}
.rich-text-table-row {
  @apply flex flex-col border-b-[1px] border-card-border md:table-row;
}
.rich-text-table-row:last-child {
  @apply border-b-0;
}
.rich-text-table-cell > p {
  @apply mb-meas0 py-meas12 pl-meas10 text-desktop-body-small;
}
