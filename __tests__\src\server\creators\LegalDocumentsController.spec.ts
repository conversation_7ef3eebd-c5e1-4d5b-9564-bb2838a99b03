import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import LegalDocumentsController from "@src/server/creators/LegalDocumentsController";
import LegalDocumentsHttpClient from "@src/server/legalDocuments/LegalDocumentsHttpClient";
import { someSignedLegalDocuments } from "../../../factories/legalDocuments/SignedLegalDocuments";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("LegalDocumentsController", () => {
  let controller: LegalDocumentsController;
  const session = { save: jest.fn() };

  beforeEach(() => jest.clearAllMocks());

  it("gets legal documents", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/v2/legal-documents",
      session: {
        ...session,
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const signedLegalDocuments = someSignedLegalDocuments();
    const legalDocuments = { allWithSignature: jest.fn().mockResolvedValue({ data: signedLegalDocuments }) };
    controller = new LegalDocumentsController((legalDocuments as unknown) as LegalDocumentsHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData()).data).toEqual(signedLegalDocuments);
    expect(legalDocuments.allWithSignature).toHaveBeenCalledTimes(1);
    expect(legalDocuments.allWithSignature).toHaveBeenCalledWith(userId);
  });
});
