import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import flags from "@src/utils/feature-flags";
import { FacebookPages } from "@src/server/channels/ConnectedAccountsHttpClient";
import { OAuthError } from "@src/server/channels/tiktok/ConnectTikTokAccountController";
import { ProfilePageProps } from "../ProfileProps";

export default class ProfilePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ProfilePageProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<ProfilePageProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};
    const error = this.hasSession(req, "error") ? (this.session(req, "error") as OAuthError) : null;
    const invalidTikTokScope = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : false;

    await this.removeFromSession(req, "error");
    await this.removeFromSession(req, "INVALID_TIKTOK_SCOPE");

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pages,
        error,
        invalidTikTokScope,
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "profile",
          "franchises-you-play",
          "information",
          "connect-accounts",
          "communication-preferences",
          "legal-documents",
          "creator-type",
          "point-of-contact",
          "notifications",
          "opportunities"
        ])),
        FLAG_COUNTRIES_BY_TYPE: flags.isCountriesByTypeEnabled()
      }
    };
  }
}
