{"titleSmall": "Avantages et récompenses", "title": "Envoyer le contenu", "subTitle": "De fantastiques opportunités sont disponibles pour nos membres. Lisez la suite pour en savoir plus.", "gameTitle": "<PERSON><PERSON><PERSON> du jeu", "gameSubTitle": "Assistez à des événements exclusifs, jouez et découvrez votre jeu préféré avant sa sortie, participez à un stream en direct d'Electronic Arts, accédez à des événements spéciaux dans les studios et bien plus encore.", "competitionTitle": "Rejoignez une liste exceptionnelle de créateurs et de créatrices de contenu", "competitionSubTitle": "Faites-vous de nouvelles connaissances, transmettez vos compétences et collaborez avec d'autres créateurs et d’autres créatrices de contenu du monde entier.", "moreRewardsTitle": "Plus de récompenses", "moreRewardsSubTitle": "Mais ce n'est pas tout ! Il existe encore beaucoup plus de choses à offrir à nos créateurs et nos créatrices.", "startCreatingTitle": "<PERSON>r<PERSON><PERSON> à créer ?", "startCreatingSubTitle": "Nous accueillons des créateurs et des créatrices aux talents et aux parcours variés, ce qui rend la communauté du Creator Network diversifiée et stimulante.", "startCreatingConclusion": "Serez-vous à la hauteur du défi ?", "joinNow": "Connexion", "discountCode": "Codes de réduction", "gameCode": "Code de jeu", "currency": "Devises en jeu", "merchandise": "Produits dérivés exclusifs", "gameAccessTitle": "Accès en avance", "gameAccessDescription": "Vous voulez découvrir les derniers jeux d’Electronic Arts avant tout le monde ? Nous encourageons la créativité au sein de notre communauté en offrant un accès en avance à vos jeux préférés.", "channelTitle": "Partenariats de chaînes", "channelDescription": "Collaborez avec nous et créez des partenariats de chaîne à long terme pour vous accompagner et développer votre communauté.", "sponsorTitle": "Contenu sponsorisé", "sponsorDescription": "Travaillez avec nous pour créer un contenu incroyable autour de vos jeux préférés pour votre chaîne.", "ebonixTitle": "Créatrice", "ebonixSubTitle": "EBONIX", "ebonixDescription": "Pour être une force de changement, il faut pouvoir se faire entendre. J'ai eu la chance de faire valoir mes idées pour faire évoluer les Sims sur le plan générationnel en consultant l'équipe du jeu sur la mise à jour des couleurs de peau ! C'est un grand pas en avant vers davantage de diversité et d'inclusion et je suis fière d'en avoir fait partie !", "description": "Découvrez et rejoignez les différentes opportunités disponibles en ce moment. Nous ajoutons sans cesse quelque chose de nouveau, alors revenez consulter les nouveautés régulièrement.", "franchisePreferences": "Préférences de franchises", "returnToOpportunities": "Retour", "details": "Détails", "location": "<PERSON><PERSON>", "eventDate": "Date de l'événement", "registration": "Enregistrement", "registrationStartDate": "Date et heure de début de l’enregistrement", "registrationEndDate": "Date et heure de fin de l’enregistrement", "signUp": "<PERSON>'abonner", "whatIsIt": "Quel est-il ?", "opportunityDescription": "Description", "aboutThisOpportunity": "About this Opportunity", "viewAll": "<PERSON><PERSON> afficher", "thingsToDo": "Prenez les mesures suivantes", "freeGameCode": "Code de jeu gratuit", "freeGameCodeDesc": "Ce code de jeu est disponible sur les plateformes suivantes. Notez que vous recevrez le code immédiatement après votre adhésion :", "thingsToDo1": "Ins<PERSON><PERSON>z-vous si cela vous intéresse", "thingsToDo2": "Lisez et acceptez les conditions de l’opportunité", "thingsToDo3": "Envoyer le contenu", "thingsToDo4": "Assister à l’événement", "join": "Rejoindre", "joinOpportunity": "Rejoindre l’opportunité", "thanksMessage": "Merci ! Enregistrement réussi.", "criteria": "Critères", "contentGuidelines": "Conditions requises pour l'envoi de contenu", "downloadEaLogo": "Télécharger le filigrane d’Electronic Arts", "joined": "a rejoint", "past": "Past", "open": "Open", "invited": "Invited", "completed": "<PERSON><PERSON><PERSON><PERSON>", "remote": "Événement en ligne", "inPersonEvent": "Événement en personne", "grabACodeEvent": "Obtenir un code", "paid": "Montant en euros payé", "contentSubmission": "Envoi de contenu", "event": "Événement", "goToDiscord": "Accéder à Discord", "getGameCode": "Votre code de jeu", "gameCodeNotReady": "Votre code de jeu n’est pas encore prêt !", "gameCodeInfo": "Votre code apparaîtra sur la page Opportunité. Veuillez revenir plus tard", "contentSubmissionSubtitle": "Vos envois de contenu", "submissionWindow": "Fenêtre d’envoi :", "submitContent": "Envoyer le contenu", "notSubmittedContent": "Vous n'avez pas envoyé de contenu.", "additionalInfo": "Informations supplémentaires", "discordChannel": "<PERSON><PERSON>", "resources": "Ressources", "youAreRegistered": "Enregistrement à cette opportunité réussi.", "pointOfContact": "Point de contact", "searchForOpportunites": "Recherche d’opportunités", "searchResults": "Résultats de recherche pour", "apply": "Appliquer", "perks": "Atouts", "filteredBy": "Filtrer par", "submitYourContent": "Envoyer votre contenu", "submitYourContentDesc": "Dans le cadre du contrat conclu avec le EA Creator Network, vous devez fournir toutes les clauses de non-responsabilité suivantes :", "guideLinePoint1": "Intégrer le filigrane 'Sponsorisé par EA' dans les 5 premières secondes de votre vidéo ou de votre stream.", "guideLinePoint2": "Mentionner de manière visible 'Sponsorisé par EA' au début de la description de la vidéo/de la barre d'infos ainsi qu’un lien dans la description", "guideLinePoint3": "Insérer une expression bien audible dans les 5 premières secondes de votre vidéo ou de votre stream, indiquant 'Merci à Electronic Arts de sponsoriser cette vidéo' ou équivalent", "guideLinePoint4": "Veiller à la bonne utilisation des outils relatifs à l’obligation d’informer des plateformes de contenu, tels que les fonctionnalités 'cette vidéo inclut une communication commerciale' de YouTube et 'partenariat' d'Instagram.", "guideLinePoint5": "Intégrer le filigrane 'Présenté par le EA Creator Network' dans les 5 premières secondes de votre vidéo ou de votre stream.", "guideLinePoint6": "Mentionner de manière visible 'Présenté par le EA Creator Network' au début de la description de la vidéo/de la barre d'infos.", "guideLinePoint7": "Insérer une expression bien audible dans les 5 premières secondes de votre vidéo ou de votre stream, indiquant 'Merci à Electronic Arts de m'avoir fait bénéficier d'un accès anticipé à cet événement' (ou équivalent)", "contentGuideSubDesc": "Pour toute question, contactez votre responsable de communauté.", "addAnother": "Ajouter un autre contenu", "urlPlaceholder": "Veuillez saisir l’URL du contenu :", "duplicateUrl": "Ce lien a déjà été envoyé. Les doublons de vidéos ne sont pas autorisés.", "instagramErrorUrl": "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.", "videoNotFromChannel": "Cette vidéo ne provient pas d'un réseau social connecté à votre compte Creator Network.", "youtubeVideoError": "Aucune vidéo Youtube n'a été trouvée avec l'identifiant donné.", "genericContentError": "Veuillez saisir une URL valide.", "noAvailableOpportunities": "Aucune opportunité disponible", "noAvailableOpportunitiesDesc": "Aucune opportunité disponible pour le moment.", "noAvailableSearchResults": "Aucune opportunité trouvée", "noAvailableSearchResultsDesc": "Veuillez vérifier l’orthographe, essayer des mots-clés différents ou ajuster vos options de filtre.", "Back": "Retour", "overview": "<PERSON><PERSON><PERSON><PERSON>", "perksSubtitle": "Ce que vous obtiendrez en nous rejoignant", "registrationCloses": "Fin de l’enregistrement :", "registrationEnds": "Registration Ends", "submissionCloses": "Fin de l’envoi :", "submissionOpens": "Ouverture de l'envoi :", "submissionClosedMessage": "La fenêtre d’envoi de cette opportunité est fermée.", "gameCodePending": "Code de jeu en attente. Revenez plus tard", "opportunityType": "Type d’opportunité", "more": "Plus", "gameCodeTitle": "Votre code de jeu", "copyGameCode": "Co<PERSON>r le code de jeu", "copied": "<PERSON><PERSON><PERSON>", "submitContentText": "Utilisez cette section pour envoyer du contenu à cette opportunité.", "submitWindowClosedText": "La fenêtre d’envoi de contenu pour cette opportunité est fermée et aucun nouveau contenu ne peut être envoyé.", "collab": "Collaboration", "dailyAllowance": "Rémunération quotidienne", "designCouncil": "Conseil de conception", "earlyAccess": "Accès en avance", "exclusivePreview": "Aperçu exclusif", "food": "Nourriture", "freeGameCodePerk": "Code de jeu gratuit", "hotel": "Hôtel", "paidPerk": "<PERSON><PERSON>", "privateDiscordChannel": "Discord privé", "swag": "<PERSON><PERSON>", "travel": "Déplacements", "vipEvent": "Évènement VIP", "creatorCodePerk": "Code de création", "supportACreator": "Soutenir une personne créatrice de contenu", "whatToDoTitle": "Que faire ?", "toDoTitle": "À faire", "whatToDoDescription": "Si vous souhaitez rejoindre cette opportunité, voici ce que vous devez faire :", "whatToDoLabels": {"joinOpportunity": "Rejoindre l’opportunité", "getGameCode": "Obtenir le code de jeu", "signContract": "Signer un contrat légal pour l’opportunité", "getPaid": "Recevoir une rémunération", "attendInPersonEvent": "Assister à l’événement", "attendOnlineEvent": "Assister à l’événement en ligne", "makeContent": "<PERSON><PERSON><PERSON> du contenu", "submitContent": "Envoyer le contenu", "getCreatorCode": "Obtenir le code de création"}, "decline": "Refuser", "declineModalTitle": "Refuser l’invitation", "declineModalContent": "Voulez-vous vraiment refuser l’invitation ? Vous ne pourrez pas rejoindre cette opportunité si vous changez d’avis.", "declinedText": "<PERSON><PERSON> avez refusé cette opportunité.", "invitedText": "Vous avez reçu une invitation pour rejoindre", "declined": "<PERSON><PERSON><PERSON><PERSON>", "contentGuidelinesTitle": "Directives pour le contenu", "creatorCode": {"title": "Votre code de création", "copyLabel": "<PERSON><PERSON><PERSON>", "copyText": "Copier le code de création", "textCopied": "<PERSON><PERSON><PERSON>", "codeNotYetActiveMessage": "Votre code de création pour cette opportunité n’est pas encore actif.", "codeExpiredMessage": "Votre code de création pour cette opportunité n’est plus actif.", "codeActivationStartTime": "Code Activation Start Time", "codeActivationEndTime": "Code Activation End Time"}, "remoteEvent": {"title": "Détails de l'événement en ligne", "eventTime": "Heure de l’événement", "description": "Cet événement est terminé.", "joinEvent": "Rejoindre l’événement", "noPasswordRequired": "Aucun mot de passe nécessaire", "noEventDetailsAdded": "Les détails de l'événement n’ont pas encore été ajoutés", "NoMeetingLinkInfo": "Contact Community Manager for event details."}, "exitJoinOpportunityFlow": "Exit join opportunity flow", "supportACreatorDisclosure": {"title": "Disclosure", "subTitle": "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:", "description1": "Include audible and written disclosure to let viewers know that using your Creator Code directly supports you. “Using my Creator Code directly supports me.” (or similar).", "description2": "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your video or stream.", "description3": "Prominently include “Sponsored by EA” “Sponsored”, or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #SponsoredbyEA, #Sponsored, or #Ad are also acceptable.", "description4": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for sponsoring this video.” (or similar).", "description5": "Ensure correct use of content platforms disclosure tools such as YouTube’s “includes paid promotion” and Instagram’s “Partnership” options."}, "contentDeliverables": "Deliverables", "registrationWindow": "Registration Window", "contentSubmissionWindow": "Submission Window", "closed": "Closed", "activationWindow": "Code Window", "keyDates": "Key Dates", "inPersonStartTime": "In-Person Event Start Time:", "inPersonEndTime": "In-Person Event End Time:", "onlineEventStartTime": "Online Event Start Time:", "onlineEventEndTime": "Online Event End Time:", "contentSubmissionDeadline": "Content Submission Deadline:", "viewDeliverables": "View Deliverables", "codeActivationStartTime": "Code Activation Start Time:", "codeActivationEndTime": "Code Activation End Time:", "checkBackSoon": "Check back soon", "payment": "Payment", "sales": "% of Sales", "eventDetails": {"eventStartTime": "Event Start Time", "eventEndTime": "Event End Time", "eventLocation": "Event Location", "password": "Password", "info": "Info", "copyAddress": "Copy address", "copyPassword": "Copy password"}, "eventInformation": "Event Information"}