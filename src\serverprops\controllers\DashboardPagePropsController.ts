import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import BrowserAnalytics, { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export type DashboardProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user?: AuthenticatedUser;
  locale: string;
  analytics?: BrowserAnalytics;
};

export default class DashboardPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<DashboardProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<DashboardProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        user: authenticatedUser,
        locale: this.currentLocale,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "my-content",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
