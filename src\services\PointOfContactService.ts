import { AxiosResponse } from "axios";
import { SendPOCEmail } from "@src/actions/Creators/SendOpportunityPOCEmail/SendPOCEmail";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
class PointOfContactService {
  constructor(private readonly client: TraceableHttpClient) {}

  sendEmailToPOC = async (email: SendPOCEmail): Promise<AxiosResponse<void>> => {
    return (await this.client.post("/api/v2/emails", { body: email })) as AxiosResponse<void>;
  };
}

export default PointOfContactService;
