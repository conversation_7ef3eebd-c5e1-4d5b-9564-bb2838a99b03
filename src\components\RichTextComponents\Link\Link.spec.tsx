import { render, screen } from "@testing-library/react";
import Link from "./Link";
import Random from "__tests__/factories/Random";
import { axe } from "jest-axe";
import { useDependency } from "@src/context/DependencyContext";
import { ArticleType } from "@components/ArticlePage/ArticlePage";

jest.mock("@src/context/DependencyContext");

describe("Link", () => {
  const linkProps = {
    url: Random.url(),
    children: Random.sentence(3)
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        BASE_PATH: "https://example.com"
      }
    });
  });

  it("shows link with correct attributes", () => {
    render(<Link {...linkProps} />);

    const link = screen.getByRole("link", { name: linkProps.children });
    expect(link).toHaveAttribute("href", linkProps.url);
    expect(link).toHaveAttribute("target", "_self");
  });

  it("opens article with slug", () => {
    const linkWithSlugProps = {
      ...linkProps,
      page: {
        slug: "test-article",
        type: "Article" as ArticleType
      }
    };
    const {
      configuration: { BASE_PATH }
    } = useDependency();

    render(<Link {...linkWithSlugProps} />);

    const link = screen.getByRole("link", { name: linkProps.children });
    expect(link).toHaveAttribute("href", `${BASE_PATH}/articles/${linkWithSlugProps.page.slug}`);
  });

  it("opens in new tab", () => {
    render(<Link {...linkProps} openInNewTab />);

    expect(screen.getByRole("link", { name: linkProps.children })).toHaveAttribute("target", "_blank");
  });

  it("applies custom CSS class when provided", () => {
    const className = "custom-link";

    render(<Link {...linkProps} className={className} />);

    expect(screen.getByRole("link", { name: linkProps.children })).toHaveClass(className);
  });

  it("is accessible", async () => {
    const { container } = render(<Link {...linkProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
