import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import CommunicationPreferencesPropsController from "./controllers/CommunicationPreferencesPropsController";
import config from "config";

const communicationPreferencesProps = (locale: string) =>
  serverPropsControllerFactory(
    new CommunicationPreferencesPropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default communicationPreferencesProps;
