import RichText, { EmbeddedItem } from "@components/RichText/RichText";
import React from "react";

type FaqPageProps = {
  title: string;
  body: {
    richText: string;
    embeddedItems: EmbeddedItem[];
  };
};

const FaqPage = ({ body }: FaqPageProps) => {
  return (
    <div className="faq-page-container" data-testid="faq-page">
      <div className="faq-page-content">
        <RichText {...body} />
      </div>
    </div>
  );
};

export default FaqPage;
