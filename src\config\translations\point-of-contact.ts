export default function labelsPointOfContact(t) {
  return {
    title: t("point-of-contact:title"),
    labels: {
      subject: t("point-of-contact:labels.subject"),
      body: t("point-of-contact:labels.body")
    },
    subjectPlaceholder: t("point-of-contact:subjectPlaceholder"),
    messages: {
      subject: t("point-of-contact:messages.subject"),
      subjectTooLong: t("point-of-contact:messages.subjectTooLong"),
      body: t("point-of-contact:messages.body"),
      bodyTooLong: t("point-of-contact:messages.subjectTooLong")
    },
    buttons: {
      send: t("send"),
      cancel: t("cancel")
    },
    success: {
      modalHeader: t("point-of-contact:success.modalHeader"),
      modalMessage: t("point-of-contact:success.modalMessage")
    }
  };
}
