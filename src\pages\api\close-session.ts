import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";
import ApiContainer from "@src/ApiContainer";
import { CloseSessionController } from "@eait-playerexp-cn/authentication";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(CloseSessionController);
  await controller.handle(req, res);
});

export default router.handler({ onError });
