import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import NoAccountPagePropsController from "@src/serverprops/controllers/NoAccountPagePropsController";
import { InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");

describe("NoAccountPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("returns notFound when no interestedCreator in session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = {};
    const controller = new NoAccountPagePropsController(options, contents, currentLocale);

    const props = await controller.handle(req, res);

    expect(props).toEqual({ notFound: true });
  });

  it("gets server side props with interestedCreator in session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const interestedCreator: InitialInterestedCreator = {
      nucleusId: "**********",
      originEmail: "<EMAIL>",
      dateOfBirth: "************",
      defaultGamerTag: "0313141",
      analyticsId: "test-analytics-id"
    };
    req.session = {
      [`${program}.noAccountCreator`]: interestedCreator,
      noAccountCreator: interestedCreator
    };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {
      header: { title: "No Account" },
      content: { message: "Create your account" }
    };
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new NoAccountPagePropsController(options, contents, currentLocale);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "noAccount");
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels: labels,
        interestedCreator,
        user: {
          analyticsId: interestedCreator.analyticsId,
          username: interestedCreator.defaultGamerTag,
          type: "INTERESTED_CREATOR"
        },
        locale: currentLocale,
        showInitialMessage: null
      }
    });
  });

  it("gets server side props with interestedCreator and showInitialMessage in session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const interestedCreator: InitialInterestedCreator = {
      nucleusId: "**********",
      originEmail: "<EMAIL>",
      dateOfBirth: "************",
      defaultGamerTag: "0313141",
      analyticsId: "test-analytics-id"
    };
    const showInitialMessage = true;
    req.session = {
      [`${program}.noAccountCreator`]: interestedCreator,
      noAccountCreator: interestedCreator,
      [`${program}.showInitialMessage`]: showInitialMessage,
      showInitialMessage
    };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {
      header: { title: "No Account" },
      content: { message: "Create your account" }
    };
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new NoAccountPagePropsController(options, contents, currentLocale);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "noAccount");
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels: labels,
        interestedCreator,
        user: {
          analyticsId: interestedCreator.analyticsId,
          username: interestedCreator.defaultGamerTag,
          type: "INTERESTED_CREATOR"
        },
        locale: currentLocale,
        showInitialMessage
      }
    });
  });
});
