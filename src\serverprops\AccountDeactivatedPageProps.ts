import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import AccountDeactivatedPagePropsController from "@src/serverprops/controllers/AccountDeactivatedPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";

const accountDeactivatedPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new AccountDeactivatedPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale
    )
  );

export default accountDeactivatedPageProps;
