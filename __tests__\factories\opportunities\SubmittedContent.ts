import { Factory } from "fishery";
import SubmittedContent from "../../../src/submittedContent/SubmittedContent";
import Random from "../Random";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

const factory = Factory.define(() => ({
  contentType: "video",
  contentTypeLabel: "Video",
  contentUri: Random.imageUrl(),
  id: Random.uuid(),
  name: Random.sentence(3),
  opportunityId: Random.uuid(),
  opportunityName: Random.sentence(10),
  status: Random.contentStatus(),
  submittedDate: LocalizedDate.epochMinusDays(2),
  submittedOn: LocalizedDate.epochMinusDays(2),
  thumbnail: Random.imageUrl(),
  type: Random.accountType(),
  sourceType: Random.string(),
  contentTitle: Random.sentence(3),
  contentDescription: Random.sentence(10)
}));

export function aSubmittedContent(override = {}): SubmittedContent {
  return factory.build(override) as SubmittedContent;
}
