.time-range-filter-button {
  @apply float-right w-fit;
}

.time-range-filter-container {
  @apply relative;
}

.time-range-filter-section {
  @apply fixed bottom-meas0 left-meas0 right-meas0 top-meas0 z-[100] w-full rounded border border-gray-20 bg-[#FFFFFF] p-meas8 xs:overflow-y-scroll md:absolute md:bottom-auto md:left-auto md:top-[46px] md:w-[340px] md:overflow-y-visible;
}

.time-range-filter-form-elements .select-header-title,
.time-range-filter-form-elements .select-list,
.time-range-filter-form-elements .select-label,
.time-range-filter-section > form,
.time-range-filter-footer > button {
  @apply w-full;
}

.time-range-filter-footer > .btn-primary {
  @apply mt-meas10;
}
.time-range-filter-button .btn-secondary:active {
  @apply bg-none;
}

.time-range-filter-form-elements > .select-box > .select-label {
  @apply text-[12px] font-normal leading-[1rem];
}

.time-range-filter-form-elements > .select-box > .select-header {
  @apply rounded-[2px] border;
}

.time-range-filter-form-elements > .select-box > .expanded {
  @apply border-primary;
}

.time-range-filter-form-elements > .select-box > .select-header > .select-header-title > .select-header-label {
  @apply text-[12px] leading-[1rem] text-black;
}

.time-range-filter-form-elements > .select-box > .select-header > .select-header-title > .icon {
  @apply text-black;
}

.filter-date-container {
  @apply mt-meas5 block md:grid;
  gap: 12px;
  grid-template-columns: 1fr 1fr;
}

.filter-date-container-label {
  @apply py-meas1 text-[16px] leading-[24px] text-white;
}

.filter-date-container .react-datepicker {
  @apply bg-white text-black;
}

.filter-date-container .datepicker-ok-option {
  @apply ml-meas3 rounded-[2px] border border-primary bg-primary px-[10px] text-[10px] font-bold leading-[16px]  text-white;
}

.filter-date-container .datepicker-cancel-option {
  @apply text-black;
}

.filter-date-container .react-datepicker .react-datepicker__children-container {
  @apply w-auto;
}

.filter-date-container .react-datepicker__day--selected .datepicker-day {
  @apply bg-primary;
}

.filter-date-container .datepicker-current-month {
  @apply text-black;
}

.filter-date-container .input-text-field {
  @apply bg-white text-black;
}

.filter-date-container .input-text-field:hover {
  @apply bg-white text-black;
}

.filter-date-container .form-input-box .input-box .input-container {
  @apply rounded-[2px] text-black;
}

.filter-date-container .form-input-box .input-box .input-container:hover {
  @apply bg-white text-black;
}

.filter-date-container .form-input-box .input-box.error.datepicker-error-input {
  @apply border-error-50;
}

.filter-date-container .form-input-box .input-box {
  @apply rounded-[2px] border text-black;
}

.filter-date-container .form-input-box .input-box:hover {
  @apply border-primary;
}

.time-range-filter-form-elements > .filter-date-container > .form-input-box > .input-box-label {
  @apply font-normal text-black;
}

.time-range-filter-form-elements .select-list {
  @apply border border-gray-20 bg-white text-black hover:bg-white focus:bg-white;
}

.time-range-filter-form-elements .select-list .select-list-item {
  @apply text-black hover:bg-gray-20 focus:bg-gray-20;
}

.time-range-filter-form-elements .select-list .selected.select-list-item {
  @apply bg-gray-20;
}

.time-range-filter-close-button {
  @apply fixed right-meas12 z-[100] block h-[16px] w-[16px] text-black md:hidden;
}

.filter-date-container .react-datepicker__day {
  @apply text-black;
}

.filter-date-container .react-datepicker__day--disabled {
  @apply text-gray-50;
}

.filter-date-container .form-input-box .input-box input:disabled {
  @apply bg-[#ffffff00];
}

.filter-date-container .form-input-box .input-box-disabled:hover {
  @apply border-gray-20;
}
