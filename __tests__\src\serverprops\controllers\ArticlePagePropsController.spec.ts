import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import ArticlesHttpClient from "@src/server/contentModal/ArticlesHttpClient";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";
import { NextApiResponse } from "next";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import ArticlePagePropsController from "@src/serverprops/controllers/ArticlePagePropsController";

jest.mock("@src/analytics/BrowserAnalytics");
jest.mock("@src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations");

describe("ArticlePagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const articlesClient = ({
    matching: jest.fn()
  } as unknown) as ArticlesHttpClient;
  const currentLocale = "en-us";
  const slug = ["test-article"];

  beforeEach(() => {
    jest.clearAllMocks();
    (serverSideTranslations as jest.Mock).mockResolvedValue({});
  });

  it("gets server side props for article page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = { id: "123" };
    const user = { id: "123", name: "Test User" };
    const article = { title: "Test Article" };
    const configuration = {};

    req.session = { identity: identity };
    (AuthenticatedUserFactory.fromIdentity as jest.Mock).mockReturnValue(user);
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (articlesClient.matching as jest.Mock).mockResolvedValue({ data: article });

    const controller = new ArticlePagePropsController(options, articlesClient, currentLocale, slug, "");
    const result = await controller.handle(req, res);

    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        article: article,
        slug,
        user: user,
        locale: currentLocale
      }
    });
  });

  it("gets server side props for unauthenticated user", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const article = { title: "Test Article" };
    const configuration = {};

    req.session = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (articlesClient.matching as jest.Mock).mockResolvedValue({ data: article });

    const controller = new ArticlePagePropsController(options, articlesClient, currentLocale, slug, "");
    const result = await controller.handle(req, res);

    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        article: article,
        slug,
        user: null,
        locale: currentLocale
      }
    });
  });

  it("returns notFound when article is not found", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = { id: "123" };
    req.session = { identity: identity };

    (articlesClient.matching as jest.Mock).mockRejectedValue(new ArticleNotFound("Article not found"));

    const controller = new ArticlePagePropsController(options, articlesClient, currentLocale, slug, "");
    const result = await controller.handle(req, res);

    expect(result).toEqual({ notFound: true });
  });

  it("throws error for unexpected exceptions", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const error = new Error("Unexpected error");
    const identity = { id: "123" };
    req.session = { identity: identity };

    (articlesClient.matching as jest.Mock).mockRejectedValue(error);

    const controller = new ArticlePagePropsController(options, articlesClient, currentLocale, slug, "");

    await expect(controller.handle(req, res)).rejects.toThrow(error);
  });
});
