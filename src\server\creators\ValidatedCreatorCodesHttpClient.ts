import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

type ValidatedCreatorCode = {
  creatorCode: string;
  isValidCode: boolean;
};

@Service()
class ValidatedCreatorCodesHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async validateCreatorCode(creatorCode: string): Promise<ValidatedCreatorCode> {
    const response = await this.client.get(`/v1/creator-code/${creatorCode}`);
    return Promise.resolve(response.data);
  }
}

export default ValidatedCreatorCodesHttpClient;
