import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type RangeFilters = {
  pastYear: string;
  pastMonth: string;
  customDateRange: string;
  thisMonth: string;
  allTime: string;
  past90Days: string;
  past6Months: string;
  yearToDate: string;
};

export type StatusFilters = {
  all: string;
  paid: string;
  pending: string;
};

export type ProgramCodeFilters = {
  all: string;
  creatorNetwork: string;
  affiliate: string;
  theSims: string;
};

export type TimeRangeFilterPageLabels = {
  timeRangeFilterPageLabels: {
    allFilters: string;
    filterby: string;
    startDate: string;
    startDateRequired: string;
    startDateError: string;
    endDate: string;
    endDateRequired: string;
    endDateError: string;
    sameDateError: string;
    range: RangeFilters;
    filteredBy: string;
    dateRange: string;
    paymentStatus: string;
    opportunityType: string;
    applyFilters: string;
    status: StatusFilters;
    program: ProgramCodeFilters;
    programCode: string;
  };
};

export class TimeRangeFilterPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): TimeRangeFilterPageLabels {
    const microCopy = new MicroCopy(microCopies);
    return {
      timeRangeFilterPageLabels: {
        allFilters: microCopy.get("timeRangeFilter.allFilters"),
        filterby: microCopy.get("timeRangeFilter.filterby"),
        startDate: microCopy.get("timeRangeFilter.startDate"),
        startDateRequired: microCopy.get("timeRangeFilter.startDateRequired"),
        startDateError: microCopy.get("timeRangeFilter.startDateError"),
        endDate: microCopy.get("timeRangeFilter.endDate"),
        endDateRequired: microCopy.get("timeRangeFilter.endDateRequired"),
        endDateError: microCopy.get("timeRangeFilter.endDateError"),
        sameDateError: microCopy.get("timeRangeFilter.sameDateError"),
        filteredBy: microCopy.get("timeRangeFilter.filteredBy"),
        dateRange: microCopy.get("timeRangeFilter.dateRange"),
        paymentStatus: microCopy.get("timeRangeFilter.paymentStatus"),
        opportunityType: microCopy.get("timeRangeFilter.opportunityType"),
        applyFilters: microCopy.get("timeRangeFilter.applyFilters"),
        range: {
          pastYear: microCopy.get("timeRangeFilter.range.pastYear"),
          pastMonth: microCopy.get("timeRangeFilter.range.pastMonth"),
          thisMonth: microCopy.get("timeRangeFilter.range.thisMonth"),
          allTime: microCopy.get("timeRangeFilter.range.allTime"),
          customDateRange: microCopy.get("timeRangeFilter.range.customDateRange"),
          past90Days: microCopy.get("timeRangeFilter.range.past90Days"),
          yearToDate: microCopy.get("timeRangeFilter.range.yearToDate"),
          past6Months: microCopy.get("timeRangeFilter.range.past6Months")
        },
        status: {
          all: microCopy.get("timeRangeFilter.status.all"),
          paid: microCopy.get("timeRangeFilter.status.paid"),
          pending: microCopy.get("timeRangeFilter.status.pending")
        },
        program: {
          all: microCopy.get("timeRangeFilter.program.all"),
          creatorNetwork: microCopy.get("timeRangeFilter.program.creatorNetwork"),
          affiliate: microCopy.get("timeRangeFilter.program.affiliate"),
          theSims: microCopy.get("timeRangeFilter.program.theSims")
        },
        programCode: microCopy.get("timeRangeFilter.programCode")
      }
    };
  }
}
