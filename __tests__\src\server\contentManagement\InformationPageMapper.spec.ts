import { InformationPageMapper } from "@src/server/contentManagement/InformationPageMapper";

describe("InformationPageMapper", () => {
  const microCopies = {
    "information.messages.firstName": "Creator First Name",
    "information.messages.lastNameTooLong": "Last name is too long",
    "information.messages.state": "State",
    "information.messages.dateOfBirthInvalid": "Date of birth is invalid",
    "information.messages.country": "Country",
    "information.messages.city": "City",
    "information.messages.ageMustBe18OrOlder": "You must be 18 or older to apply",
    "information.messages.street": "Street",
    "information.messages.dateOfBirth": "Date of Birth",
    "information.messages.firstNameTooLong": "First name is too long",
    "information.messages.streetTooLong": "Street is too long",
    "information.messages.stateTooLong": "State is too long",
    "information.messages.zipCode": "Zip Code",
    "information.messages.zipCodeTooLong": "Zip Code is too long",
    "information.messages.cityTooLong": "City is too long",
    "information.messages.lastName": "Last Name",
    "information.messages.preferredNameTooLong": "Preferred name is too long",
    "information.messages.preferredPronoun": "Preferred Pronoun",
    "information.messages.businessName": "Business Name",
    "information.messages.email": "Email",
    "information.labels.state": "State",
    "information.labels.city": "City",
    "information.labels.country": "Country",
    "information.labels.preferredName": "Preferred Name",
    "information.labels.preferredPronouns": "Preferred Pronouns",
    "information.labels.preferredPronoun": "Preferred Pronoun",
    "information.labels.enterPronoun": "Enter Pronoun",
    "information.labels.lastName": "Last Name",
    "information.labels.zipCode": "Zip Code",
    "information.labels.EAID": "EA ID",
    "information.eaEmailID": "EA Email ID",
    "information.labels.firstName": "First Name",
    "information.labels.dateOfBirth": "Date of Birth",
    "information.labels.selectCountry": "Select Country",
    "information.labels.street": "Street",
    "information.interestedUserDescription2": "Interested User Description 2",
    "information.interestedUserDescription1": "Interested User Description 1",
    "information.basicInformation": "Basic Information",
    "information.informationPageTitle": "Information Page Title",
    "information.infoTitle": "Information Title",
    "information.labels.selectProunoun": "Select Pronoun"
  };

  it("maps information page labels", () => {
    const mapper = new InformationPageMapper();
    const labels = mapper.map(microCopies).informationLabels;

    expect(labels.messages.firstName).toBe("Creator First Name");
    expect(labels.messages.lastNameTooLong).toBe("Last name is too long");
    expect(labels.messages.state).toBe("State");
    expect(labels.messages.dateOfBirthInvalid).toBe("Date of birth is invalid");
    expect(labels.messages.country).toBe("Country");
    expect(labels.messages.city).toBe("City");
    expect(labels.messages.ageMustBe18OrOlder).toBe("You must be 18 or older to apply");
    expect(labels.messages.street).toBe("Street");
    expect(labels.messages.dateOfBirth).toBe("Date of Birth");
    expect(labels.messages.firstNameTooLong).toBe("First name is too long");
    expect(labels.messages.streetTooLong).toBe("Street is too long");
    expect(labels.messages.stateTooLong).toBe("State is too long");
    expect(labels.messages.zipCode).toBe("Zip Code");
    expect(labels.messages.zipCodeTooLong).toBe("Zip Code is too long");
    expect(labels.messages.cityTooLong).toBe("City is too long");
    expect(labels.messages.lastName).toBe("Last Name");
    expect(labels.messages.preferredNameTooLong).toBe("Preferred name is too long");
    expect(labels.messages.preferredPronoun).toBe("Preferred Pronoun");
    expect(labels.messages.businessName).toBe("Business Name");
    expect(labels.messages.email).toBe("Email");
    expect(labels.labels.state).toBe("State");
    expect(labels.labels.city).toBe("City");
    expect(labels.labels.country).toBe("Country");
    expect(labels.labels.preferredName).toBe("Preferred Name");
    expect(labels.labels.preferredPronouns).toBe("Preferred Pronouns");
    expect(labels.labels.preferredPronoun).toBe("Preferred Pronoun");
    expect(labels.labels.enterPronoun).toBe("Enter Pronoun");
    expect(labels.labels.lastName).toBe("Last Name");
    expect(labels.labels.zipCode).toBe("Zip Code");
    expect(labels.labels.EAID).toBe("EA ID");
    expect(labels.labels.eaEmailID).toBe("EA Email ID");
    expect(labels.labels.firstName).toBe("First Name");
    expect(labels.labels.dateOfBirth).toBe("Date of Birth");
    expect(labels.labels.selectCountry).toBe("Select Country");
    expect(labels.labels.street).toBe("Street");
    expect(labels.interestedUserDescription2).toBe("Interested User Description 2");
    expect(labels.interestedUserDescription1).toBe("Interested User Description 1");
    expect(labels.basicInformation).toBe("Basic Information");
    expect(labels.informationPageTitle).toBe("Information Page Title");
    expect(labels.infoTitle).toBe("Information Title");
    expect(labels.labels.selectProunoun).toBe("Select Pronoun");
  });

  it("throws error for missing micro copies", () => {
    const mapper = new InformationPageMapper();

    expect(() => mapper.map({})).toThrow("Micro copy with key information.messages.firstName is absent");
  });
});
