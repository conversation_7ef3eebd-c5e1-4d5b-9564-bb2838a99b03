import "reflect-metadata";
import React, { ComponentType, useCallback, useEffect, useState } from "react";
import MigrationLayout from "@components/migrations/migration-layout/MigrationLayout";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { useAppContext } from "@src/context";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import Error from "../_error";
import { useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useRouter } from "next/router";
import { SESSION_USER } from "@src/utils";
import dynamic from "next/dynamic";
import Loading from "@components/loading/Loading";
import { useDependency } from "@src/context/DependencyContext";
import { CommunicationPreferencesPageLabels } from "@src/server/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/server/contentManagement/ConnectAccountsPageMapper";
import { InformationPageLabels } from "@src/server/contentManagement/InformationPageMapper";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import { GetServerSidePropsResult } from "next";
import informationProps from "@src/serverprops/InformationProps";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import verifyIncompleteRegistration from "@src/serverprops/middleware/VerifyIncompleteRegistration";

const OnboardingInformationStep: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "onboarding/OnboardingInformationStep"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type PageLabels = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: BreadcrumbPageLabels &
    CommunicationPreferencesPageLabels &
    CommonPageLabels &
    InformationPageLabels &
    ConnectAccountsPageLabels;
  user: AuthenticatedUserFactory;
  FLAG_COUNTRIES_BY_TYPE: boolean;
  futureCreator: Record<string, unknown>;
  registrationCode: string | null;
};

export default function OnBoardingInformation({
  pageLabels,
  user,
  FLAG_COUNTRIES_BY_TYPE,
  isExistingCreator,
  futureCreator,
  registrationCode
}) {
  const { dispatch, state } = useAppContext();
  const onClose = useCallback(() => setShowConfirmation(true), []);
  const {
    errorHandler,
    metadataClient,
    creatorsClient,
    configuration: { PROGRAM_CODE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();

  const { locale } = useRouter();

  const router = useRouter();
  const { informationLabels, communicationPreferencesPageLabels, commonPageLabels, connectAccountsLabels } = pageLabels;
  const { error: errorToast } = useToast();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const stableDispatch = useCallback(dispatch, [dispatch]);

  const { exceptionCode = null, sessionUser = null, isLoading = false } = state;

  useEffect(() => user && stableDispatch({ type: SESSION_USER, data: user }), [user, stableDispatch]);

  const layout = {
    main: {
      unhandledError: commonPageLabels.unhandledError
    },
    buttons: {
      close: commonPageLabels.close,
      cancel: commonPageLabels.cancel,
      next: commonPageLabels.next
    }
  };
  const infoLabels = {
    interestedCreatorTitle: informationLabels?.interestedCreatorTitle,
    messages: {
      ...(informationLabels.messages as Record<never, never>)
    }
  };
  const translation = {
    messages: {
      ...(communicationPreferencesPageLabels.messages as Record<never, never>)
    }
  };
  const formLabels = {
    ...informationLabels,
    ...(informationLabels.labels as Record<never, never>),
    ...(informationLabels.messages as Record<never, never>),
    ...commonPageLabels,
    ...(communicationPreferencesPageLabels.labels as Record<never, never>),
    connectSocialMediaAccountTitle: connectAccountsLabels.title,
    connectSocialMediaAccountDescription: connectAccountsLabels.description
  };

  const informationPageLabels = {
    ...commonPageLabels,
    ...informationLabels
  };

  const formFields = {
    preferredName: { required: false },
    preferredPronouns: { required: false },
    preferredPronoun: { required: true },
    firstName: { required: true },
    lastName: { required: true },
    dateOfBirth: { required: true },
    country: { required: true },
    street: { required: true },
    city: { required: true },
    state: { required: true },
    zipCode: { required: true },
    defaultGamerTag: { required: true },
    originEmail: { required: true }
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={informationPageLabels.informationPageTitle}
      theSims={commonPageLabels.theSims}
      className="onboarding-creator"
      onClose={onClose}
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        information: commonPageLabels.information,
        contract: commonPageLabels.contract,
        paymentInfo: commonPageLabels.paymentInfo,
        connectAccounts: commonPageLabels.connectedAccounts,
        preferences: commonPageLabels.preferences
      }}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      isLoading={isLoading}
      completed={commonPageLabels.completed}
    >
      <OnboardingInformationStep
        analytics={{ checkedApplicationStatus: () => {} }}
        stableDispatch={stableDispatch}
        state={state}
        labels={{
          layout: layout,
          formLabels: formLabels,
          pageLabels: informationPageLabels,
          translation: translation,
          infoLabels: infoLabels
        }}
        preferredPronounsOptions={[
          { value: "He / Him", label: "He / Him" },
          { value: "She / Her", label: "She / Her" },
          { value: "They / Them", label: "They / Them" },
          { value: "Other (Please specify)", label: "Other (Please specify)" },
          { value: "Prefer not to say", label: "Prefer not to say" }
        ]}
        errorHandling={errorHandler}
        configuration={{
          onBoardingClient: undefined,
          creatorsClient,
          metadataClient,
          formFields,
          navigateToNextPage: "/connect-accounts",
          DEFAULT_AVATAR_IMAGE: DEFAULT_AVATAR_IMAGE
        }}
        onClose={onClose}
        setShowConfirmation={setShowConfirmation}
        showConfirmation={showConfirmation}
        user={user}
        router={router}
        locale={locale}
        errorToast={errorToast}
        FLAG_COUNTRIES_BY_TYPE={FLAG_COUNTRIES_BY_TYPE}
        PROGRAM_CODE={PROGRAM_CODE}
        isExistingCreator={isExistingCreator}
        futureCreator={futureCreator}
        registrationCode={registrationCode}
      />
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();
  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyIncompleteRegistration)
    .use(addLocaleCookie(locale))
    .get(informationProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<PageLabels>;
};
