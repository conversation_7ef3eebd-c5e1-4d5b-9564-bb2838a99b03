import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import PaymentInformationPagePropsController from "@src/serverprops/controllers/PaymentInformationPagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import config from "config";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("../../../../config");
jest.mock("next-i18next/serverSideTranslations", () => ({
  serverSideTranslations: jest.fn().mockResolvedValue({
    _nextI18Next: {
      initialI18nStore: {},
      initialLocale: "en-us",
      userConfig: {}
    }
  })
}));

describe("PaymentInformationPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    req.session = { identity };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {
      transactionHistory: {
        title: "Transaction History"
      }
    };
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const simsLaunchDate = "2024-01-01";
    config.PAYMENTS_DEFAULT_START_DATE = simsLaunchDate;
    const controller = new PaymentInformationPagePropsController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "paymentsPage");
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        PAYMENTS_DEFAULT_START_DATE: simsLaunchDate,
        pageLabels: labels,
        locale: currentLocale,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        _nextI18Next: {
          initialI18nStore: {},
          initialLocale: "en-us",
          userConfig: {}
        }
      }
    });
  });
});
