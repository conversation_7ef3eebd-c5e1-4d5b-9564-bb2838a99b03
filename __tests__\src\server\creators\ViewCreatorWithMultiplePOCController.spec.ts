import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ViewCreatorWithMultiplePOCController from "../../../../src/server/creators/ViewCreatorWithMultiplePOCController";
import CreatorsWithMultiplePOCHttpClient from "../../../../src/server/creators/CreatorsWithMultiplePOCHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { aCreatorWithMultiplePOC } from "__tests__/factories/creators/CreatorWithMultiplePOC";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("ViewCreatorWithMultiplePOCController", () => {
  let controller: ViewCreatorWithMultiplePOCController;

  beforeEach(() => jest.clearAllMocks());

  it("shows a creator with multiple poc", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/v8/creators`,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const creator = aCreatorWithMultiplePOC();
    const creators = { withId: jest.fn().mockResolvedValue(creator) };
    controller = new ViewCreatorWithMultiplePOCController((creators as unknown) as CreatorsWithMultiplePOCHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creator);
    expect(creators.withId).toHaveBeenCalledTimes(1);
    expect(creators.withId).toHaveBeenCalledWith(userId);
  });
});
