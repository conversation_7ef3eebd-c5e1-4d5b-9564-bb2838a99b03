import { memo } from "react";
import { controller, Icon, user } from "@eait-playerexp-cn/core-ui-kit";
import {
  communicationSettings,
  connectedAccounts,
  creatorType,
  legalDocuments,
  paymentInformation
} from "../icons/Profile";
import { useRouter } from "next/router";
import classNames from "classnames/bind";
import { NO_SHALLOW_ROUTING } from "@src/utils";
import PointOfContactDetails from "./PointOfContactDetails";

const iconMap = {
  User: user,
  Controller: controller,
  "Profile/CreatorType": creatorType,
  "Profile/ConnectedAccounts": connectedAccounts,
  "Profile/CommunicationSettings": communicationSettings,
  "Profile/LegalDocuments": legalDocuments,
  "Profile/Payment": paymentInformation
};

const ProfileMenuWeb = ({ profileLabels, pocLabels, creator, analytics, buttons }) => {
  const { pathname, locale, push } = useRouter();

  const onClickHandler = (section) => {
    if (section === "payment-information") analytics.clickedPaymentInformationInMyProfile({ locale });

    const shallow = !NO_SHALLOW_ROUTING.includes(section);
    push({ pathname, query: { section: section } }, undefined, { shallow });
  };

  return (
    <>
      <div className="profile-menu-web-container">
        <h3 className="profile-menu-web-title">{profileLabels?.myProfile}</h3>
        <div className="profile-menu-web-tabs-container">
          <Tab title={profileLabels?.information} icon="User" section="information" onClick={onClickHandler} />
          <Tab
            title={profileLabels?.gamePreferences}
            icon="Controller"
            section="game-preferences"
            onClick={onClickHandler}
          />
          <Tab
            title={profileLabels?.creatorType}
            icon="Profile/CreatorType"
            section="creator-type"
            onClick={onClickHandler}
          />
          <Tab
            title={profileLabels?.connectedAccounts}
            icon="Profile/ConnectedAccounts"
            section="connected-accounts"
            onClick={onClickHandler}
          />
          <Tab
            title={profileLabels?.communicationSettings}
            icon="Profile/CommunicationSettings"
            section="communication-settings"
            onClick={onClickHandler}
          />
          <Tab
            title={profileLabels?.legalDocuments}
            icon="Profile/LegalDocuments"
            section="legal-documents"
            onClick={onClickHandler}
          />
        </div>
        {creator?.hasPointOfContact() && (
          <PointOfContactDetails
            {...{
              pocLabels: pocLabels,
              creator: creator,
              profileLabels: profileLabels,
              pocName: creator.pointOfContactName(),
              analytics,
              buttons,
              pocDiscordTag: creator.pointOfContactDiscordTag(),
              imageUrl: "/img/sims-poc.png",
              showEllipsis: true
            }}
          />
        )}
      </div>
    </>
  );
};

const Tab = memo(function Tab({ title, icon, section, onClick }) {
  const { query } = useRouter();

  return (
    <div
      className="profile-menu-web-tab"
      onClick={() => {
        onClick(section);
      }}
    >
      <Icon icon={iconMap[icon]} className="profile-menu-web-tab-icon" />
      <span
        className={classNames(
          {
            "profile-menu-web-tab-title-selected": query.section ? section === query.section : section === "information"
          },
          "profile-menu-web-tab-title"
        )}
      >
        {title}
      </span>
    </div>
  );
});

export default memo(ProfileMenuWeb);
