import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ViewCreatorProfileController from "../../../../src/server/creators/ViewCreatorProfileController";
import { aCreatorWithPayableStatus } from "../../../factories/creators/CreatorWithPayableStatus";
import CreatorsWithPayableStatusHttpClient from "../../../../src/server/creators/CreatorsWithPayableStatusHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("ViewCreatorProfileController", () => {
  let controller: ViewCreatorProfileController;

  beforeEach(() => jest.clearAllMocks());

  it("shows a creator with payment status", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/v3/creators`,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const creator = aCreatorWithPayableStatus();
    const creators = { withId: jest.fn().mockResolvedValue(creator) };
    controller = new ViewCreatorProfileController((creators as unknown) as CreatorsWithPayableStatusHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creator);
    expect(creators.withId).toHaveBeenCalledTimes(1);
    expect(creators.withId).toHaveBeenCalledWith(userId);
  });
});
