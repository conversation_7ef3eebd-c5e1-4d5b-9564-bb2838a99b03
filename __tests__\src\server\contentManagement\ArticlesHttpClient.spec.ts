import ArticlesHttpClient, { ArticlesCriteria } from "@src/server/contentModal/ArticlesHttpClient";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import Random from "__tests__/factories/Random";
import { AxiosError } from "axios";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";

describe("ArticlesHttpClient", () => {
  const articleResponse = {
    sys: {
      id: Random.uuid(),
      publishedAt: Random.string(),
      firstPublishedAt: Random.string(),
      publishedVersion: Random.number()
    },
    title: "Modeling Your Asset",
    slug: "article-modeling-your-asset",
    body: {
      richText:
        '{"nodeType":"document","data":{},"content":[{"nodeType":"heading-1","data":{},"content":[{"nodeType":"text","value":"Modeling Your Asset","marks":[],"data":{}}]}',
      embeddedItems: []
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("finds article details with page slug", async () => {
    const slug = "article-modeling-your-asset";
    const client = { get: jest.fn().mockReturnValue({ data: articleResponse }) };
    const article = new ArticlesHttpClient((client as unknown) as TraceableHttpClient);
    const criteria: ArticlesCriteria = { locale: Random.locale() };

    const response = await article.matching(slug, criteria);

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v1/articles/${slug}`, { query: { ...criteria, published: true } });
    expect(response).toEqual(articleResponse);
  });

  it("throws Article Not Found exception", async () => {
    const slug = "article-model";
    const error = { response: { status: 404 } } as AxiosError;
    const client = { get: jest.fn().mockRejectedValue(error) };
    const article = new ArticlesHttpClient((client as unknown) as TraceableHttpClient);
    const criteria: ArticlesCriteria = { locale: Random.locale() };

    await expect(article.matching(slug, criteria)).rejects.toThrow(ArticleNotFound);
  });
});
