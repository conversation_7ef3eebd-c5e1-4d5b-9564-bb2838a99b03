import "reflect-metadata";
import { render, screen, waitFor } from "@testing-library/react";
import MyContent from "@src/pages/my-content";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import SubmittedContentService from "@src/services/SubmittedContentService";
import { useDependency } from "@src/context/DependencyContext";
import { mockMatchMedia } from "__tests__/helpers/window";
import { aContentFeedback } from "__tests__/factories/opportunities/ContentFeedback";
import { delay } from "__tests__/helpers/timer";
import { aSubmittedContentWithProgramCode } from "__tests__/factories/opportunities/SubmittedContentWithProgramCode";

jest.mock("next-i18next", () => {
  const t = jest.fn().mockImplementation((str: string) => str);

  return {
    ...jest.requireActual("next-i18next"),
    useTranslation: () => ({
      t,
      i18n: {
        changeLanguage: () => new Promise<void>(() => {})
      }
    })
  };
});

jest.mock("../../../src/services/SubmittedContentService");
jest.mock("../../../src/context/DependencyContext");

describe("MyContent", () => {
  mockMatchMedia();
  const submitContentResponse = {
    contents: [
      {
        id: "De73_632qKs",
        name: "Test submit content",
        type: "YOUTUBE",
        thumbnail: "https://i.ytimg.com/vi/De73_632qKs/default.jpg",
        status: null,
        submittedOn: 1650560236000,
        contentUri: "https://www.youtube.com/watch?v=De73_632qKs",
        contentType: "video",
        submittedDate: 1650585637000,
        opportunityName: "Apex Legends",
        opportunityId: "a0MK000000ACbyvMAD",
        reviewFinalRemark: {
          content: "Review comment",
          author: "Reviewer",
          date: 1650672037000
        },
        requiresChanges: () => false,
        formattedSubmittedDate: () => "Apr 22, 2022 (IST)",
        formattedReviewFinalRemarkDate: () => "Apr 23, 2022 (IST)"
      }
    ],
    count: 1,
    total: 1
  };

  const router = {
    route: "/",
    locale: "en-us",
    isReady: true,
    push: jest.fn().mockResolvedValue(true)
  };

  const contentFeedback = {
    ...aContentFeedback(),
    requiresChanges: () => true,
    formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
  };

  const content = aSubmittedContentWithProgramCode({
    name: "Feedback provided for submitted content",
    status: "CHANGE_REQUESTED",
    id: "beta456",
    requiresChanges: () => true,
    formattedSubmittedDate: () => "Apr 22, 2022 (IST)",
    formattedReviewFinalRemarkDate: () => "Apr 23, 2022 (IST)"
  });

  const contentWithChangesRequested = {
    ...submitContentResponse,
    contents: [content]
  };

  const myContentProps = {
    user: {},
    locale: ""
  };

  beforeEach(() => {
    jest.clearAllMocks();
    addAPICallsTo();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      },
      errorHandler: jest.fn()
    });
  });

  function addAPICallsTo(): void {
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({
        data: contentWithChangesRequested
      }),
      getContentsFeedback: jest.fn().mockImplementation(async () =>
        delay(1000).then(() =>
          Promise.resolve({
            data: { contentsFeedback: [contentFeedback] }
          })
        )
      )
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);
  }

  it("displays my content title and its description", async () => {
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({ data: submitContentResponse })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: "myContent" })).toBeInTheDocument();
      expect(screen.getByText(/dashboard:myContentDescription/i)).toBeInTheDocument();
      expect(screen.getByText("Test submit content")).toBeInTheDocument();
    });
  });

  /**
   * This test is for validating the status `change requested`(feedback has been provided) as per latest figma design
   */
  it("displays content with change requested", async () => {
    const content = aSubmittedContentWithProgramCode({
      name: "Feedback provided for submitted content",
      status: "CHANGE_REQUESTED",
      id: "beta456",
      requiresChanges: () => true, // since status = "CHANGE_REQUESTED"
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const contentWithChangesRequested = {
      ...submitContentResponse,
      contents: [content]
    };
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({ data: contentWithChangesRequested })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByText(/^changesRequired/i)).toBeInTheDocument();
      /** Unread icon should not be shown here */
      expect(screen.queryByText("content-card-beta456-unread-icon")).not.toBeInTheDocument();
    });
  });

  /**
   * This test is for validating the status `change received`(updated content based on feedback received) as per latest figma design
   */
  it("displays content with change received", async () => {
    const content = aSubmittedContentWithProgramCode({
      name: "Updated content based on feedback received",
      status: "CHANGE_RECEIVED",
      id: "beta456",
      requiresChanges: () => false,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const contentWithChangesReceived = {
      ...submitContentResponse,
      contents: [content]
    };
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({ data: contentWithChangesReceived })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByText("Updated content based on feedback received")).toBeInTheDocument();
      expect(screen.getByText(/inReview$/i)).toBeInTheDocument();
      /** Unread icon should not be shown here */
      expect(screen.queryByText("content-card-beta456-unread-icon")).not.toBeInTheDocument();
    });
  });

  it("displays submitted content card", async () => {
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({ data: submitContentResponse })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} />);

    await screen.findByText("Test submit content");
  });

  it("displays content with change requested", async () => {
    const router = {
      locale: "ja-jp",
      push: jest.fn(),
      isReady: true
    };
    (useRouter as jest.Mock).mockImplementation(() => router);
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({ data: submitContentResponse })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} />);
    const opportunity = await screen.findByText("Apex Legends");

    await userEvent.click(opportunity);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/a0MK000000ACbyvMAD"));
  });

  it("shows skeleton loader while feedback is being retrieved", async () => {
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({
        data: contentWithChangesRequested
      }),
      getContentsFeedback: jest.fn().mockImplementation(async () =>
        delay(1000).then(() =>
          Promise.resolve({
            data: { contentsFeedback: [contentFeedback] }
          })
        )
      )
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} locale={"en-us"} />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(() => {
      expect(service.getContentsFeedback).toHaveBeenCalledTimes(1);
    });
  });

  it("shows feedback and verify the contents for website", async () => {
    const { t } = useTranslation();
    const websiteContent = aSubmittedContentWithProgramCode({
      name: "Feedback provided for submitted content",
      status: "CHANGE_REQUESTED",
      type: "WEBSITE",
      id: "beta456",
      requiresChanges: () => true,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const websiteContentWithChangesRequested = {
      ...submitContentResponse,
      contents: [websiteContent]
    };
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({
        data: websiteContentWithChangesRequested
      }),
      getContentsFeedback: jest.fn().mockImplementation(async () =>
        delay(1000).then(() =>
          Promise.resolve({
            data: { contentsFeedback: [contentFeedback] }
          })
        )
      )
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} locale={"en-us"} />);

    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(() => {
      expect(screen.getByText(contentFeedback.description)).toBeInTheDocument();
      expect(screen.getByText(/common:additionalDescription/)).toBeInTheDocument();
      expect(t).toHaveBeenLastCalledWith("common:additionalDescription", {
        contentType: "url",
        updateType: "update"
      });
      expect(screen.getByRole("button", { name: /hideChangesRequired/i })).toBeInTheDocument();
      expect(service.getSubmittedContentsWithProgramCode).toHaveBeenCalledTimes(1);
      expect(service.getContentsFeedback).toHaveBeenCalledTimes(1);
    });
  });

  it("shows feedback and verify the contents for file", async () => {
    const { t } = useTranslation();
    const fileContent = aSubmittedContentWithProgramCode({
      name: "Feedback provided for submitted content",
      status: "CHANGE_REQUESTED",
      type: "FILE",
      sourceType: "IN_GAME_LISTING",
      id: "beta456",
      requiresChanges: () => true,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)"
    });
    const fileContentWithChangesRequested = {
      ...submitContentResponse,
      contents: [fileContent]
    };
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({
        data: fileContentWithChangesRequested
      }),
      getContentsFeedback: jest.fn().mockImplementation(async () =>
        delay(1000).then(() =>
          Promise.resolve({
            data: { contentsFeedback: [contentFeedback] }
          })
        )
      )
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} locale={"en-us"} />);
    await waitFor(() => {
      expect(screen.getByText("Feedback provided for submitted content")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewChangesRequired/i })).toBeInTheDocument();
    });

    await userEvent.click(await screen.findByRole("button", { name: /viewChangesRequired/i }));

    await waitFor(() => {
      expect(screen.getByText(contentFeedback.description)).toBeInTheDocument();
      expect(screen.getByText(/common:additionalDescription/)).toBeInTheDocument();
      expect(t).toHaveBeenLastCalledWith("common:additionalDescription", {
        contentType: "file",
        updateType: "upload"
      });
      expect(screen.getByRole("button", { name: /hideChangesRequired/i })).toBeInTheDocument();
      expect(service.getSubmittedContentsWithProgramCode).toHaveBeenCalledTimes(1);
      expect(service.getContentsFeedback).toHaveBeenCalledTimes(1);
    });
  });

  it("displays review final remark with formatted date for rejected content", async () => {
    const content = aSubmittedContentWithProgramCode({
      name: "Content with review remarks",
      status: "REJECTED",
      id: "beta789",
      requiresChanges: () => false,
      formattedSubmittedDate: () => "Apr 22, 2022 (IST)",
      reviewFinalRemark: {
        content: "Review comment",
        author: "Reviewer",
        date: 1650672037000
      },
      formattedReviewFinalRemarkDate: () => "Apr 23, 2022 (IST)"
    });
    const contentWithReviewRemark = {
      ...submitContentResponse,
      contents: [content]
    };
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({
        data: contentWithReviewRemark
      })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} locale={"en-us"} />);

    await waitFor(() => {
      expect(screen.getByText("Content with review remarks")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /viewDetails/i })).toBeInTheDocument();
    });

    await userEvent.click(screen.getByRole("button", { name: /viewDetails/i }));

    await waitFor(() => {
      expect(screen.getByText(/Apr 23, 2022 \(IST\)/)).toBeInTheDocument();
    });
  });

  it("replaces content type to image for carousel albums", async () => {
    // we are checking the logic inside the class contructor
    const data = {
      ...submitContentResponse,
      contents: [{ ...submitContentResponse.contents[0], contentType: "image", type: "INSTAGRAM" }]
    };
    const service = ({
      getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue({
        data
      })
    } as unknown) as SubmittedContentService;
    (SubmittedContentService as jest.Mock).mockReturnValue(service);

    render(<MyContent {...myContentProps} />);

    await waitFor(() => {
      expect(screen.getByText(/image/i)).toBeInTheDocument();
      expect(screen.getByTestId("content-submission-icon-image")).toBeInTheDocument();
    });
  });
});
