import OpportunityService, { OpportunityWithProgramCode } from "@src/services/OpportunityService";
import { anOpportunityWithProgramCode } from "__tests__/factories/opportunities/OpportunityWithPerks";
import { aParticipationStatus } from "__tests__/factories/opportunities/ParticipationStatus";
import { LocalizedDate, LocalizedDateRange } from "@eait-playerexp-cn/client-kernel";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

jest.mock("@eait-playerexp-cn/http-client");

describe("OpportunityService", () => {
  beforeEach(() => jest.clearAllMocks());

  it("declines a given invitation with the correct invitation ID", async () => {
    const invitationId = "inv-123";
    const client = ({
      post: jest.fn().mockReturnValue({ data: {} })
    } as unknown) as TraceableHttpClient;
    const service = new OpportunityService(client);

    await service.declineInvitation(invitationId);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith(`/api/declined-invitations/${invitationId}`);
  });

  it("finds an opportunity matching a given criteria", async () => {
    const criteria = { page: 1, size: 10, status: "JOINED" };
    const client = ({
      get: jest.fn().mockResolvedValue({ data: { opportunities: [anOpportunityWithProgramCode()] } })
    } as unknown) as TraceableHttpClient;
    const service = new OpportunityService(client);

    await service.matchingWithProgramDetails(criteria);

    expect(client.get).toHaveBeenCalledWith("/api/v4/dashboard", {
      query: criteria
    });
  });

  it("gets a participation status with submission information with correct opportunity IDs", async () => {
    const opportunityIds = ["opp-123", "opp-456"];
    const client = ({
      post: jest.fn().mockReturnValue({ data: { participationStatuses: [aParticipationStatus()] } })
    } as unknown) as TraceableHttpClient;
    const service = new OpportunityService(client);

    await service.getParticipationStatusWithSubmissionInformation(opportunityIds);

    expect(client.post).toHaveBeenCalledWith("/api/opportunities/participation-status", {
      body: { opportunityIds }
    });
  });

  describe("OpportunityWithProgramCode class", () => {
    const mockDateTime = () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);
      return futureDate.getTime();
    };

    const pastDateTime = () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 7);
      return pastDate.getTime();
    };

    it("initializes with the correct properties", () => {
      const mockOpp = anOpportunityWithProgramCode({
        id: "opp-123",
        title: "Test Opportunity",
        gameTitle: "Test Game",
        hasEvent: false,
        hasGameCodes: false,
        hasDiscordChannel: false,
        hasDeliverables: false,
        registrationPeriod: {
          startDate: pastDateTime(),
          endDate: mockDateTime(),
          timeZone: "America/Los_Angeles"
        }
      });

      const opportunity = new OpportunityWithProgramCode(mockOpp);

      expect(opportunity.id).toBe("opp-123");
      expect(opportunity.title).toBe("Test Opportunity");
      expect(opportunity.gameTitle).toBe("Test Game");
      expect(opportunity.hasEvent).toBe(false);
      expect(opportunity.hasGameCodes).toBe(false);
      expect(opportunity.hasDeliverables).toBe(false);
      expect(opportunity.registrationPeriod).toBeInstanceOf(LocalizedDateRange);
    });

    it("returns correct codes availability", () => {
      const mockOpp = anOpportunityWithProgramCode({
        hasGameCodes: true,
        gameCodes: {
          availability: [
            {
              platform: {
                label: "Xbox",
                imageAsIcon: "xbox.png"
              }
            },
            {
              platform: {
                label: "PlayStation",
                imageAsIcon: "ps.png"
              }
            }
          ]
        }
      });

      const opportunity = new OpportunityWithProgramCode(mockOpp);

      expect(opportunity.codesAvailability()).toHaveLength(2);
      expect(opportunity.codesAvailability()[0].label).toBe("PlayStation");
      expect(opportunity.codesAvailability()[1].label).toBe("Xbox");
    });

    it("correctly returns settings based on opportunity features", () => {
      const opportunitiesLabels = {
        remote: "Remote Event",
        inPersonEvent: "In-Person Event",
        gameCode: "Game Code",
        contentSubmission: "Content Submission"
      };
      const mockOpp = anOpportunityWithProgramCode({
        hasGameCodes: true,
        hasDeliverables: true
      });

      const opportunity = new OpportunityWithProgramCode(mockOpp);

      expect(opportunity.settings(opportunitiesLabels)).toContain("Game Code");
      expect(opportunity.settings(opportunitiesLabels)).toContain("Content Submission");
    });

    it("correctly formats settingKeyDates with all date types", () => {
      const futureEpoch = new Date().getTime() + 604800000;
      const pastEpoch = new Date().getTime() - 604800000;
      const mockOpp = anOpportunityWithProgramCode({
        id: "opp-123",
        hasDeliverables: true,
        hasEvent: true,
        type: "support_a_creator",
        event: {
          type: "Remote Event",
          eventPeriod: {
            startDate: futureEpoch,
            endDate: futureEpoch + 3600000,
            timeZone: "America/Los_Angeles"
          }
        },
        contentSubmission: {
          submissionWindow: {
            startDate: futureEpoch - 86400000,
            endDate: futureEpoch + 86400000,
            timeZone: "America/Los_Angeles"
          }
        },
        registrationPeriod: {
          startDate: pastEpoch,
          endDate: futureEpoch,
          timeZone: "America/Los_Angeles"
        },
        creatorCodeActivationWindow: {
          startDate: pastEpoch,
          endDate: futureEpoch,
          timeZone: "America/Los_Angeles"
        }
      });

      const opportunity = new OpportunityWithProgramCode(mockOpp);

      const activationWindow = new LocalizedDateRange(
        new LocalizedDate(pastEpoch),
        new LocalizedDate(futureEpoch),
        "America/Los_Angeles"
      );
      const opportunityKeyDatesLabels = {
        registrationCloses: "Registration Closes",
        onlineEventStartTime: "Online Event Start",
        inPersonStartTime: "In-Person Event Start",
        onlineEventEndTime: "Online Event End",
        inPersonEndTime: "In-Person Event End",
        contentSubmissionDeadline: "Content Submission Deadline",
        codeActivationStartTime: "Code Activation Start",
        codeActivationEndTime: "Code Activation End"
      };
      const keyDates = opportunity.settingKeyDates(opportunityKeyDatesLabels, "en-US", activationWindow);

      expect(keyDates.length).toBe(6);
    });
  });
});
