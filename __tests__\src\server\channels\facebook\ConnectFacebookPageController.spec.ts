import "reflect-metadata";
import ConnectFacebookPageController from "@src/server/channels/facebook/ConnectFacebookPageController";
import { createMocks, MockResponse } from "node-mocks-http";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import { NextApiResponse } from "next";
import Random from "../../../../factories/Random";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";

describe("ConnectFacebookPageController", () => {
  let controller: ConnectFacebookPageController;
  let connectedAccounts;
  const session = {
    save: jest.fn(),
    identity: Identity.fromStored({
      type: "INTERESTED_CREATOR"
    } as StoredIdentity)
  };

  beforeEach(() => {
    jest.clearAllMocks();
    connectedAccounts = ({ connectFacebookPage: jest.fn() } as unknown) as ConnectedAccountsHttpClient;
    controller = new ConnectFacebookPageController(connectedAccounts);
  });

  it("connects a Facebook page for an interested creator", async () => {
    const credentials = {
      nucleusId: Random.nucleusId(),
      pageAccessToken: Random.accessToken(),
      pageId: "a057717c-01f2-49de-9422-289b06649809",
      creatorId: null
    };

    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/facebook-channels",
      body: credentials,
      session: { ...session, nucleusId: credentials.nucleusId }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.fbPages).toBeUndefined();
    expect(req.session.accountType).toEqual("Facebook");
    expect(req.session.save).toHaveBeenCalledTimes(2);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
  });

  it("connects a Facebook page for a creator", async () => {
    const credentials = {
      creatorId: Random.uuid(),
      pageAccessToken: Random.accessToken(),
      pageId: Random.uuid(),
      nucleusId: undefined
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/facebook-channels",
      body: credentials,
      session: {
        ...session,
        identity: Identity.fromStored({
          type: "CREATOR",
          id: credentials.creatorId
        } as StoredIdentity)
      }
    });

    await controller.handle((req as unknown) as NextApiRequestWithSession, (res as unknown) as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.fbPages).toBeUndefined();
    expect(req.session.accountType).toEqual("Facebook");
    expect(req.session.save).toHaveBeenCalledTimes(2);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
  });
});
