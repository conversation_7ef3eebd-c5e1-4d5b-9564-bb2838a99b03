import { render, screen, waitFor } from "@testing-library/react";
import { axe } from "jest-axe";
import React, { useState } from "react";
import userEvent from "@testing-library/user-event";
import UploadContentSubmissionForm, {
  UploadContentSubmissionFormProps
} from "@components/pages/content-submission/UploadContentSubmissionForm";
import { submitFileContentTranslations, submitWebsiteContentTranslations } from "../../../translations";
import axios from "axios";
import MockAdapter from "axios-mock-adapter";
import { renderWithToast } from "../../../helpers/toast";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/services/OpportunityService";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityWithPerks";
import { selectOption } from "../../../helpers/forms";
import { aSubmittedContent } from "../../../factories/opportunities/SubmittedContent";
import Random from "../../../factories/Random";
import SubmittedContentService from "@src/services/SubmittedContentService";
import { useDependency } from "@src/context/DependencyContext";
import { fileTypeFromBlob } from "file-type";
import { ContentDetails } from "@components/pages/content-submission/ContentDeliverablesTab";

jest.mock("../../../../src/services/SubmittedContentService");
jest.mock("../../../../src/services/OpportunityService");
jest.mock("../../../../src/context/DependencyContext");
jest.mock("file-type", () => ({
  fileTypeFromBlob: jest.fn().mockResolvedValue({ ext: "png", mime: "image/png" })
}));

describe("UploadContentSubmissionForm", () => {
  const setUpdatedContent = jest.fn();
  const cancelUploadProgress = jest.fn();
  const resetUploadForm = jest.fn();
  const uploadContentSubmissionFormProps: UploadContentSubmissionFormProps = {
    ...submitWebsiteContentTranslations,
    setUpdatedContent: setUpdatedContent,
    contentTypes: [
      { name: "", label: "Please select", value: "" },
      { name: "VIDEO", label: "video", value: "video" },
      { name: "IMAGE", label: "image", value: "image" },
      { name: "TEXT", label: "text", value: "text" }
    ],
    participationId: Random.uuid(),
    thumbnail: Random.url(),
    cancelUploadProgress,
    resetUploadForm,
    contentFormat: "FILE",
    nucleusId: 123,
    buttonsLabels: submitFileContentTranslations.buttonsLabels,
    contentSubmissionLabels: {
      ...submitWebsiteContentTranslations.contentSubmissionLabels,
      ...submitFileContentTranslations.contentSubmissionLabels
    },
    formLabels: { ...submitWebsiteContentTranslations.formLabels, ...submitFileContentTranslations },
    fileTypes: [
      {
        type: "video",
        extensions: ["MOV"]
      },
      {
        type: "image",
        extensions: ["PNG", "JPG"]
      },
      {
        type: "text",
        extensions: ["TXT"]
      }
    ],
    source: axios.CancelToken,
    analytics: ({} as unknown) as BrowserAnalytics,
    locale: "en-us",
    opportunity: new OpportunityWithDeliverables(aOpportunityWithPerksAndContentSubmissionWithDeliverables()),
    showProgressBar: false,
    setShowProgressBar: jest.fn(),
    uploadPercentage: null,
    setUploadPercentage: jest.fn(),
    deliverableId: Random.uuid(),
    creatorId: Random.uuid()
  };
  const submittedContentService = {
    getSignedUrl: jest.fn(),
    validateContent: jest.fn(),
    markUploadComplete: jest.fn()
  };
  const deliverableTitle = Random.string();
  const deliverableType = "SINGLE";
  const analytics = ({
    submittedFileUpload: jest.fn()
  } as unknown) as BrowserAnalytics;
  const fileId = Random.uuid();
  const signedUrl =
    "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/643344f9-1539-4a44-8a6c-73ccccaf5663/title.png?X-Amz-Signature=yiybkygh8798";
  const signedResponse = { id: fileId, url: signedUrl };
  const versionId = "xYyAqyOfxFHIfriclwjMIygdTBtel8aD";
  const participationId = uploadContentSubmissionFormProps.participationId;
  const fileName = "title.png";
  let uploadedContent = {
    contentType: "image",
    fileName,
    id: fileId,
    participationId,
    thumbnail: uploadContentSubmissionFormProps.thumbnail,
    title: "Title",
    versionId,
    deliverableId: uploadContentSubmissionFormProps.deliverableId
  };
  const content = JSON.stringify([{ name: "test" }]);
  const blob = new Blob([content]);
  const file = new File([blob], "values.png", {
    type: ".png"
  });
  File.prototype.text = jest.fn().mockResolvedValueOnce(content);
  const selectedFile = file;

  let mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mock = new MockAdapter(axios);
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({ data: signedResponse });
    mock.onPut(signedUrl, selectedFile).reply(200, null, { "x-amz-version-id": versionId });
    (useDependency as jest.Mock).mockReturnValue({
      errorHandler: jest.fn(),
      configuration: {
        PROGRAM_CODE: "sims_creator_program"
      }
    });
    (SubmittedContentService as jest.Mock).mockImplementation(() => submittedContentService);
  });

  it("shows form elements to submit file content", async () => {
    render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);

    expect(screen.getByText(uploadContentSubmissionFormProps.formLabels.contentTitle)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.formLabels.contentType)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.formLabels.contentDescription)).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText(uploadContentSubmissionFormProps.formLabels.contentDescriptionPlaceholder)
    ).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.formLabels.maxcharacterLimit)).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText(uploadContentSubmissionFormProps.formLabels.contentTitlePlaceholder)
    ).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.formLabels.fileUpload)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.formLabels.noFileChoosen)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.buttonsLabels.cancel)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.buttonsLabels.upload)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionFormProps.buttonsLabels.upload)).toBeDisabled();
  });

  it("validates file content form", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    const { contentTitleInput } = await fillForm({
      contentTitle: "Title",
      contentType: "video",
      contentDescription: "new text",
      container
    });
    // Select an invalid option
    await selectOption({ option: "Please select", container, label: "Content Type" });

    // Clear title
    await userEvent.clear(contentTitleInput);
    await userEvent.clear(
      screen.getByPlaceholderText(uploadContentSubmissionFormProps.formLabels.contentDescriptionPlaceholder)
    );

    expect(await screen.findByText(uploadContentSubmissionFormProps.buttonsLabels.upload)).toBeDisabled();
    expect(screen.getByText("Content Title is required")).toBeInTheDocument();
    expect(screen.getByText("Content Type is required")).toBeInTheDocument();
    expect(screen.getByText("Content Description is required")).toBeInTheDocument();
  });

  it("enable 'Choose File' when user select content type", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    expect(screen.getByTestId("upload")).toBeDisabled();

    await fillForm({
      contentTitle: "Title",
      contentType: "video",
      contentDescription: "new text",
      container
    });

    expect(await screen.findByTestId("upload")).toBeEnabled();
  });

  it("disable 'Choose File' when user unselect content type", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    await fillForm({
      contentTitle: "Title",
      contentType: "video",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();

    await selectOption({ option: "Please select", container, label: "Content Type" });

    expect(await screen.findByTestId("upload")).toBeDisabled();
  });

  it("enables 'Upload' button when a creator enters mandatory fields", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "text",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    expect(screen.getByRole("button", { name: "Upload" })).toBeDisabled();
    const file = new File(["content"], "values.txt", { type: "text/plain" });

    await userEvent.upload(screen.getByTestId("upload"), file);

    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
  });

  it("disables 'Upload' button when a creator doesn't fill title field", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: " ",
      contentType: "text",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    const str = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([str]);
    const file = new File([blob], "values.txt", {
      type: ".txt"
    });
    File.prototype.text = jest.fn().mockResolvedValueOnce(str);

    await userEvent.upload(screen.getByTestId("upload"), file);

    expect(await screen.findByRole("button", { name: "Upload" })).toBeDisabled();
  });

  it("shows progress bar and success toast message on submitting uploaded file", async () => {
    const mock = new MockAdapter(axios);
    const url =
      "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/643344f9-1539-4a44-8a6c-73ccccaf5663/title.png?X-Amz-Signature=52af22ef94f77ae4ef40f48a78c53165962cf4ff802c74e942cc418956980a50";
    const signedResponse = { id: "643344f9-1539-4a44-8a6c-73ccccaf5663", url };
    const versionId = "xYyAqyOfxFHIfriclwjMIygdTBtel8aD";
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", {
      type: ".png"
    });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({
      data: signedResponse
    });
    mock.onPut(url, selectedFile).reply(200, null, { "x-amz-version-id": versionId });
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "image",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(await screen.findByRole("progressbar", { name: /upload file progress/i })).toHaveClass(
      "content-submission-file-progress"
    );
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.title)
    ).toBeInTheDocument();
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.content)
    ).toBeInTheDocument();
    expect(submittedContentService.getSignedUrl).toHaveBeenCalledTimes(1);
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledTimes(1);
    expect(mock.history.put).toHaveLength(1);
    expect(resetUploadForm).toHaveBeenCalled();
    expect(analytics.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(analytics.submittedFileUpload).toHaveBeenCalledWith({
      contentType: "image",
      fileExtension: "png",
      fileSize: 17,
      locale: uploadContentSubmissionFormProps.locale,
      opportunity: uploadContentSubmissionFormProps.opportunity
    });
    unmount(); // remove toast
  });

  it("shows error toast when file upload fails", async () => {
    const mock = new MockAdapter(axios);
    const url =
      "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/643344f9-1539-4a44-8a6c-73ccccaf5663/title.png?X-Amz-Signature=52af22ef94f77ae4ef40f48a78c53165962cf4ff802c74e942cc418956980a50";
    const versionId = "xYyAqyOfxFHIfriclwjMIygdTBtel8aD";
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", {
      type: ".png"
    });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockRejectedValue(new Error("Upload failed"));
    mock.onPut(url, selectedFile).reply(200, null, { "x-amz-version-id": versionId });
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "image",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(submittedContentService.getSignedUrl).toHaveBeenCalledTimes(1);
    expect(
      await screen.findByText(uploadContentSubmissionFormProps.contentSubmissionLabels.error.title)
    ).toBeInTheDocument();
    expect(
      await screen.findByText(uploadContentSubmissionFormProps.contentSubmissionLabels.error.content)
    ).toBeInTheDocument();
    unmount();
  });

  it("successfully submitted the file when title has emojis and special characters", async () => {
    const mock = new MockAdapter(axios);
    const url =
      "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/643344f9-1539-4a44-8a6c-73ccccaf5663/title.png?X-Amz-Signature=52af22ef94f77ae4ef40f48a78c53165962cf4ff802c74e942cc418956980a50";
    const signedResponse = { id: "643344f9-1539-4a44-8a6c-73ccccaf5663", url };
    const versionId = "xYyAqyOfxFHIfriclwjMIygdTBtel8aD";
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", {
      type: ".png"
    });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({
      data: signedResponse
    });
    mock.onPut(url, selectedFile).reply(200, null, { "x-amz-version-id": versionId });
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "test😀😃#@😄$👋^🤚🖐✋&*",
      contentType: "image",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(await screen.findByRole("progressbar", { name: /upload file progress/i })).toHaveClass(
      "content-submission-file-progress"
    );
    expect(setUpdatedContent).toHaveBeenCalledWith(true);
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.title)
    ).toBeInTheDocument();
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.content)
    ).toBeInTheDocument();
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledTimes(1);
    expect(mock.history.put).toHaveLength(1);
    expect(resetUploadForm).toHaveBeenCalled();
    expect(analytics.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(analytics.submittedFileUpload).toHaveBeenCalledWith({
      contentType: "image",
      fileExtension: "png",
      fileSize: 17,
      locale: uploadContentSubmissionFormProps.locale,
      opportunity: uploadContentSubmissionFormProps.opportunity
    });
    unmount(); // remove toast
  });

  it("shows prefilled values of form when update file is enabled", async () => {
    const content = (aSubmittedContent({
      contentTitle: "Test prefill values",
      contentType: "image",
      contentUri:
        "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/7cf84fba-cbec-461d-8a94-caa4808d0deb/test-content.svg?versionId=OcrNFDinICS_YSOOi2ghuLdQQyyrPNAL&X-Amz-Algorithm=AWS-ALGO&X-Amz-Date=20230111T122429Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Credential=CREDS&X-Amz-Signature=abcdefghijklmnopqrstuvwxyz123",
      contentTypeLabel: "Image",
      contentDescription: "Test description"
    }) as unknown) as ContentDetails;

    render(
      <UploadContentSubmissionForm {...uploadContentSubmissionFormProps} content={content} isForUpdateUploadedFile />
    );

    expect(await screen.findByLabelText(/^Content Title/)).toHaveValue(content.contentTitle);
    expect(await screen.findByTestId("content-type")).toHaveTextContent(content.contentType);
  });

  it("shows error message when selected file is invalid type", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "video",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    const file = new File(["content"], "values.txt", { type: "text/plain" });

    await userEvent.upload(screen.getByTestId("upload"), file);

    expect(
      await screen.findByText(uploadContentSubmissionFormProps.contentSubmissionLabels.invalidFileTypeMessage)
    ).toBeInTheDocument();
  });

  it("shows error message when selected file size exceeds 500 MB", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "text",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    const str = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([str]);
    const file = new File([blob], "values.txt", {
      type: ".txt"
    });
    Object.defineProperty(file, "size", { value: 5242880000 });
    File.prototype.text = jest.fn().mockResolvedValueOnce(str);

    await userEvent.upload(screen.getByTestId("upload"), file);

    expect(
      await screen.findByText(uploadContentSubmissionFormProps.contentSubmissionLabels.maxLimitMessage)
    ).toBeInTheDocument();
  });

  it("removes selected file when clicking on delete button", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    await fillForm({
      contentTitle: "Title",
      contentType: "text",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    const str = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([str]);
    const file = new File([blob], "values.txt", {
      type: ".txt"
    });
    File.prototype.text = jest.fn().mockResolvedValueOnce(str);
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.getByText(file.name)).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: /Remove selected file/i }));

    expect(await screen.findByRole("button", { name: "Upload" })).toBeDisabled();
    expect(screen.queryByText(file.name)).not.toBeInTheDocument();
  });

  it("validate submit button when user enter empty space in title", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    const { contentTitleInput } = await fillForm({
      contentTitle: "Title",
      contentType: "text",
      contentDescription: "new text",
      container
    });
    await userEvent.clear(contentTitleInput);

    await userEvent.type(contentTitleInput, " ");

    expect(await screen.findByText(uploadContentSubmissionFormProps.buttonsLabels.upload)).toBeDisabled();
  });

  it("initially shows file types set in the opportunity settings", async () => {
    render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);

    expect(
      await screen.findByText("Accepted File Formats: MOV, PNG, JPG, TXT. Max File Size : 500 MB")
    ).toBeInTheDocument();
  });

  it("submits file content", async () => {
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({
      data: signedResponse
    });
    (fileTypeFromBlob as jest.Mock).mockResolvedValue({ mime: "image/png" });
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
          deliverableTitle={deliverableTitle}
          deliverableType={deliverableType}
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "image",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(await screen.findByRole("progressbar", { name: /upload file progress/i })).toHaveClass(
      "content-submission-file-progress"
    );
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.title)
    ).toBeInTheDocument();
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.content)
    ).toBeInTheDocument();
    expect(submittedContentService.markUploadComplete).toBeCalledTimes(1);
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledWith(signedResponse.id);
    expect(resetUploadForm).toHaveBeenCalled();
    expect(analytics.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(analytics.submittedFileUpload).toHaveBeenCalledWith({
      contentType: "image",
      fileExtension: "png",
      fileSize: 17,
      locale: uploadContentSubmissionFormProps.locale,
      opportunity: uploadContentSubmissionFormProps.opportunity,
      deliverableTitle,
      deliverableType
    });
    unmount(); // remove toast
  });

  it("initially shows file types set in the opportunity settings", async () => {
    render(
      <UploadContentSubmissionForm
        {...uploadContentSubmissionFormProps}
        contentTypes={[
          ...uploadContentSubmissionFormProps.contentTypes,
          ...[{ name: "ZIP", label: "zip", value: "zip" }]
        ]}
        fileTypes={[
          ...uploadContentSubmissionFormProps.fileTypes,
          ...[
            {
              type: "zip",
              extensions: ["ZIP"]
            }
          ]
        ]}
      />
    );

    expect(
      await screen.findByText("Accepted File Formats: MOV, PNG, JPG, TXT, ZIP. Max File Size : 500 MB")
    ).toBeInTheDocument();
  });

  it("sucessfully submits zip file content", async () => {
    const fileName = "title.zip";
    uploadedContent = {
      ...uploadedContent,
      fileName: fileName,
      contentType: "zip"
    };
    const file = new File([blob], "values.zip", {
      type: ".zip"
    });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    mock.onPut(signedUrl, selectedFile).reply(200, null, { "x-amz-version-id": versionId });
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    (fileTypeFromBlob as jest.Mock).mockResolvedValue({ mime: "application/zip" });
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
          deliverableTitle={deliverableTitle}
          deliverableType={deliverableType}
          fileTypes={[
            {
              type: "zip",
              extensions: ["ZIP"]
            },
            {
              type: "text",
              extensions: ["DOC"]
            }
          ]}
          contentTypes={[{ name: "ZIP", label: "zip", value: "zip" }]}
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "zip",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(await screen.findByRole("progressbar", { name: /upload file progress/i })).toHaveClass(
      "content-submission-file-progress"
    );
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.title)
    ).toBeInTheDocument();
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.content)
    ).toBeInTheDocument();
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledTimes(1);
    expect(mock.history.put).toHaveLength(1);
    expect(mock.history.put[0].url).toBe(signedUrl);
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledWith(signedResponse.id);
    expect(resetUploadForm).toHaveBeenCalled();
    expect(analytics.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(analytics.submittedFileUpload).toHaveBeenCalledWith({
      contentType: "zip",
      fileExtension: "zip",
      fileSize: 17,
      locale: uploadContentSubmissionFormProps.locale,
      opportunity: uploadContentSubmissionFormProps.opportunity,
      deliverableTitle,
      deliverableType
    });
    unmount(); // remove toast
  });

  it("updates file requirements message after selecting a different content type", async () => {
    const content = (aSubmittedContent({
      type: null,
      contentTitle: "Test prefill values",
      contentType: "video",
      contentUri:
        "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/7cf84fba-cbec-461d-8a94-caa4808d0deb/test-content.svg?versionId=OcrNFDinICS_YSOOi2ghuLdQQyyrPNAL&X-Amz-Algorithm=AWS-ALGO&X-Amz-Date=20230111T122429Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Credential=CREDS&X-Amz-Signature=abcdefghijklmnopqrstuvwxyz123",
      contentTypeLabel: "Video",
      contentDescription: "Test description"
    }) as unknown) as ContentDetails;

    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
          content={content}
          isForUpdateUploadedFile
        />
      );
    };
    const { container } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByRole("button", { name: "Upload" })).toBeDisabled();
    expect(await screen.findByText("Accepted File Formats: MOV. Max File Size : 500 MB")).toBeInTheDocument();

    //selecting other option from content type dropdown
    await selectOption({ option: "text", container, label: "Content Type" });

    expect(await screen.findByText("Accepted File Formats: TXT. Max File Size : 500 MB")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });

  it("uploads the file successfully", async () => {
    const mock = new MockAdapter(axios);
    const url = "https://mcr-int-temporary-direct-upload-useast1.s3.amazonaws.com/118/410d1/bundles";
    const signedResponse = { id: "643344f9-1539-4a44-8a6c-73ccccaf5663", url };
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", { type: ".png" });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({ data: signedResponse });
    mock.onPut(url, selectedFile).reply(200, null);
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    (fileTypeFromBlob as jest.Mock).mockResolvedValue({ mime: "image/png" });
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();
    await fillForm({
      contentTitle: "Title",
      contentType: "image",
      contentDescription: "new text",
      container
    });
    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(await screen.findByRole("progressbar", { name: /upload file progress/i })).toHaveClass(
      "content-submission-file-progress"
    );
    expect(submittedContentService.getSignedUrl).toHaveBeenCalledTimes(1);
    expect(setUpdatedContent).toHaveBeenCalledWith(true);
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.title)
    ).toBeInTheDocument();
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.content)
    ).toBeInTheDocument();
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledTimes(1);
    expect(mock.history.put).toHaveLength(1);
    expect(resetUploadForm).toHaveBeenCalled();
    expect(analytics.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(analytics.submittedFileUpload).toHaveBeenCalledWith({
      contentType: "image",
      fileExtension: "png",
      fileSize: 17,
      locale: uploadContentSubmissionFormProps.locale,
      opportunity: uploadContentSubmissionFormProps.opportunity
    });
    unmount(); // remove toast
  });

  it("updates previously uploaded content", async () => {
    const mock = new MockAdapter(axios);
    const url = "https://mcr-int-temporary-direct-upload-useast1.s3.amazonaws.com/118/410d1/bundles";
    const signedResponse = { id: "643344f9-1539-4a44-8a6c-73ccccaf5663", url };
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", { type: ".png" });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({ data: signedResponse });
    mock.onPut(url, selectedFile).reply(200, null);
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const submittedContent = (aSubmittedContent({
      name: "Test prefill values",
      contentType: "image",
      contentUri:
        "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/a0YK0000005VYBAMA4/7cf84fba-cbec-461d-8a94-caa4808d0deb/test-content.svg?versionId=OcrNFDinICS_YSOOi2ghuLdQQyyrPNAL&X-Amz-Algorithm=AWS-ALGO&X-Amz-Date=20230111T122429Z&X-Amz-SignedHeaders=host&X-Amz-Expires=900&X-Amz-Credential=CREDS&X-Amz-Signature=abcdefghijklmnopqrstuvwxyz123",
      contentTypeLabel: "Image"
    }) as unknown) as ContentDetails;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
          content={submittedContent}
          isForUpdateUploadedFile
        />
      );
    };
    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByRole("button", { name: "Upload" })).toBeDisabled();
    await selectOption({ option: submittedContent.contentType, container, label: "Content Type" });
    await userEvent.upload(await screen.findByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(await screen.findByRole("progressbar", { name: /upload file progress/i })).toHaveClass(
      "content-submission-file-progress"
    );
    expect(submittedContentService.getSignedUrl).toHaveBeenCalledTimes(1);
    expect(setUpdatedContent).toHaveBeenCalledWith(true);
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.title)
    ).toBeInTheDocument();
    expect(
      screen.getByText(submitWebsiteContentTranslations.contentSubmissionLabels.success.content)
    ).toBeInTheDocument();
    expect(submittedContentService.markUploadComplete).toHaveBeenCalledTimes(1);
    expect(mock.history.put).toHaveLength(1);
    expect(resetUploadForm).toHaveBeenCalled();
    expect(analytics.submittedFileUpload).toHaveBeenCalledTimes(1);
    expect(analytics.submittedFileUpload).toHaveBeenCalledWith({
      contentType: "image",
      fileExtension: "png",
      fileSize: 17,
      locale: uploadContentSubmissionFormProps.locale,
      opportunity: uploadContentSubmissionFormProps.opportunity
    });
    unmount(); // remove toast
  });

  it("gets a signed URL for a file upload", async () => {
    const url = Random.url();
    const signedResponse = { id: Random.uuid(), url };
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", { type: "image/png" });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({ data: signedResponse });
    mock.onPut(url, selectedFile).reply(200, null);
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    (fileTypeFromBlob as jest.Mock).mockResolvedValue({ mime: "image/png" });
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
        />
      );
    };

    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();

    await fillForm({
      contentTitle: "Title",
      contentType: "image",
      contentDescription: "new text",
      container
    });

    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(submittedContentService.getSignedUrl).toHaveBeenCalledTimes(1);
    expect(submittedContentService.getSignedUrl).toHaveBeenCalledWith({
      participationId,
      creatorId: uploadContentSubmissionFormProps.creatorId,
      deliverableId: uploadContentSubmissionFormProps.deliverableId,
      fileName: selectedFile.name,
      contentTitle: "Title",
      extension: ".png",
      contentType: "image",
      mimeType: "image/png",
      thumbnail: uploadContentSubmissionFormProps.thumbnail,
      fileSize: selectedFile.size,
      programCode: "sims_creator_program",
      contentScanSourceType: "FILE_UPLOAD",
      contentDescription: "new text",
      nucleusId: 123
    });

    unmount();
  });

  it("gets a signed URL for a listing", async () => {
    const url = Random.url();
    const signedResponse = { id: Random.uuid(), url };
    const content = JSON.stringify([{ name: "test" }]);
    const blob = new Blob([content]);
    const file = new File([blob], "values.png", { type: "image/png" });
    File.prototype.text = jest.fn().mockResolvedValueOnce(content);
    const selectedFile = file;
    (submittedContentService.getSignedUrl as jest.Mock).mockResolvedValue({ data: signedResponse });
    mock.onPut(url, selectedFile).reply(200, null);
    (submittedContentService.markUploadComplete as jest.Mock).mockImplementation(() => Promise.resolve());
    (fileTypeFromBlob as jest.Mock).mockResolvedValue({ mime: "image/png" });
    const analytics = ({ submittedFileUpload: jest.fn() } as unknown) as BrowserAnalytics;
    const UploadContentSubmissionFormWrapperComponent = () => {
      const [progressBar, setProgressBar] = useState(false);
      const [percentage, setPercentage] = useState(0);
      return (
        <UploadContentSubmissionForm
          {...uploadContentSubmissionFormProps}
          analytics={analytics}
          showProgressBar={progressBar}
          setShowProgressBar={setProgressBar}
          uploadPercentage={percentage}
          setUploadPercentage={setPercentage}
          contentFormat="LISTING"
        />
      );
    };

    const { container, unmount } = renderWithToast(<UploadContentSubmissionFormWrapperComponent />);
    expect(screen.getByTestId("upload")).toBeDisabled();

    await fillForm({
      contentTitle: "Title",
      contentType: "image",
      contentDescription: "new text",
      container
    });

    expect(await screen.findByTestId("upload")).toBeEnabled();
    await userEvent.upload(screen.getByTestId("upload"), file);
    expect(await screen.findByRole("button", { name: "Upload" })).toBeEnabled();

    await userEvent.click(screen.getByRole("button", { name: "Upload" }));

    expect(submittedContentService.getSignedUrl).toHaveBeenCalledTimes(1);
    expect(submittedContentService.getSignedUrl).toHaveBeenCalledWith({
      participationId,
      creatorId: uploadContentSubmissionFormProps.creatorId,
      deliverableId: uploadContentSubmissionFormProps.deliverableId,
      fileName: selectedFile.name,
      contentTitle: "Title",
      extension: ".png",
      contentType: "image",
      mimeType: "image/png",
      thumbnail: uploadContentSubmissionFormProps.thumbnail,
      fileSize: selectedFile.size,
      programCode: "sims_creator_program",
      contentScanSourceType: "LISTING",
      contentDescription: "new text",
      nucleusId: 123
    });

    unmount();
  });

  it("shows error message when provided title is too long", async () => {
    render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    const title = "a".repeat(952);

    await userEvent.type(screen.getByLabelText(uploadContentSubmissionFormProps.formLabels.contentTitle), title);

    expect(
      await screen.findByText(uploadContentSubmissionFormProps.contentSubmissionLabels.contentTitleLongMessage)
    ).toBeInTheDocument();
  }, 15_000);

  it("shows error message when provided Description is too long", async () => {
    render(<UploadContentSubmissionForm {...uploadContentSubmissionFormProps} />);
    const contentDescription = "a".repeat(802);

    await userEvent.type(
      screen.getByPlaceholderText(uploadContentSubmissionFormProps.formLabels.contentDescriptionPlaceholder),
      contentDescription
    );
    expect(
      await screen.findByText(uploadContentSubmissionFormProps.contentSubmissionLabels.contentDescriptionLongMessage)
    ).toBeInTheDocument();
  }, 12_000);

  async function fillForm({ contentTitle, contentType, contentDescription, container }) {
    // Fill out the title
    const contentTitleInput = screen.getByLabelText(uploadContentSubmissionFormProps.formLabels.contentTitle);
    await userEvent.type(contentTitleInput, contentTitle);
    await waitFor(() => expect(contentTitleInput).toHaveValue(contentTitle));
    // Fill out the description
    const contentDescriptionInput = screen.getByPlaceholderText(
      uploadContentSubmissionFormProps.formLabels.contentDescriptionPlaceholder
    );
    await userEvent.type(contentDescriptionInput, contentDescription);
    await waitFor(() => expect(contentDescriptionInput).toHaveValue(contentDescription));

    // Select the content type
    const contentTypeDropdown = await selectOption({ option: contentType, container, label: "Content Type" });
    // Wait for the submit button to be enabled
    const uploadButton = screen.getByText(uploadContentSubmissionFormProps.buttonsLabels.upload);

    return { contentTitleInput, contentTypeDropdown, uploadButton };
  }
});
