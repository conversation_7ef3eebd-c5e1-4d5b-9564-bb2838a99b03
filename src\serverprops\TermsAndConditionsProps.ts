import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import TermsAndConditionsPagePropsController from "@src/serverprops/controllers/TermsAndConditionsPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";
import config from "config";

const termsAndConditionsProps = (locale: string) =>
  serverPropsControllerFactory(
    new TermsAndConditionsPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default termsAndConditionsProps;
