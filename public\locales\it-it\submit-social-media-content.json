{"title": "Aggiungi contenuti da {{mediaType}}", "infoLabel": "Assicurati che l'URL che stai aggiungendo provenga da questo account {{mediaType}}:", "contentUrl": "Inserisci l'URL di {{mediaType}}:", "contentUrlPlaceholder": "Esempio: https://www.{{mediaType}}.com/mylink", "duplicateUrl": "Questo URL è già stato inviato.", "instagramErrorUrl": "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.", "videoNotFromChannel": "Questo video non proviene da un canale social collegato al tuo account del Creator Network.", "youtubeVideoError": "Nessun video YouTube trovato con l'ID fornito.", "genericContentError": "Inserisci un URL valido.", "unsupportedContentError": "Il contenuto che stai cercando di inviare non corrisponde all'opzione selezionata.", "cancelButton": "<PERSON><PERSON><PERSON>", "submitButton": "Invia contenuti", "contentUrlRequired": "L'URL di {{mediaType}} è obbligatorio", "unsafeUrlError": "Non puoi inviare contenuti da questo sito web o dominio.", "urlNotFromConnectedAccount": "L'URL che hai inserito non proviene da questo account \"Social Channel\" collegato.", "urlNotFromConnectedChannel": "L'URL che stai tentando di inviare non proviene dall'account {{accountName}} di {{mediaType}}", "unknownTikTokVideo": "The URL you are trying to submit is either not from the {{accountName}} TikTok account or is not publicly available.", "invalidSocialSubmission": "Impossibile salvare i contenuti del social media perché: l'ID dell'account non può essere vuoto", "accountAuthorizationFailure": "Impossibile salvare i contenuti del social media perché: impossibile ottenere le informazioni dell'account di TikTok. Token scaduto o non valido", "cannotExpandUrl": "Expanding this short URL is taking longer than usual. Please try again later.", "cannotSubmitContentInvalidInput": "Submitted URL is invalid", "invalidFacebookPage": "Unable to submit. This content does not belong to the Facebook page selected.", "unSupportedContentType": "Only Instagram videos and reels are allowed for submission. The content type you're trying to upload is not supported.", "unSupportedContentTypeForMedia": "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.", "duplicateScannedContentUrl": "Your Community Manager has submitted this URL."}