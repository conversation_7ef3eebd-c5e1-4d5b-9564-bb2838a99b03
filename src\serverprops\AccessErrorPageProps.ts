import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import AccessErrorPagePropsController from "@src/serverprops/controllers/AccessErrorPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";

const accessErrorPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new AccessErrorPagePropsController(ApiContainer.get("options"), ApiContainer.get(ContentManagementService), locale)
  );

export default accessErrorPageProps;
