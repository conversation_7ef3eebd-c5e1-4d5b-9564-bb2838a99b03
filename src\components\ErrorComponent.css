.error-container {
  @apply flex min-h-[1050px] w-full flex-col flex-col items-center justify-center bg-error-mobile-background-image bg-cover bg-top  bg-no-repeat xs:min-h-[590px] md:min-h-[746px] md:bg-error-tablet-background-image lg:min-h-[748px]  lg:bg-error-desktop-background-image 2xl:flex-row 2xl:bg-error-desktop-background-image;
}

.error-container .error-character-container {
  @apply h-full min-h-[1050px] w-full bg-error-mobile-character-background-image bg-cover bg-center bg-no-repeat xs:min-h-[297px] md:min-h-[433px] md:bg-error-tablet-character-background-image lg:min-h-[748px] lg:max-w-[712px] lg:bg-error-desktop-character-background-image lg:bg-right 2xl:bg-error-desktop-character-background-image;
}

.error-page {
  @apply pt-[104px];
}
.error-content-container {
  @apply max-w-[567px] p-meas12 text-center text-white 2xl:mx-[7.5rem] 2xl:min-w-[567px] 2xl:text-left;
}
.error-content-container > .error-heading {
  @apply mt-meas25 font-display-bold text-desktop-display-large font-bold text-gray-10;
  font-feature-settings: "liga" off, "clig" off;
}

.error-content-container > .error-title {
  @apply m-meas0 p-meas15 font-display-bold font-bold text-gray-10 xs:px-meas0 xs:text-mobile-h1 md:text-desktop-h2;
}

.error-content-container > .error-content {
  @apply text-desktop-h4 text-white xs:font-text-regular xs:text-mobile-h4;
}
.error-content-container > .error-nav {
  @apply flex flex-row flex-wrap justify-center gap-x-meas5 pt-meas15 font-text-regular text-desktop-body-large text-white 2xl:justify-start;
}
.error-content-container > .error-nav .nav-web-link-label {
  @apply cursor-pointer border-b-0 pb-meas0 underline xs:font-text-regular  md:text-desktop-body-large lg:text-desktop-body-large;
}
