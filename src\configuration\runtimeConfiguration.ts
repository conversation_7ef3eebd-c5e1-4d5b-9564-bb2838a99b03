import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import config from "config";

const runtimeConfiguration = (user?: AuthenticatedUser) => ({
  GTM_AUTH: config.GTM_AUTH,
  GTM_PREVIEW: config.GTM_PREVIEW,
  SENTRY_DSN: config.SENTRY_DSN,
  BUILD_VERSION: config.RELEASE_VERSION,
  APP_ENV: config.APP_ENV,
  SUPPORTED_LOCALES: config.SUPPORTED_LOCALES,
  HTTP_REQUEST_TIMEOUT: config.HTTP_REQUEST_TIMEOUT,
  SERVICE_NAME: config.SERVICE_NAME,
  NOTIFICATIONS_MFE_BASE_URL: config.NOTIFICATIONS_MFE_BASE_URL,
  ALLOWED_NOTIFICATIONS: config.ALLOWED_NOTIFICATIONS,
  PROGRAM_CODE: config.PROGRAM_CODE,
  METADATA_API_BASE_URL: config.METADATA_API_BASE_URL,
  MENU_ITEMS: config.MENU_ITEMS,
  user: user ?? null,
  ONBOARDING_MFE_BASE_URL: config.ONBOARDING_MFE_BASE_URL,
  NOTIFICATION_BASE_URLS: config.NOTIFICATION_BASE_URLS,
  SINGLE_PROGRAM_NOTIFICATIONS: config.SINGLE_PROGRAM_NOTIFICATIONS,
  DEFAULT_NOTIFICATION_PROGRAM: config.DEFAULT_NOTIFICATION_PROGRAM,
  BASE_PATH: config.BASE_PATH,
  CREATORS_API_BASE_URL: config.CREATORS_API_BASE_URL,
  FLAG_OBSERVABILITY: config.FLAG_OBSERVABILITY
});

export default runtimeConfiguration;
