{"addContent": "Ajou<PERSON>z du contenu", "addNewContent": "Ajouter du nouveau contenu", "addContentInstruction": "Veuillez sélectionner l’endroit où vous souhaitez envoyer du contenu :", "reviewContent": "Vérification", "title": "Ajoutez la ou les URL de votre contenu", "description": "You can submit content from a connected account on Facebook, Instagram, Youtube, or Twitch and from another website.", "opportunityHeading": "Envoi de contenu pour :", "urlTitle": "Veuillez saisir l’URL du contenu :", "urlPlaceholder": "Saisissez une URL...", "addMoreUrlLabel": "Ajoutez une autre URL", "accountInformation1": "Vous avez besoin d'aide pour envoyer la bonne URL de réseau social ?", "accountInformation2": "Sélectionnez un réseau →", "clickTheIcon": "Cliquez sur l’icône →", "contentInformation1": "Respectez les droits des autres personnes. N’envoyez que du contenu pour lequel vous possédez une autorisation écrite de distribution. Veuillez consulter notre", "contentInformation2": "Contrat Utilisateur ", "contentInformation3": "pour en savoir plus.", "modalTitle": "Annuler l’envoi de contenu ?", "modalDescription": "Voulez-vous vraiment annuler l'envoi de contenu ? Tous les envois de contenu saisis à ce stade seront perdus.", "no": "Non, je veux rester sur cette page", "yes": "<PERSON><PERSON>, je veux quitter", "contentSubmissionSucessTitle": "{{accountConnected}} Connexion réussie !", "contentSubmissionSucessDescription": "Vous avez réussi à vous connecter à {{accountConnected}}. Vous pouvez envoyer du contenu à partir de ce compte.", "connectAnAccount": "Connectez un compte"}