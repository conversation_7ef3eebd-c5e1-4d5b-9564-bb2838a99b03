import { Inject, Service } from "typedi";
import CreatorWithCreatorPrograms from "./CreatorWithCreatorPrograms";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithProgramsHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithCreatorPrograms> {
    const response = await this.client.get(`/v7/creators/${id}`);
    return Promise.resolve(CreatorWithCreatorPrograms.fromApi(response.data));
  }
}

export default CreatorsWithProgramsHttpClient;
