import { CompositePropagator, W3CBaggagePropagator, W3CTraceContextPropagator } from "@opentelemetry/core";
import { BatchSpanProcessor, WebTracerProvider } from "@opentelemetry/sdk-trace-web";
import { registerInstrumentations } from "@opentelemetry/instrumentation";
import { Resource } from "@opentelemetry/resources";
import {
  SEMRESATTRS_DEPLOYMENT_ENVIRONMENT,
  SEMRESATTRS_SERVICE_NAME,
  SEMRESATTRS_SERVICE_VERSION
} from "@opentelemetry/semantic-conventions";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { NoOpSpanExporter } from "@eait-playerexp-cn/telemetry";

export type FrontendTracerOptions = {
  serviceName: string;
  environment: string;
  version: string;
  enabled: boolean;
};

const FrontendTracer = async ({ serviceName, environment, version, enabled }: FrontendTracerOptions) => {
  const { ZoneContextManager } = await import("@opentelemetry/context-zone");

  const tracerProvider = new WebTracerProvider({
    resource: Resource.default().merge(
      new Resource({
        [SEMRESATTRS_SERVICE_NAME]: serviceName,
        [SEMRESATTRS_DEPLOYMENT_ENVIRONMENT]: environment,
        [SEMRESATTRS_SERVICE_VERSION]: version
      })
    )
  });

  const contextManager = new ZoneContextManager();

  tracerProvider.addSpanProcessor(
    new BatchSpanProcessor(enabled ? new OTLPTraceExporter({ headers: {} }) : new NoOpSpanExporter())
  );

  tracerProvider.register({
    contextManager,
    propagator: new CompositePropagator({
      propagators: [new W3CBaggagePropagator(), new W3CTraceContextPropagator()]
    })
  });

  registerInstrumentations({ tracerProvider });
};

export default FrontendTracer;
