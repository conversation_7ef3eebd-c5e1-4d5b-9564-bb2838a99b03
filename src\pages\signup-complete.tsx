import "reflect-metadata";
import React, { FC } from "react";
import { useRouter } from "next/router";
import Layout, { LayoutHeader } from "@components/Layout";
import TopNavBar from "@components/header/TopNavBar";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import { SignupCompletePageLabels } from "@src/server/contentManagement/SignupCompletePageMapper";
import SignUpCompletePage from "@components/signup-complete/SignUpCompletePage";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import { GetServerSidePropsResult } from "next";
import signupCompleteProps from "@src/serverprops/SignupCompleteProps";

export type SignupCompleteProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: SignupCompletePageLabels;
};

const SignupCompletePage: FC<SignupCompleteProps> = ({ pageLabels }) => {
  const { signupCompleteLabels } = pageLabels;
  const router = useRouter();
  const { locale } = router;

  const onSignupButtonClick = () => {
    router.push("/dashboard");
  };

  return (
    <Layout>
      <LayoutHeader pageTitle="">
        <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
      </LayoutHeader>
      <SignUpCompletePage pageLabels={{ signupCompleteLabels }} onClick={onSignupButtonClick} />
    </Layout>
  );
};

export default SignupCompletePage;

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();
  router
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(addLocaleCookie(locale))
    .get(signupCompleteProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<SignupCompleteProps>;
};
