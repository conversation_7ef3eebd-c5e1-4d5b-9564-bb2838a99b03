import "reflect-metadata";
import { memo, useMemo } from "react";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { ErrorPageLabels } from "@src/server/contentManagement/ErrorPageMapper";
import Image from "next/image";
import { LayoutHeader } from "@components/Layout";
import TopNavBar from "@components/header/TopNavBar";
import { useRouter } from "next/router";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import accountDeactivatedPageProps from "@src/serverprops/AccountDeactivatedPageProps";

export type AccountDeactivatedProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: CommonPageLabels & ErrorPageLabels;
  deactivatedAccount: {
    defaultGamerTag: string;
  };
};

export default memo(function AccountDeactivated({ pageLabels, deactivatedAccount }: AccountDeactivatedProps) {
  const { errorLabels } = pageLabels;
  const { locale } = useRouter();

  const formattedSubtitle = useMemo(() => {
    return errorLabels.accountDeactivateSubtitle.replace(
      /\{\{Username\}\}/g,
      `"${deactivatedAccount.defaultGamerTag}"`
    );
  }, [errorLabels.accountDeactivateSubtitle, deactivatedAccount.defaultGamerTag]);

  return (
    <>
      <LayoutHeader pageTitle={errorLabels.accountDeactivateTitle} tabTitle={errorLabels.accountDeactivated}>
        <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
      </LayoutHeader>
      <div className="erro-mg-container">
        <div className="error-mg-bg"></div>
        <div className="account-deactivated-header sims-logo">
          <div className="sims-creator-logo">
            <Image
              src="/img/sims-logo.svg"
              alt="The Sims Creator Program"
              width={175}
              height={42}
              className="scp__characters-image"
              priority
            />
          </div>
        </div>
        <div className="account-deactivated-container">
          <h2 className="account-deactivated-title">{errorLabels.accountDeactivateTitle} </h2>
          <div className="account-deactivated-content">{formattedSubtitle}</div>
          <hr className="horizontal-line" />
        </div>
      </div>
    </>
  );
});

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .get(accountDeactivatedPageProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<AccountDeactivatedProps>;
};
