import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { FAQsProps } from "@src/pages/faq";
import ArticlesHttpClient from "@src/server/contentModal/ArticlesHttpClient";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export default class FaqPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<FAQsProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly articles: ArticlesHttpClient,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, _res: NextApiResponse): Promise<GetServerSidePropsResult<FAQsProps>> {
    try {
      const authenticatedUser = this.hasIdentity(req)
        ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.program, this.defaultAvatar)
        : null;
      const { data: faqPage } = await this.articles.matching("faq", {
        locale: this.currentLocale
      });

      return {
        props: {
          runtimeConfiguration: runtimeConfiguration(),
          faqPage,
          user: authenticatedUser,
          ...(await serverSideTranslations(this.currentLocale, [
            "common",
            "dashboard",
            "my-content",
            "notifications",
            "connect-accounts",
            "opportunities",
            "faq"
          ]))
        }
      };
    } catch (e) {
      if (e instanceof ArticleNotFound) return { notFound: true };
      throw e;
    }
  }
}
