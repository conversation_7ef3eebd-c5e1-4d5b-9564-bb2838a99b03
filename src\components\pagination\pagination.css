/* Pagination css */
.pagination-container {
  @apply flex justify-center font-text-regular;
}
.pagination-numbers {
  @apply ml-meas20 mr-meas20 flex cursor-pointer md:ml-meas32 md:mr-meas32;
}
.pagination-number {
  @apply mr-meas4;
}
.pagination-number:last-child {
  @apply mr-meas0;
}
.pagination-text {
  @apply flex cursor-pointer items-center uppercase xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.pagination-number.pagination-text.button-reset,
.pagination-text.button-reset:disabled {
  @apply text-font-disabled opacity-100;
}
.pagination-text-selected.pagination-number.pagination-text {
  @apply text-font-normal;
}
.pagination-text.button-reset {
  @apply text-font-normal;
}
