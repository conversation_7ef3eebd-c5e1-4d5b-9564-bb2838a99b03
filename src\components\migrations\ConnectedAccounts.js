import React, { memo, useCallback, useMemo } from "react";
import {
  AccountCard,
  discordIcon,
  facebookIcon,
  instagramIcon,
  tiktokIcon,
  twitchIcon,
  youTubeIcon
} from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import {
  GET_FB_PAGES,
  POPUP_OPENED,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS,
  SHOW_FACEBOOK_PAGES,
  useAsync,
  WINDOW_PARAMS
} from "../../utils";
import Loading from "../Loading";
import ConnectedAccountsService from "../../services/ConnectedAccountsService";
import DisconnectAccountModal from "../pages/DisconnectAccountModal";
import { useDependency } from "@src/context/DependencyContext";

const AccountsMapper = memo(function AccountsMapper({
  id = null,
  accountId = null,
  accountType = "",
  username = "",
  name = "",
  labels,
  setAccountToRemove,
  disconnected = false,
  isExpired = false,
  setShowAddConfirmation,
  showAddConfirmation,
  setShowRemoveAccountModal,
  isInterestedCreator
}) {
  const { errorHandler } = useDependency();
  const { dispatch } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);

  const onRemoveAccount = useCallback(
    (e) => {
      e.preventDefault();
      setAccountToRemove(`${id}-${accountType}`);
      accountType === "FACEBOOK" && dispatch({ type: GET_FB_PAGES, data: true });
      setShowRemoveAccountModal(true);
    },
    [id, accountType, setAccountToRemove, setShowRemoveAccountModal]
  );

  const onReconnectAccount = useCallback(
    (e) => {
      e.preventDefault();

      if (!showAddConfirmation) {
        setShowAddConfirmation(true);
        const loginWindow = window.open(`/api/${accountType.toLowerCase()}-login`, "_blank", WINDOW_PARAMS);
        stableDispatch({ type: POPUP_OPENED, data: true });
        const loop = setInterval(function () {
          if (loginWindow.closed) {
            clearInterval(loop);
            stableDispatch({ type: POPUP_OPENED, data: false });
            ConnectedAccountsService.clearAccountType()
              .then(() => {
                isInterestedCreator && accountType === "FACEBOOK" && dispatch({ type: GET_FB_PAGES, data: true });
                isInterestedCreator &&
                  accountType === "FACEBOOK" &&
                  dispatch({ type: SHOW_FACEBOOK_PAGES, data: true });
                isInterestedCreator
                  ? dispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true })
                  : window.location.reload(); // refresh to trigger getServersideProps rerun and spew new session props
              })
              .catch((e) => errorHandler(stableDispatch, e));
          }
        }, 100);
      }
    },
    [showAddConfirmation, setShowAddConfirmation, stableDispatch]
  );
  let accounts = [
    { label: "youTube", accountIcon: youTubeIcon, type: "youtube", accountType: "YOUTUBE" },
    { label: "twitch", type: "twitch", accountIcon: twitchIcon, accountType: "TWITCH" },
    { label: "facebook", type: "facebook", accountIcon: facebookIcon, accountType: "FACEBOOK" },
    { label: "instagram", type: "instagram", accountIcon: instagramIcon, accountType: "INSTAGRAM" },
    { label: "tiktok", type: "tiktok", accountIcon: tiktokIcon, accountType: "TIKTOK" },
    { label: "discord", type: "discord", accountIcon: discordIcon, accountType: "DISCORD" }
  ];
  const accountIcon = useMemo(() => accounts.find((account) => account.accountType === accountType)?.accountIcon, [
    accountType
  ]);

  return (
    <>
      <AccountCard
        {...{
          accountIcon,
          expired: !disconnected && isExpired,
          handleRemoveAccount: onRemoveAccount,
          accountId,
          accountType,
          labels,
          username: username || name,
          handleReconnectAccount: onReconnectAccount
        }}
      />
    </>
  );
});

const Accounts = memo(function Accounts({
  accounts,
  setAccountToRemove,
  accountToRemove,
  labels,
  setShowAddConfirmation,
  showAddConfirmation,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  isInterestedCreator
}) {
  return useMemo(() => {
    return accounts.map((account, ind) => (
      <AccountsMapper
        key={ind}
        {...{
          labels,
          setAccountToRemove,
          accountToRemove,
          ...{ ...account, accountType: account.type },
          setShowAddConfirmation,
          isExpired: account.expired,
          showAddConfirmation,
          showRemoveAccountModal,
          setShowRemoveAccountModal,
          isInterestedCreator
        }}
      />
    ));
  }, [accounts, labels, setAccountToRemove, accountToRemove]);
});

export const ConnectedAccount = memo(function ConnectedAccount({
  layout,
  labels,
  setAccountToRemove,
  accountToRemove,
  accounts,
  isLoading,
  setShowAddConfirmation,
  showAddConfirmation,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  isInterestedCreator
}) {
  const { errorHandler } = useDependency();
  const { dispatch } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { removeAccountTitle, removeAccountDescription1, removeAccountDescription2 } = labels.modal;
  const onCloseRemoveModal = useCallback(() => setShowRemoveAccountModal(false), [setShowRemoveAccountModal]);

  const onRemoveAccount = useCallback(async () => {
    const [id, type = ""] = accountToRemove.match(/\w+(?=\-(\w+)+)/);
    try {
      if (type && /^discord$/i.test(type)) {
        await ConnectedAccountsService.removeDiscordAccount(id);
      } else {
        await ConnectedAccountsService.removeConnectedAccount(id);
      }
      setAccountToRemove(null);
      setShowRemoveAccountModal(false);
    } catch (error) {
      setAccountToRemove(null);
      setShowRemoveAccountModal(false);
      errorHandler(stableDispatch, error);
    }
  }, [accountToRemove, stableDispatch, setAccountToRemove]);
  const { pending, execute: onRemove } = useAsync(onRemoveAccount, false);

  const modalLabels = {
    title: removeAccountTitle,
    close: layout.buttons.close,
    remove: layout.buttons.remove,
    cancel: layout.buttons.cancel,
    removeAccountDescription1: removeAccountDescription1,
    removeAccountDescription2: removeAccountDescription2
  };

  return (
    (isLoading && <Loading />) ||
    (!!accounts.length && (
      <div>
        <div className="mg-connected-accounts-container">
          <h4 className="mg-connected-accounts-title">{labels.myAccount}</h4>
          <div className="connected-acc-card-container">
            <Accounts
              {...{
                accounts,
                setAccountToRemove,
                accountToRemove,
                labels,
                setShowAddConfirmation,
                showAddConfirmation,
                showRemoveAccountModal,
                setShowRemoveAccountModal,
                isInterestedCreator
              }}
            />
          </div>
        </div>
        {showRemoveAccountModal && (
          <DisconnectAccountModal
            labels={modalLabels}
            onRemove={onRemove}
            onCancel={onCloseRemoveModal}
            isPending={pending}
          />
        )}
      </div>
    ))
  );
});
export default ConnectedAccount;
