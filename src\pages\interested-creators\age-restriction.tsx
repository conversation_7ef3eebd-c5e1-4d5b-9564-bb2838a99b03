import { AgeRestrictionPageLabels } from "@src/server/contentManagement/AgeRestrictionPageMapper";
import Image from "next/image";
import React, { FC } from "react";
import Layout, { LayoutHeader } from "@components/Layout";
import TopNavBar from "@components/header/TopNavBar";
import { useRouter } from "next/router";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import ageRestrictionProps from "@src/serverprops/AgeRestrictionProps";
import { GetServerSidePropsResult } from "next";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";

export type AgeRestrictionProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: AgeRestrictionPageLabels;
  locale?: string;
};

const AgeRestrictionPage: FC<AgeRestrictionProps> = ({ pageLabels }) => {
  const { ageRestrictionLabels } = pageLabels;
  const { locale } = useRouter();

  return (
    <Layout>
      <LayoutHeader pageTitle={ageRestrictionLabels.title}>
        <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
      </LayoutHeader>
      <div className="age-restriction-container">
        <div className="age-restriction-bg"></div>
        <div className="age-restriction-header">
          <div className="sims-creator-logo">
            <Image
              src="/img/sims-logo.svg"
              alt="The Sims Creator Program"
              width={175}
              height={42}
              className="scp__characters-image"
              priority
              data-testid="the-sims-creator-program"
            />
          </div>
        </div>
        <div className="age-restriction-main-content age-restriction-content">
          <div className="horizontal-line top-line"></div>
          <div className="age-restriction-content-container">
            <h1 className="age-restriction-title">{ageRestrictionLabels.title}</h1>
            <div className="age-restriction-body">{ageRestrictionLabels.subTitle}</div>
          </div>
          <div className="horizontal-line bottom-line"></div>
        </div>
      </div>
    </Layout>
  );
};

export default AgeRestrictionPage;

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();
  router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(ageRestrictionProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<AgeRestrictionProps>;
};
