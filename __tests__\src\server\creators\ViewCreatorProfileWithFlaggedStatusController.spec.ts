import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ViewCreatorProfileWithFlaggedStatusController from "../../../../src/server/creators/ViewCreatorProfileWithFlaggedStatusController";
import CreatorsWithFlaggedStatusHttpClient from "../../../../src/server/creators/CreatorsWithFlaggedStatusHttpClient";
import { aCreatorWithFlaggedStatus } from "../../../factories/creators/CreatorWithFlaggedStatus";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("ViewCreatorProfileWithFlaggedStatusController", () => {
  let controller: ViewCreatorProfileWithFlaggedStatusController;

  beforeEach(() => jest.clearAllMocks());

  it("shows a creator with flagged status", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/v6/creators`,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const creator = aCreatorWithFlaggedStatus();
    const creators = { withId: jest.fn().mockResolvedValue(creator) };
    controller = new ViewCreatorProfileWithFlaggedStatusController(
      (creators as unknown) as CreatorsWithFlaggedStatusHttpClient
    );

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creator);
    expect(creators.withId).toHaveBeenCalledTimes(1);
    expect(creators.withId).toHaveBeenCalledWith(userId);
  });
});
