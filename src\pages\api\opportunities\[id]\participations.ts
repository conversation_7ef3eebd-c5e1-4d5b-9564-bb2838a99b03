import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";
import ApiContainer from "@src/ApiContainer";
import JoinOpportunityAction from "@src/server/JoinOpportunity/JoinOpportunityAction";
import JoinOpportunityInput from "@src/server/JoinOpportunity/JoinOpportunityInput";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(JoinOpportunityAction);
    const identity = req.session.identity;
    const opportunityId = req.query.id as string;

    const participation = await action.execute(new JoinOpportunityInput(identity.id, opportunityId, req.body));

    res.status(201).json(participation);
    res.end();
  });

export default router.handler({ onError });
