.mg-header-container {
  @apply mb-meas16 flex w-full flex-col !px-meas0 pt-meas10 md:mb-meas31 md:!px-meas15 md:pt-meas12;
}
.interested-creator .mg-header-close {
  @apply !mr-meas0 !mt-meas0;
}
.mg-header {
  @apply flex w-full flex-row !items-center justify-between px-meas5 pb-meas13 text-gray-10 md:px-meas0;
}
.mg-header-back {
  @apply block flex-1 justify-start md:hidden;
}
.mg-header-logo {
  @apply hidden justify-between font-display-regular font-bold xs:text-mobile-h5 md:block md:text-tablet-h5 lg:text-desktop-h5 xl:justify-start;
}
.mg-header-logo > a > span {
  @apply ml-meas4;
}
.mg-header-close {
  @apply flex flex-1 justify-end;
}
.mg-header-logo .icon-block {
  @apply m-meas4 inline;
}
.mg-header-logo .ea-logo {
  @apply inline h-meas16 w-meas16 fill-gray-10;
}
.mg-header-steps {
  @apply flex w-full flex-col items-center justify-between xl:flex-row;
}
.mg-header-stepper-back {
  @apply hidden self-start md:mb-meas10 md:block;
}
.mg-header-display-back-bt {
  @apply flex w-[75px] items-center justify-center;
}
.mg-header-display-back-bt > span:last-child {
  @apply ml-meas4 cursor-pointer font-display-regular text-gray-10;
}
.mg-header-hide-back-bt {
  @apply hidden;
}
.mg-header-breadcrumb-nav {
  @apply left-meas9 cursor-pointer text-gray-10;
}
