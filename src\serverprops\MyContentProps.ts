import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import MyContentPagePropsController from "./controllers/MyContentPagePropsController";
import config from "config";

const myContentProps = (locale: string) =>
  serverPropsControllerFactory(
    new MyContentPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default myContentProps;
