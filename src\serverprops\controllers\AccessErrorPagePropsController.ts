import { Re<PERSON><PERSON><PERSON><PERSON>, <PERSON>questH<PERSON>lerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { AccessErrorProps } from "@src/pages/access-error";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { ErrorPageLabels } from "@src/server/contentManagement/ErrorPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult } from "next";

export default class AccessErrorPagePropsController
  extends RequestHandler
  implements ServerPropsController<AccessErrorProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<AccessErrorProps>> {
    const pageLabels = (await this.contents.getPageLabels(this.currentLocale, "error")) as ErrorPageLabels &
      CommonPageLabels;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels
      }
    };
  }
}
