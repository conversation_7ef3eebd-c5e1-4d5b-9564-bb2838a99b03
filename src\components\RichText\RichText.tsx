import { ArticleRichTextBody } from "@src/pages/articles/[...slug]";
import { documentToReactComponents } from "@contentful/rich-text-react-renderer";
import { BLOCKS, INLINES, Node } from "@contentful/rich-text-types";
import { ReactNode } from "react";
import Text from "@components/RichTextComponents/Text/Text";
import EmbeddedObjects from "../EmbeddedObjects/EmbeddedObjects";
import Link, { LinkType } from "@components/RichTextComponents/Link/Link";
import { ArticleType } from "@components/ArticlePage/ArticlePage";
import Table from "@components/RichTextComponents/Table/Table";

export type Media = {
  url: string;
  title?: string;
  description?: string;
  fileName?: string;
  contentType?: string;
  size?: number;
  width?: number;
  height?: number;
};

export type SystemInformation = {
  id: string;
  publishedAt: string;
  firstPublishedAt: string;
  publishedVersion: number;
};
export type ImageType = "Image";

export type EmbeddedItem = {
  sys: SystemInformation;
  title?: string;
  entryType: ImageType | LinkType;
  media?: Media;
  mobileMedia?: Media;
  altText?: string;
  titleTags?: string;
  ariaTags?: string;
  administrativeTitle?: string;
  url?: string;
  openInNewTab?: boolean;
  page?: {
    slug: string;
    type: ArticleType;
  };
};

const findEmbeddedSection = (embeddedItems: Array<EmbeddedItem>, node: Node): EmbeddedItem | undefined => {
  const { data } = node;
  return embeddedItems.find((entry) => entry.sys.id === data.target.sys.id);
};

const RichText = ({ richText, embeddedItems }: ArticleRichTextBody) => {
  if (!richText) return null;

  const options = {
    renderNode: {
      [BLOCKS.QUOTE]: (_: Node, children: ReactNode) => {
        return <blockquote className="rich-text-quote">{children}</blockquote>;
      },
      [BLOCKS.PARAGRAPH]: (_: Node, children: ReactNode) => {
        return <Text className="rich-text-paragraph">{children}</Text>;
      },
      [BLOCKS.TABLE]: (_: Node, children: ReactNode) => {
        return <Table {...{ children }} />;
      },
      [BLOCKS.TABLE_ROW]: (_: Node, children: ReactNode) => {
        return <tr className="rich-text-table-row">{children}</tr>;
      },
      [BLOCKS.TABLE_HEADER_CELL]: (_: Node, children: ReactNode) => {
        return <th className="rich-text-table-header">{children}</th>;
      },
      [BLOCKS.TABLE_CELL]: (_: Node, children: ReactNode) => {
        return <td className="rich-text-table-cell">{children}</td>;
      },
      [BLOCKS.OL_LIST]: (_: Node, children: ReactNode) => {
        return <ol className="rich-text-ordered-list">{children}</ol>;
      },
      [BLOCKS.UL_LIST]: (_: Node, children: ReactNode) => {
        // This should be improved to individual components after June release
        return <ul className="rich-text-unordered-list">{children}</ul>;
      },
      [BLOCKS.LIST_ITEM]: (_: Node, children: ReactNode) => {
        return <li className="rich-text-list-item">{children}</li>;
      },
      [INLINES.ENTRY_HYPERLINK]: (node: Node, children: ReactNode) => {
        if (!embeddedItems) return null;

        const embeddedEntry = findEmbeddedSection(embeddedItems, node);
        if (!embeddedEntry) return null;

        const { url, page, openInNewTab } = embeddedEntry;
        return (
          <Link {...{ url, page, openInNewTab }} className="rich-text-link">
            {children}
          </Link>
        );
      },
      [BLOCKS.EMBEDDED_ENTRY]: (node: Node) => {
        if (!embeddedItems) return null;
        const embeddedEntry = findEmbeddedSection(embeddedItems, node);

        if (!embeddedEntry) return null;
        if (embeddedEntry.entryType === "Link") return null;

        const { media, entryType } = embeddedEntry;
        return <EmbeddedObjects {...{ entryType, media }} />;
      },
      [INLINES.HYPERLINK]: (node: Node, children: ReactNode) => {
        const url = node.data.uri as string;

        return (
          <Link url={url} openInNewTab className="rich-text-link">
            {children}
          </Link>
        );
      },
      [BLOCKS.HR]: () => {
        return <hr className="my-meas16" />;
      },
      [BLOCKS.HEADING_1]: (_: Node, children: ReactNode) => {
        return (
          <Text as="h1" className="rich-text-heading1">
            {children}
          </Text>
        );
      },
      [BLOCKS.HEADING_2]: (_: Node, children: ReactNode) => {
        return (
          <Text as="h2" className="rich-text-heading2">
            {children}
          </Text>
        );
      },
      [BLOCKS.HEADING_3]: (_: Node, children: ReactNode) => {
        return (
          <Text as="h3" className="rich-text-heading3">
            {children}
          </Text>
        );
      },
      [BLOCKS.HEADING_4]: (_: Node, children: ReactNode) => {
        return (
          <Text as="h4" className="rich-text-heading4">
            {children}
          </Text>
        );
      },
      [BLOCKS.HEADING_5]: (_: Node, children: ReactNode) => {
        return (
          <Text as="h5" className="rich-text-heading5">
            {children}
          </Text>
        );
      },
      [BLOCKS.HEADING_6]: (_: Node, children: ReactNode) => {
        return (
          <Text as="h6" className="rich-text-heading6">
            {children}
          </Text>
        );
      }
    }
  };

  return <div data-testid="richText">{documentToReactComponents(JSON.parse(richText), options)}</div>;
};

export default RichText;
