import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import Loading from "@components/loading/Loading";
import { AccountType, Content, ContentCard, ContentFeedbackProps } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "@src/context";
import { HAS_EXCEPTION, SESSION_USER } from "@src/utils";
import classNames from "classnames";
import Link from "next/link";
import { useRouter } from "next/router";
import ErrorPage from "./_error";
import Pagination from "@components/pagination/Pagination";
import { useDependency } from "@src/context/DependencyContext";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import SubmittedContentService, { ContentsFeedback } from "@src/services/SubmittedContentService";
import Header from "@components/header/header";
import labelsDashboard from "@src/config/translations/dashboard";
import labelsCommon from "@src/config/translations/common";
import labelsMyContent from "@src/config/translations/my-content";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import { NotificationsLabels, TopNavigationPageLabels } from "@components/ProgramTopNavigation";
import Footer, { FooterPageLabels } from "@src/components/footer/ProgramFooter";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import { GetServerSidePropsResult } from "next";
import myContentProps from "@src/serverprops/MyContentProps";

const PAGE_SIZE = 10;
const DEFAULT_PAGE_NUMBER = 1;

/**
 * A component for listing the contents submitted by current logged in user.
 *
 * @param {object} user - a current logged in user details
 * @param {string} locale - a locale to translate the app
 * @returns {JSX.Element}
 */

type ContentCardLayout = {
  contentCard: {
    submitted: string;
    approved: string;
    rejected: string;
    notApproved: string;
    contentNotApproved: string;
    contentApproved: string;
    pendingApproval: string;
    approvalNotRequired: string;
    changesRequired: string;
    inReview: string;
    viewChangesRequired: string;
    hideChangesRequired: string;
    viewDetails: string;
    hideDetails: string;
    updated: string;
    inScan: string;
    sentOn: string;
    from: string;
  };
  buttons?: {
    upload?: string;
  };
};

type ContentItem = {
  id: string;
  opportunityId: string;
  status: string;
  type?: AccountType;
  sourceType: string;
  requiresChanges: () => boolean;
  formattedSubmittedDate: (locale: string) => string;
  formattedReviewFinalRemarkDate?: (locale: string) => string;
  reviewFinalRemark?: {
    content: string;
    author: string;
    date: string;
  };
  contentType?: string;
  contentTypeLabel?: string;
};

type ContentCardsWithFeedbackProps = {
  currentPageContents: ContentItem[];
  layout: ContentCardLayout;
  handleToggleFeedback: (collapsed: boolean, contentId: string, status: string, cardType: string) => void;
  navigateToOpportunityDetails: (opportunityId: string) => void;
  locale: string;
  contentsFeedback: Record<string, ContentFeedbackProps> | null;
  selectedContentId: string;
  feedbackLoading: boolean;
};

export type MyContentPageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUserFactory;
  locale: string;
};

export default function MyContent({ user, locale }) {
  const { errorHandler, client } = useDependency();
  const SubmittedContentServiceInstance = useMemo(() => new SubmittedContentService(client), [client]);
  const router = useRouter();
  const { dispatch, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const [isLoading, setIsLoading] = useState(false);
  const [pages, setPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(null);
  const [currentPageContents, setCurrentPageContents] = useState(null);
  const [contents, setContents] = useState({});
  const { t } = useTranslation([
    "common",
    "dashboard",
    "my-content",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { layout, dashboardLabels, notificationsLabels, myContentLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      dashboardLabels: labelsDashboard(t),
      layout: { ...labelsCommon(t), footer: { locale: router.locale, labels: labelsCommon(t).footer } },
      notificationsLabels: notificationBellLabels,
      myContentLabels: labelsMyContent(t)
    };
    return labels;
  }, [t]);

  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    signout,
    notifications,
    opportunities,
    myContent,
    documentation
  } = layout.header;
  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const labels = ({
    commonPageLabels: {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      myProfile,
      signout,
      how,
      reward,
      perks,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy,
      notifications,
      opportunities,
      myContent,
      documentation
    },
    notificationsBellLabels: notificationsLabels
  } as unknown) as { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };

  const footerLabels: FooterPageLabels = {
    commonPageLabels: {
      dashboard: dashboard,
      opportunities: opportunities,
      myContent: myContent,
      documentation: documentation,
      faq: faq,
      legal: legal,
      disclaimer: disclaimer,
      updates: updates,
      terms: terms,
      privacy: privacy,
      rights: rights,
      report: report,
      faqs: faqs,
      disclosure: disclosure,
      policy: policy,
      policies: policies
    }
  };

  const stableDispatch = useCallback(dispatch, [dispatch]);
  const [contentsFeedback, setContentsFeedback] = useState(null);
  const [selectedContentId, setSelectedContentId] = useState("");
  const [feedbackLoading, setFeedbackLoading] = useState(false);

  const formatSubmittedContent = (contents) => {
    const formattedContent = contents;
    formattedContent.forEach((content) => (content.contentTypeLabel = myContentLabels[content.contentType]));
    setCurrentPageContents(formattedContent);
  };

  useEffect(() => {
    function getSubmittedContents() {
      if (!router.isReady) return;
      let currentPage = DEFAULT_PAGE_NUMBER;
      if (parseInt(router.query?.page as string) > 0) {
        currentPage = parseInt(router.query.page as string);
      }
      setIsLoading(true);
      SubmittedContentServiceInstance.getSubmittedContentsWithProgramCode(PAGE_SIZE, currentPage)
        .then((res) => {
          setIsLoading(false);
          if (res.data && res.data.total > 0) {
            const pages = [];
            for (let i = 0; i < Math.ceil(res.data.total / PAGE_SIZE); i++) {
              pages.push(i + 1);
            }
            setPages(pages);
            setContents({ ...contents, [currentPage]: res.data.contents });
            setCurrentPage(currentPage);
            formatSubmittedContent(res.data.contents);
          }
        })
        .catch((e) => {
          setIsLoading(false);
          dispatch({ type: HAS_EXCEPTION, data: e?.response?.status });
        });
    }
    getSubmittedContents();
  }, [router.isReady]);

  useEffect(() => {
    if (currentPage > 0) {
      if (contents[currentPage] && contents[currentPage].length > 0) {
        setCurrentPageContents(contents[currentPage]);
      } else {
        setIsLoading(true);
        SubmittedContentServiceInstance.getSubmittedContentsWithProgramCode(PAGE_SIZE, currentPage)
          .then((res) => {
            setIsLoading(false);
            if (res.data && res.status === 200 && res.data.total > 0) {
              setContents({ ...contents, [currentPage]: res.data.contents });
              formatSubmittedContent(res.data.contents);
            }
          })
          .catch((e) => dispatch({ type: HAS_EXCEPTION, data: e?.response?.status }));
      }
    }
  }, [currentPage]);

  useEffect(() => {
    if (user) dispatch({ type: SESSION_USER, data: user });
  }, [user]);

  const navigateToOpportunityDetails = (opportunityId) => {
    router.push(`/opportunities/${opportunityId}`);
  };
  const onToggleFeedback = async (collapsed, contentId, status, cardType) => {
    setSelectedContentId(contentId);
    if (collapsed == false) return;
    if (status === "REJECTED" || status === "APPROVED") return;
    const currentFeedback = contentsFeedback?.[contentId]
      ? contentsFeedback?.[contentId]
      : await getFeedbacks({ contentId }, cardType);
    setContentsFeedback({ ...contentsFeedback, [contentId]: currentFeedback });
    setFeedbackLoading(false);
  };

  const getFeedbacks = async (criteria, type) => {
    setFeedbackLoading(true);
    try {
      const response = await SubmittedContentServiceInstance.getContentsFeedback(criteria);
      return response.data.contentsFeedback.slice(0, 1).map((feedback) => ({
        ...feedback,
        ...layout.contentCard,
        sentOnLabel: layout.contentCard.sentOn,
        fromLabel: layout.contentCard.from,
        title: layout.contentCard.changesRequired,
        contentVersion: feedback.contentVersion,
        lastUpdateDate: `${((feedback as unknown) as ContentsFeedback).formattedSubmittedDate(locale)}`,
        content: feedback.description,
        note: t("common:additionalDescription", {
          ...{
            contentType: type === "FILE" ? type.toLowerCase() : layout.contentCard.url?.toLowerCase(),
            updateType:
              type === "FILE" ? layout.buttons.upload?.toLowerCase() : layout.contentCard.update?.toLowerCase()
          }
        })
      }));
    } catch (e) {
      setFeedbackLoading(false);
      errorHandler(stableDispatch, e);
    }
  };

  if (exceptionCode) {
    return <ErrorPage statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.myContent} tabTitle={`${layout.theSims} | ${layout.header.myContent}`}>
        <Header labels={labels} user={user} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={!!user}>
        {isLoading && <Loading />}
        <div className="my-content-container">
          <div className="my-content-full-screen">
            <h3 className="my-content-title">{layout.header.myContent}</h3>
            <div className="my-content-sub-title">{dashboardLabels.myContentDescription}</div>
            <div className={classNames("my-content-list-container", { "empty-list": !currentPageContents })}>
              {currentPageContents ? (
                <div className="my-content-list my-content-list-remove-padding-x">
                  <ContentCardsWithFeedback
                    currentPageContents={currentPageContents}
                    layout={layout}
                    handleToggleFeedback={onToggleFeedback}
                    navigateToOpportunityDetails={navigateToOpportunityDetails}
                    locale={router.locale}
                    contentsFeedback={contentsFeedback}
                    selectedContentId={selectedContentId}
                    feedbackLoading={feedbackLoading}
                  />
                </div>
              ) : (
                <div className="my-content-no-content">
                  <div className="my-content-no-content-row1">
                    <img src="/img/dashboard/light-bulb.svg" alt="icon" className="no-my-content-icon" />
                    <div>
                      <div className="my-content-no-content-title">{dashboardLabels.noSubmittedContent}</div>
                      <div className="my-content-no-content-desc">
                        {dashboardLabels.noSubmittedContentDesc}{" "}
                        <Link href="/opportunities" className="dashboard-opportunities-no-content-desc-link">
                          {layout.header.opportunities}
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            {pages && pages.length > 1 && (
              <Pagination
                next={layout.buttons.next}
                prev={layout.buttons.prev}
                pages={pages}
                currentPage={currentPage}
                onPageChange={(page) => setCurrentPage(page)}
              />
            )}
          </div>
        </div>
      </LayoutBody>
      <LayoutFooter>
        <Footer locale={locale} labels={footerLabels} analytics={undefined} />
      </LayoutFooter>
    </Layout>
  );
}

const ContentCardsWithFeedback = memo(function ContentCardsWithFeedback({
  currentPageContents,
  layout,
  handleToggleFeedback,
  navigateToOpportunityDetails,
  locale,
  contentsFeedback,
  selectedContentId,
  feedbackLoading
}: ContentCardsWithFeedbackProps) {
  return (
    <div>
      {currentPageContents.map((item, index) => (
        <div key={item.id}>
          <ContentCard
            key={index}
            content={
              ({
                ...item,
                reviewFinalRemark: item.reviewFinalRemark
                  ? {
                      ...item.reviewFinalRemark,
                      date: item.formattedReviewFinalRemarkDate(locale)
                    }
                  : item.reviewFinalRemark
              } as unknown) as Content
            }
            labels={layout.contentCard}
            accountType={
              item.type
                ? item.type
                : item.sourceType === "USER_DEVICE" || item.sourceType === "IN_GAME_LISTING"
                ? "UPLOAD"
                : (item.sourceType as AccountType)
            } /* type will be present only when SOURCE_TYPE is 'SOCIAL'.
          When it's a website or file content, type will be null and Content Card we'll have to rely on sourceType attribute instead of type. */
            opportunityClickHandler={() => {
              navigateToOpportunityDetails(item.opportunityId);
            }}
            submittedDate={`${item.formattedSubmittedDate(locale)}`}
            changesRequested={item.requiresChanges()}
            handleToggleFeedback={(collapsed) =>
              handleToggleFeedback(
                collapsed,
                item.id,
                item.status,
                item.type
                  ? item.type
                  : item.sourceType === "USER_DEVICE" || item.sourceType === "IN_GAME_LISTING"
                  ? "FILE"
                  : item.sourceType
              )
            }
            feedback={{
              ...contentsFeedback?.[item.id]?.[0]
            }}
            isLoading={item.id === selectedContentId ? feedbackLoading : false}
            isMcrEnabled={true}
          />
        </div>
      ))}
    </div>
  );
});

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(myContentProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<MyContentPageProps>;
};
