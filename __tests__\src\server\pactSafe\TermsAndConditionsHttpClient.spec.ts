import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import TermsAndConditionsHttpClient from "@src/server/pactSafe/TermsAndConditionsHttpClient";

jest.mock("uuid");

describe("TermsAndConditionsHttpClient", () => {
  beforeEach(() => jest.clearAllMocks());

  it("calls a signed status with program for terms and conditions", async () => {
    const creatorId = "34637843";
    const locale = "en-us";
    const program = "AFFILIATE";
    const client = { get: jest.fn().mockReturnValue({ data: { upToDate: false } }) };
    const signedStatusResponse = new TermsAndConditionsHttpClient((client as unknown) as TraceableHttpClient);

    const application = await signedStatusResponse.signedStatusWithProgram(creatorId, locale, program);

    expect(application).toEqual({ upToDate: false });
    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v2/terms-and-conditions-status/${creatorId}`, {
      query: { locale, program }
    });
  });

  it("calls a signing URL for pact safe", async () => {
    const signerInformation = {
      businessName: "test",
      creatorId: "234324",
      country: "UK",
      email: "",
      firstName: "",
      lastName: "",
      screenName: "",
      locale: "en-us"
    };

    const signerUrl = {
      contractUrl:
        'https://app.pactsafe.com/sign?r=60d22c838ea821120cee5998&s=60c2f746704ffb0e40d92edc&signature=leSvpmgIQMLPi7Ua2Pl4Z0i5AkTRFzO6km3Q2FLMzeSkx9ZIRA7Bb59PCiFsv3vQ4oQTd-4~0kNsOMykCtwi5Vp9Qe9aqmaC~UNTPuwiCnhYSIdkfG88YkWUF1xdFDOZUWkcdPG-~sVLn8MAj8p0vtlYczeydgMsdVHWcRkjBYa3Z~BoqMs1bkt0m7tFovXsh2Aenos3CKaDB118ipQ1CbGmwcdbbWcVptIaqo0ES85cKV-5Cx~vEqnnO18uy6IdAeOsFsbKEQBh2kYFZymGdCgfv65fH6vof9hRaE--TCUnYR~QZm01uO1Cpb02fI320wg1eBWjNWivJucA7-lyrg__","token":"aL0BIJp~PBpgsMm4mte4aG-mdtwVjkxOV4STqA0lxy4_'
    };
    const client = { post: jest.fn().mockReturnValue({ data: signerUrl }) };
    const signerUrlWithTierForPactSafe = new TermsAndConditionsHttpClient((client as unknown) as TraceableHttpClient);

    const application = await signerUrlWithTierForPactSafe.signerUrl(signerInformation);

    expect(application).toEqual(signerUrl);
    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v3/terms-and-conditions/signing-url", { body: signerInformation });
  });
});
