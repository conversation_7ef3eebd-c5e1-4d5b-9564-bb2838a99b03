import { Inject, Service } from "typedi";
import PaymentsHistoryWithProgramCode from "./PaymentsHistoryWithProgramCode";
import { PaymentsCriteria } from "@src/services/paymentsStatistics/PaymentsService";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
export default class PaymentsHistoryWithProgramCodeHttpClient {
  constructor(@Inject("paymentClient") private client: TraceableHttpClient) {}

  async matching(id: string, criteria: PaymentsCriteria): Promise<PaymentsHistoryWithProgramCode> {
    const response = await this.client.get(`/v3/creators/${id}/payment-history`, { query: criteria });
    return Promise.resolve(response.data as PaymentsHistoryWithProgramCode);
  }
}
