.dashboard-container {
  @apply min-h-screen font-text-regular text-font-normal xs:px-meas6 xs:pt-meas10 md:px-meas10 md:pt-meas16 lg:xl:px-meas14;
}
.dashboard-container {
  @apply block h-full w-full;
}
@media screen and (min-width: 768px) {
  .dashboard-container {
    @apply block h-full w-full;
  }
}
@media screen and (min-width: 1280px) {
  .dashboard-container {
    @apply block h-full w-full;
  }
}
.dashboard-full-screen {
  max-width: 1210px;
  margin: auto;
}
.dashboard-wrapper {
  @apply xl:mx-auto xl:max-w-[1140px];
}
.dashboard-sub-title {
  @apply mb-meas16 font-display-bold xs:text-mobile-h1 md:text-tablet-h4 lg:text-desktop-h4;
}
.dashboard-opportunities-tabs-container {
  @apply flex w-full overflow-x-auto whitespace-nowrap;
}

.dashboard-sub-title-my-content {
  @apply mb-meas10 font-display-bold xs:text-mobile-h1 md:text-tablet-h4 lg:text-desktop-h4;
}

.dashboard-opportunities-tabs-container::-webkit-scrollbar {
  @apply hidden;
}
.dashboard-opportunities-tabs-item {
  @apply mr-meas8 cursor-pointer pb-meas2 pl-[6px] pr-meas2 font-text-regular text-font-weak xs:text-mobile-body-large md:text-tablet-body-large lg:mr-meas26 lg:text-desktop-body-large;
}
.dashboard-opportunities-tabs-item-selected {
  @apply border-b-4 border-primary font-bold;
}
.dashboard-tab-content-opportunities {
  @apply mb-meas24 rounded md:border md:border-navy-60;
}
.dashboard-tab-content-my-content {
  @apply mb-meas24 mt-meas2;
}
.dashboard-opportunities-no-content {
  @apply flex flex-col items-center justify-between rounded border border-card-border bg-section-background-1 p-meas12 pb-meas18 text-font-normal xl:flex-row xl:pb-meas18 xl:pl-meas16 xl:pr-meas16 xl:pt-meas20;
}
.dashboard-opportunities-list {
  @apply flex flex-nowrap overflow-x-auto cardSmall:mt-meas0 cardSmall:block md:pl-meas6 md:pr-meas7;
}
.dashboard-content-list {
  @apply px-meas12 pb-meas20;
}
.dashboard-opportunities-icon {
  @apply h-[61px] w-[60px];
}
.dashboard-opportunities-icon {
  @apply self-center;
}
.dashboard-opportunities-no-content-title {
  @apply mb-meas8 mt-meas8 text-center font-text-bold text-desktop-h2 text-font-normal sm:text-desktop-h4 xl:mb-meas4 xl:ml-meas20 xl:mt-meas0 xl:text-left;
}
.dashboard-opportunities-no-content-desc {
  @apply mb-meas12 text-center font-text-regular text-desktop-body-small  text-font-normal sm:text-desktop-body-default md:max-w-[597px] xl:mb-meas0 xl:ml-meas20 xl:text-left;
}
.dashboard-opportunities-no-content-desc-link {
  @apply underline;
}
.dashboard-opportunities-no-content-desc > a {
  @apply underline;
}
.dashboard-opportunities-no-content-row1 {
  @apply flex flex-col items-center xl:flex-row;
}
.opportunity-submit-content {
  @apply mt-meas8 pb-meas0 xl:mt-meas7;
}
.dashboard-content-button {
  @apply m-auto text-center;
}
.dashboard-pagination-container {
  @apply my-meas10;
}
@media screen and (min-width: 768px) {
  .dashboard-opportunities-list-item {
    background-color: transparent;
  }
  .dashboard-opportunities-list-item:last-child {
    border-bottom: 0px;
    padding-bottom: 12px;
  }
}

.dashboard-opportunities-list-item > .opportunity-card-thumbnail-container {
  @apply relative mr-[14.75rem] h-[135px];
}
.dashboard-opportunities-list-item > .opportunity-card-thumbnail-container > .opportunity-card-thumbnail {
  @apply absolute h-[135px] max-h-[135px] w-[221px] max-w-[221px];
}
.dashboard-opportunities-list-item > .opportunity-card-thumbnail-container > .opportunity-card-thumbnail-shadow {
  @apply hidden;
}
.dashboard-opportunities-list-with-perks {
  @apply pb-meas10;
}
.dashboard-opportunities-list-with-perks .opportunity-card-container {
  @apply w-full border-b-0 p-meas0;
}

.dashboard-opportunities-list-with-perks .opportunity-card {
  @apply m-meas0;
}

/**Have to add this selector, since the DOM is nested, we can't handle with .opportunity-card-container:last-child*/
.opportunity-card-link:last-child > .opportunity-card-container {
  @apply mb-meas0;
}

.dashboard-container .opportunity-cardv2-perks-more-icon-mobile,
.dashboard-container .opportunity-cardv2-perks-more-icon-small-mobile {
  @apply rounded;
}

.dashboard-my-content-container {
  @apply pb-[90px] xl:mt-meas6;
}
