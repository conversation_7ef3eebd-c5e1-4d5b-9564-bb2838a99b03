import { Inject, Service } from "typedi";
import AssignedGameCode from "./AssignedGameCode";
import OpportunityParticipantDetails from "./OpportunityParticipantDetails";
import AssignedGameCodeWithStatus from "./AssignedGameCodeWithStatus";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type OpportunityParticipationStatusWithSubmissionStatus = {
  id: string;
  participationId: string;
  status: string;
  invitationId: string;
  hasChangesRequested: true;
  hasNotSubmittedContent: true;
};

export type OpportunityParticipationStatuses = Array<OpportunityParticipationStatusWithSubmissionStatus>;

@Service()
export default class ParticipationsHttpClient {
  constructor(@Inject("opportunityClient") private client: TraceableHttpClient) {}

  async viewAssignedGameCode(id: string): Promise<AssignedGameCode> {
    const response = await this.client.get(`/v1/participations/${id}/game-codes`);
    return AssignedGameCode.fromApi(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Participations/operation/searchParticipationDetails}
   */
  async viewAssignedGameCodes(participationIds: Array<string>): Promise<Array<AssignedGameCodeWithStatus>> {
    const response = await this.client.post(`/v1/assigned-game-codes`, { body: { participationIds } });
    return response.data.assignedGameCodes?.map((data) => AssignedGameCodeWithStatus.fromApi(data));
  }

  async withCreatorCode(creatorId: string, opportunityId: string): Promise<Array<OpportunityParticipantDetails>> {
    const response = await this.client.post(`/v1/creator/${creatorId}/participation-details`, {
      body: [opportunityId]
    });
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Participations/operation/claimAssignedGameCode}
   */
  async claimGameCode(participationId: string): Promise<void> {
    await this.client.put(`/v1/claimed-game-codes/${participationId}`);
    return Promise.resolve();
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Opportunities/operation/getParticipationStatusV3}
   */
  async withSubmissionStatus(
    creatorId: string,
    opportunityIds: Array<string>
  ): Promise<OpportunityParticipationStatuses> {
    const response = await this.client.post(`/v3/opportunities/status/creator/${creatorId}`, {
      body: { opportunityIds }
    });
    return Promise.resolve(response.data);
  }
}
