import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Header from "../components/header/header";
import { useRouter } from "next/router";
import BrowserAnalytics from "../analytics/BrowserAnalytics";
import ProfileLayout from "../components/ProfileLayout";
import labelsCommon from "../config/translations/common";
import labelsLegalDocuments from "../config/translations/legal-documents";
import labelsProfile from "../config/translations/profile";
import labelsFranchisesYouPlay from "../config/translations/franchises-you-play";
import labelsInformation from "../config/translations/information";
import labelsCreatorType from "../config/translations/creator-type";
import labelsCommunicationPreferences from "../config/translations/communication-preferences";
import labelsPointOfContact from "../config/translations/point-of-contact";
import labelsConnectAccounts from "../config/translations/connect-accounts";
import { useDependency } from "../context/DependencyContext";
import { useAppContext } from "../context";
import { ERROR, SESSION_USER, useIsMounted } from "../utils";
import Information from "../components/profile/Information";
import GamePreferences from "../components/profile/GamePreferences";
import CreatorType from "../components/profile/CreatorType";
import LegalDocuments from "../components/profile/LegalDocuments";
import ConnectAccounts from "./connect-accounts";
import CommunicationPreferences from "../components/profile/CommunicationPreferences";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import Error from "./_error";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import Loading from "../components/Loading";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../components/Layout";
import Footer from "../components/footer/ProgramFooter";
import ConnectedAccountsService from "../../src/services/ConnectedAccountsService";
import { mapNotificationsBellLabels } from "../config/translations/mappers/notifications";
import errorLogger from "../../src/serverprops/middleware/ErrorLogger";
import initializeSession from "../../src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "../../src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "../../src/serverprops/middleware/VerifyAccessToProgram";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "../../src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import { createRouter } from "next-connect";
import profileProps from "../../src/serverprops/ProfileProps";

const ProfileContent = memo(function ProfileContent({
  user,
  myProfileView,
  error,
  pages,
  query,
  legalDocumentsLabels,
  infoLabels,
  creator,
  hardwarePartners,
  countries,
  t,
  creatorTypeLabels,
  creatorTypes,
  franchisesYouPlayLabels,
  buttons,
  updateCreator,
  franchises,
  platforms,
  communicationLabels,
  connectAccountslabels,
  layout,
  analytics,
  locale,
  allCountries
}) {
  switch (query.section) {
    case "connected-accounts":
      return <ConnectAccounts {...{ user, myProfileView, error, pages }} />;
    case "legal-documents":
      return <LegalDocuments {...{ legalDocumentsLabels, layout }} />;
    case "creator-type":
      return (
        creator && (
          <CreatorType
            {...{
              t,
              creatorTypeLabels,
              buttons,
              user,
              creator,
              updateCreator,
              creatorTypes,
              layout,
              analytics
            }}
          />
        )
      );
    case "communication-settings":
      return (
        creator && (
          <CommunicationPreferences
            {...{
              translations: communicationLabels,
              buttons,
              labels: connectAccountslabels,
              layout,
              analytics,
              locale
            }}
          />
        )
      );
    case "game-preferences":
      return (
        creator && (
          <GamePreferences
            {...{
              franchisesYouPlayLabels,
              buttons,
              infoLabels,
              user,
              creator,
              updateCreator,
              franchises,
              platforms,
              layout,
              analytics
            }}
          />
        )
      );
    default:
      return (
        creator && (
          <Information
            {...{
              infoLabels,
              buttons,
              user,
              creator,
              updateCreator,
              hardwarePartners,
              countries,
              layout,
              analytics,
              allCountries
            }}
          />
        )
      );
  }
});

export default function Profile({
  user,
  error,
  pages,
  analytics = new BrowserAnalytics(user),
  invalidTikTokScope,
  FLAG_COUNTRIES_BY_TYPE
}) {
  const {
    metadataClient,
    creatorsClient,
    errorHandler,
    configuration: { PROGRAM_CODE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const { dispatch, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { query, locale } = useRouter();
  const isMounted = useIsMounted();
  const [creator, setCreator] = useState(null);
  const [hardwarePartners, setHardwarePartners] = useState(null);
  const [franchises, setFranchises] = useState(null);
  const [platforms, setPlatforms] = useState(null);
  const [creatorTypes, setCreatorTypes] = useState(null);
  const [countries, setCountries] = useState(null);
  const [allCountries, setAllCountries] = useState(null);

  const { t } = useTranslation([
    "common",
    "profile",
    "franchises-you-play",
    "creator-type",
    "information",
    "connect-accounts",
    "notifications",
    "opportunities"
  ]);
  const stableT = useCallback(t, []);
  const {
    layout,
    profileLabels,
    franchisesYouPlayLabels,
    infoLabels,
    communicationLabels,
    legalDocumentsLabels,
    creatorTypeLabels,
    connectAccountslabels,
    pocLabels,
    notificationsLabels
  } = useMemo(() => {
    const commonLabels = labelsCommon(t);
    const labels = {
      layout: commonLabels,
      profileLabels: labelsProfile(t),
      franchisesYouPlayLabels: labelsFranchisesYouPlay(t),
      infoLabels: {
        ...labelsInformation(t),
        header: { calendar: commonLabels.header.calendar },
        profileLabels: { updateAvatar: labelsProfile(t).updateAvatar }
      },
      communicationLabels: labelsCommunicationPreferences(t),
      legalDocumentsLabels: labelsLegalDocuments(t),
      creatorTypeLabels: labelsCreatorType(t),
      connectAccountslabels: labelsConnectAccounts(t),
      pocLabels: labelsPointOfContact(t),
      notificationsLabels: mapNotificationsBellLabels(t)
    };
    labels.layout.footer = { locale: locale, labels: labels.layout.footer };
    return labels;
  }, [t]);

  useEffect(() => {
    async function fetchData() {
      if (isMounted()) {
        try {
          const creator = await creatorService.getCreator(PROGRAM_CODE);
          creator.additionalInformation.hardwarePartners =
            creator.additionalInformation.hardwarePartners?.map((partner) => ({
              ...partner,
              label: partner.name,
              value: partner?.id
            })) || [];
          if (creator.preferredPrimaryPlatform) {
            creator.preferredPrimaryPlatform = {
              ...creator.preferredPrimaryPlatform,
              value: creator.preferredPrimaryPlatform?.id,
              label: creator.preferredPrimaryPlatform.name
            };
          }
          creator.preferredSecondaryPlatforms = creator.preferredSecondaryPlatforms.map((platform) => {
            return {
              ...platform,
              value: platform?.id,
              label: platform.name
            };
          });
          if (creator.preferredPrimaryFranchise) {
            creator.preferredPrimaryFranchise = {
              ...creator.preferredPrimaryFranchise,
              value: creator.preferredPrimaryFranchise?.id,
              label: creator.preferredPrimaryFranchise.name
            };
          }
          creator.preferredSecondaryFranchises =
            creator.preferredSecondaryFranchises?.map((platform) => {
              return {
                ...platform,
                value: platform?.id,
                label: platform.name
              };
            }) || [];
          creator.communicationPreferences.contentLanguages =
            creator.communicationPreferences.contentLanguages?.map((language) => ({
              ...language,
              value: language.code,
              label: language.name
            })) || [];
          creator.communicationPreferences.preferredLanguage = creator.program.preferredLanguage;
          setCreator(creator);
          const hardwarePartners = await metadataService.getHardwarePartners();
          hardwarePartners && setHardwarePartners(hardwarePartners);
          const franchises = await metadataService.getFranchises();
          franchises && setFranchises(franchises);
          const platforms = await metadataService.getPlatformsMatching({ type: "SITE" });
          platforms && setPlatforms(platforms);
          const creatorTypes = await metadataService.getCreatorTypes();
          creatorTypes && setCreatorTypes(creatorTypes);
          const countries = FLAG_COUNTRIES_BY_TYPE
            ? await metadataService.getCountriesMatching({ type: "PAYMENTS" })
            : await metadataService.getCountries();
          countries && setCountries(countries);
          const allCountries = FLAG_COUNTRIES_BY_TYPE
            ? await metadataService.getCountriesMatching()
            : await metadataService.getCountries();
          allCountries && setAllCountries(allCountries);
        } catch (e) {
          errorHandler(stableDispatch, e);
        }
      }
    }
    fetchData();
  }, [stableDispatch, isMounted]);

  useEffect(() => {
    async function removeFbPages() {
      try {
        // Unset FBPages from session.
        await ConnectedAccountsService.clearFbPages();
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    }
    return () => {
      // Cleared the FB pages session on unmount, as the modal comes up even after refreshing the browser
      removeFbPages();
    };
  }, [pages]);

  useEffect(() => {
    if (isMounted()) {
      user && stableDispatch({ type: SESSION_USER, data: user });
    }
    if (invalidTikTokScope) {
      stableDispatch({ type: ERROR, data: layout.main.unhandledError });
    }
  }, [user, stableDispatch]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    opportunities,
    documentation,
    myContent,
    signout,
    notifications
  } = layout.header;

  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const labels = {
    commonPageLabels: {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      opportunities,
      myContent,
      documentation,
      myProfile,
      signout,
      how,
      reward,
      perks,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy,
      notifications
    },
    notificationsBellLabels: notificationsLabels
  };

  return (
    <Layout>
      <LayoutHeader
        pageTitle={profileLabels.profilePageTitle}
        tabTitle={`${layout.theSims} | ${profileLabels.profilePageTitle}`}
      >
        <Header user={user} labels={labels} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={true}>
        {(!creator && (
          <div className="profile-loader">
            <Loading />
          </div>
        )) || (
          <ProfileLayout
            {...layout}
            {...{
              profileLabels,
              pocLabels,
              user,
              creator,
              notificationsLabels,
              analytics
            }}
          >
            <ProfileContent
              {...{
                user,
                myProfileView: true,
                error,
                pages,
                query,
                legalDocumentsLabels,
                infoLabels,
                buttons: layout.buttons,
                creator,
                updateCreator: setCreator,
                hardwarePartners,
                countries,
                t: stableT,
                creatorTypeLabels,
                creatorTypes,
                franchisesYouPlayLabels,
                franchises,
                platforms,
                communicationLabels,
                connectAccountslabels,
                layout,
                analytics,
                locale,
                updateAvatar: profileLabels.updateAvatar,
                allCountries
              }}
            />
          </ProfileLayout>
        )}
      </LayoutBody>
      <LayoutFooter>
        <Footer labels={labels} analytics={undefined} locale={locale} />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(profileProps(locale));

  return await router.run(req, res);
};
