import React from "react";
import SubCategoryCard, { SubCategory } from "@components/SubCategoryCard/SubCategoryCard";
import { SystemInformation } from "@components/RichText/RichText";

export type CategoryType = "Category";
export type Category = {
  sys: SystemInformation;
  title: string;
  description: string;
  internalName: string;
  subCategories: SubCategory[];
  entryType: CategoryType;
};

const CategoryCard = ({ title, description, subCategories }: Category) => {
  return (
    <div className="category-card-container">
      <h1 className="category-card-heading">{title}</h1>
      <p className="category-card-description">{description}</p>
      <div className="category-card-cards-container">
        {subCategories.map((subCategory, index) => (
          <SubCategoryCard key={`card-${index}`} {...subCategory} />
        ))}
      </div>
    </div>
  );
};

export default CategoryCard;
