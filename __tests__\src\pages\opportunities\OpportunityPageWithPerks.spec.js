import "reflect-metadata";
import React from "react";
import OpportunityPageWithPerks from "@components/pages/OpportunityPageWithPerks";
import {
  anOpportunityWithPaymentDetails,
  aOpportunityWithPerksAndContentSubmissionWithDeliverables
} from "../../../factories/opportunities/OpportunityWithPerks";
import { anOpportunityGameCode } from "../../../factories/opportunities/OpportunityGameCode";
import { render, screen, waitFor, within } from "@testing-library/react";
import opportunitiesLabels from "../../../translations/opportunities";
import layout from "../../../translations/common";
import userEvent from "@testing-library/user-event";
import { aCreatorWithExpiredAccounts } from "../../../factories/creators/CreatorWithExpiredAccount";
import { contentSubmissionTabLabels } from "../../../translations/utils";
import { myContentTranslations } from "../../../translations";
import { renderWithToast, triggerAnimationEnd } from "../../../helpers/toast";
import { mockMatchMedia } from "../../../helpers/window";
import { useRouter } from "next/router";
import { aParticipationDetails } from "../../../factories/opportunities/ParticipationDetails";
import { aCreatorCode } from "../../../factories/opportunities/CreatorCode";
import { anOpportunityEvent } from "../../../factories/opportunities/OpportunityEvent";
import { paymentBannerTranslations } from "../../../translations/payment-information";
import { aCreatorWithCreatorCode } from "../../../factories/creators/CreatorWithCreatorCode";
import { renderPage } from "../../../helpers/page";
import { aPointOfContact } from "../../../factories/creators/PointOfContact";
import OpportunityService, {
  OpportunityParticipantDetails,
  OpportunityWithDeliverables,
  OpportunityWithPaymentDetails
} from "../../../../src/services/OpportunityService";
import { useDetectScreen } from "../../../../src/utils";
import { aCreatorWithFlaggedStatus } from "../../../factories/creators/CreatorWithFlaggedStatus";
import { anAccountInformationWithFlaggedStatus } from "../../../factories/creators/AccountInformationWithFlaggedStatus";
import { aContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityContentSubmissionWithDeliverables";
import { aSubmittedContentWithDeliverable } from "../../../factories/opportunities/SubmittedContentWithDeliverable";
import { aDeliverable } from "../../../factories/deliverable/Deliverable";
import { aPrimaryPlatform } from "../../../factories/platforms/PreferredPlatform";
import { aConnectedAccount } from "../../../factories/creators/ConnectedAccounts";
import SubmittedContentService, {
  SubmittedContentWithDeliverableDetail
} from "../../../../src/services/SubmittedContentService";
import OperationsService from "../../../../src/services/OperationsService";
import { delay } from "../../../helpers/timer";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../../../src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { aPerk } from "@eait-playerexp-cn/metadata-test-fixtures";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../../src/context/DependencyContext");
jest.mock("../../../../src/services/OpportunityService");
jest.mock("../../../../src/services/OperationsService");
jest.mock("../../../../src/services/SubmittedContentService");
jest.mock("../../../../src/utils", () => ({
  ...jest.requireActual("../../../../src/utils"),
  onToastClose: jest.fn(),
  useDetectScreen: jest.fn().mockImplementation(() => false)
}));

describe("OpportunityPageWithPerks", () => {
  mockMatchMedia();
  let participationStatusResponse;
  const user = { id: "GAURAV123" };
  const analytics = { viewedOpportunityDetails: jest.fn(), clickedDeliverablesTab: jest.fn() };
  const opportunityPageWithPerksProps = {
    user,
    WATERMARKS_URL: "",
    opportunitiesLabels,
    contentSubmissionTabLabels,
    myContentLabels: myContentTranslations,
    layout: { ...layout, footer: { labels: layout.footer } },
    analytics,
    paymentBannerLabels: paymentBannerTranslations,
    t: jest.fn(),
    pocLabels: opportunitiesLabels.pocLabels
  };
  const router = {
    isReady: true,
    query: { id: "OPPO123" },
    locale: "en-us"
  };
  const backButtonPages = [
    ["Dashboard", "/dashboard"],
    ["Payment Information Page", "profile?section=payment-information"],
    ["My Content", "/my-content"],
    ["Notifications", "/notifications"]
  ];
  const opportunity = new OpportunityWithDeliverables(
    aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      id: "OPPO123",
      title: "Fifa",
      hasGameCodes: true,
      hasDeliverables: true,
      gamesCodes: null,
      perks: [],
      registrationPeriod: { endDate: LocalizedDate.epochPlusDays(5) },
      contentSubmission: {
        socialMedia: {},
        submissionWindow: {
          endDate: LocalizedDate.epochPlusMonths(2)
        },
        guidelines:
          "<p>Madden NFL is an American football video game series developed by EA Tiburon for EA Sports. It is named after Pro Football Hall of Fame coach.Madden NFL is an American football video game series developed by EA Tiburon for EA Sports. It is named after Pro Football Hall of Fame coach.Madden NFL is an American football video game series developed by EA Tiburon for EA Sports. It is named after Pro Football Hall of Fame coach.Madden NFL is an American football video game series developed by EA Tiburon for EA Sports. It is named after Pro Football Hall of Fame coach.Madden NFL is an American football video game series developed by EA Tiburon for EA Sports. It is named af</p><p><br></p><ul><li>Lorem ipsum dolor sit amet, consectetur adipiscing elit</li><li>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</li><li>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</li></ul><p><br></p><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore </p>"
      }
    })
  );
  const creator = aCreatorWithCreatorCode({ id: user.id });
  const description =
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam eros turpis, suscipit in lacus vel, porttitor gravida sapien. Curabitur ut lorem sit amet est tincidunt volutpat. Ut quis justo sed nunc pellentesque vestibulum. Cras commodo, eros eu commodo tristique, purus arcu malesuada lectus, at facilisis lorem leo eget ex. Proin congue magna gravida condimentum molestie. Nam venenatis vel mauris vehicula consequat. Maecenas ullamcorper rutrum lorem eget vestibulum. Curabitur egestas, sem quis scelerisque facilisis, tortor nisi pulvinar orci, vel bibendum eros felis sed dui. Interdum et malesuada fames ac ante ipsum primis in faucibus. Etiam nisl enim, tristique et luctus eget, vehicula sit amet augue. In hac habitasse platea dictumst. Nullam ut leo ut ante porta varius sit amet ac diam. Pellentesque consequat mauris mattis molestie vestibulum. Cras lobortis diam at euismod porttitor. Morbi et metus vitae ligula hendrerit volutpat.";
  const screenSizes = [
    [10000, description, description.substring(0, 900)],
    [1279, description, description.substring(0, 750)],
    [767, description, description.substring(0, 600)]
  ];
  let submittedContent = {
    contents: [],
    count: 0,
    total: 0
  };
  participationStatusResponse = [
    new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
  ];
  const errorHandler = jest.fn();
  const submittedContentService = {
    getSubmittedContents: jest.fn()
  };
  const operationsService = {
    viewAssignedGameCode: jest.fn()
  };
  const opportunityService = {
    getOpportunityWithPaymentDetails: jest.fn().mockResolvedValue({
      data: {}
    }),
    declineInvitation: jest.fn(),
    getOpportunitiesParticipationStatus: jest.fn(),
    getOpportunityWithDeliverables: jest.fn(),
    getParticipationDetails: jest.fn().mockResolvedValue({
      data: participationStatusResponse
    })
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    useDependency.mockReturnValue({
      metadataClient: {},
      errorHandler
    });
    MetadataService.mockReturnValue({
      getContentTypes: jest.fn().mockResolvedValue([{ value: "video", label: "Video" }])
    });
    SubmittedContentService.mockImplementation(() => submittedContentService);
    OpportunityService.mockImplementation(() => opportunityService);
    OperationsService.mockImplementation(() => operationsService);
  });

  xit("shows content Deliverables tab for joined opportunity", async () => {
    opportunityService.getParticipationDetails.mockResolvedValueOnce({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    submittedContentService.getSubmittedContents.mockResolvedValueOnce({
      data: submittedContent
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValueOnce({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: true,
            hasGameCodes: false
          })
        ),
        creator
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

    await waitFor(() => {
      expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1);
      expect(screen.getByText("Deliverables")).toBeInTheDocument();
      expect(screen.queryByText("Content Submission (0)")).not.toBeInTheDocument();
    });
  });

  xit("shows content Deliverables tab before joining an opportunity", async () => {
    submittedContentService.getSubmittedContents.mockResolvedValueOnce({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValueOnce({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "INVITED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValueOnce({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: true,
            hasGameCodes: false
          })
        ),
        creator
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

    await waitFor(() => {
      expect(screen.getByText("Deliverables")).toBeInTheDocument();
      expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1);
      expect(screen.queryByText("Content Submission (0)")).not.toBeInTheDocument();
    });
  });

  xit("redirect to Deliverables tab on click of submit content button", async () => {
    const participationStatusResponse = [
      new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
    ];
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationStatusResponse
    });
    const getSubmitContentResponse = {
      contents: [],
      count: 0,
      total: 0
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: getSubmitContentResponse
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: true,
            hasGameCodes: false
          })
        ),
        creator
      }
    });
    const analytics = {
      startedContentSubmissionFlow: jest.fn(),
      viewedOpportunityDetails: jest.fn()
    };

    render(
      <OpportunityPageWithPerks
        {...opportunityPageWithPerksProps}
        analytics={analytics}
        pocLabels={opportunitiesLabels.pocLabels}
      />
    );
    const submitContentButton = await screen.findByRole("button", { name: "Submit Content" });

    await userEvent.click(submitContentButton);

    await waitFor(() => {
      expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1);
      expect(analytics.startedContentSubmissionFlow).toHaveBeenCalledTimes(1);
      expect(screen.getByText("Deliverables")).toHaveClass("tab-item-selected tab-item");
      expect(submittedContentService.getSubmittedContents).toHaveBeenCalledTimes(1); // We are expecting that after clicking on submit button this api will be called to fetch the latest submitted content and list is refreshed
    });
  });

  xit("displays no submitted content card when content submission is yet to open", async () => {
    const participationStatusResponse = [
      new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
    ];
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationStatusResponse
    });
    const getSubmitContentResponse = {
      contents: [],
      count: 0,
      total: 0
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: getSubmitContentResponse
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: true,
            hasGameCodes: false,
            registrationPeriod: { endDate: LocalizedDate.epochMinusDays(2) },
            contentSubmission: {
              socialMedia: {},
              submissionWindow: {
                startDate: LocalizedDate.epochPlusDays(2),
                endDate: LocalizedDate.epochPlusDays(5)
              },
              guidelines:
                "<p>Madden NFL is an American football video game series developed by EA Tiburon for EA Sports.</p>"
            }
          })
        ),
        creator
      }
    });
    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);
    expect(await screen.findByRole("button", { name: "Submit Content" })).toBeDisabled();

    await userEvent.click(screen.getByText("Deliverables"));

    await waitFor(() => {
      expect(
        screen.getByText("Madden NFL is an American football video game series developed by EA Tiburon for EA Sports.")
      ).toBeInTheDocument();
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.submissionOpensWithNoContent)
      ).toBeInTheDocument();
    });
  });

  xit("shows Content deliverables section on deliverable tab", async () => {
    const participationStatusResponse = [
      new OpportunityParticipantDetails({ participationId: "12", status: "invited", id: "OPPO123" })
    ];
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: true,
            hasGameCodes: false,
            registrationPeriod: { endDate: LocalizedDate.epochPlusDays(2) },
            contentSubmission: aContentSubmissionWithDeliverables({
              deliverables: [
                aDeliverable({
                  socialAccountTypes: ["YOUTUBE"]
                }),
                aDeliverable({
                  socialAccountTypes: ["FACEBOOK"]
                })
              ]
            })
          })
        ),
        creator
      }
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationStatusResponse
    });
    const getSubmitContentResponse = {
      contents: [],
      count: 0,
      total: 0
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: getSubmitContentResponse
    });
    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await userEvent.click(await screen.findByText("Deliverables"));

    await waitFor(() => {
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.contentDeliverables)
      ).toBeInTheDocument();
      expect(
        screen.getByText(contentSubmissionTabLabels.contentSubmissionLabels.deliverablesInstruction)
      ).toBeInTheDocument();
      expect(screen.getAllByTestId("deliverable-card-container")).toHaveLength(2);
    });
  });

  xit("logs 'Clicked Deliverables tab' event when user clicks the 'Deliverables' tab", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false
      })
    );
    submittedContentService.getSubmittedContents.mockResolvedValueOnce({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValueOnce({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "INVITED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

    await userEvent.click(await screen.findByText("Deliverables"));

    await waitFor(() => {
      expect(analytics.clickedDeliverablesTab).toHaveBeenCalledTimes(1);
      expect(analytics.clickedDeliverablesTab).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
    });
  });

  xit("hides submitted content count for joined opportunity", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false
      })
    );
    submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(aSubmittedContentWithDeliverable({ deliverableId: "abcxyz123" })),
        new SubmittedContentWithDeliverableDetail(aSubmittedContentWithDeliverable({ deliverableId: "abcxyz123" })),
        new SubmittedContentWithDeliverableDetail(aSubmittedContentWithDeliverable({ deliverableId: "abcxyz123" }))
      ],
      count: 1,
      total: 3
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

    await waitFor(() => {
      expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1);
      expect(screen.queryByText("Content Submission (3)")).not.toBeInTheDocument();
      expect(screen.queryByText("Deliverables (3)")).not.toBeInTheDocument();
      expect(screen.queryByText("Deliverables")).toBeInTheDocument();
    });
  });

  xit("append submitted content to deliverable", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [aDeliverable({ id: "1234", type: "UNLIMITED" }), aDeliverable({ id: "45678", type: "SINGLE" })]
        })
      })
    );
    submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(aSubmittedContentWithDeliverable({ deliverableId: "1234" })),
        new SubmittedContentWithDeliverableDetail(aSubmittedContentWithDeliverable({ deliverableId: "1234" }))
      ],
      count: 1,
      total: 2
    };

    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

    await waitFor(() => {
      expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
    });
  });

  xit("shows cached submitted content when clicking on prev page button for a deliverable", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [aDeliverable({ id: "1234", type: "UNLIMITED", format: "WEBSITE", socialAccountTypes: null })]
        })
      })
    );
    const submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 1" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 2" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 3" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 4" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 5" })
        )
      ],
      count: 5,
      total: 10
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });
    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);
    expect(await screen.findByRole("button", { name: "Submit Content" })).toBeEnabled();
    await userEvent.click(screen.getByText("Deliverables"));
    expect(await screen.findByText(/Prev/i)).toBeInTheDocument();
    expect(await screen.findByText(/Next/i)).toBeInTheDocument();
    await userEvent.click(screen.getByText(/Next/i));
    await waitFor(() => {
      expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
    });

    await userEvent.click(screen.getByText(/Prev/i));

    await waitFor(() => {
      expect(screen.getAllByTestId("deliverable-content-card")).toHaveLength(5);
    });
  });

  xit("shows new page of submitted content when clicking on next page button for a deliverable", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [aDeliverable({ id: "1234", type: "UNLIMITED", format: "WEBSITE", socialAccountTypes: null })]
        })
      })
    );
    const submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 1" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 2" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 3" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 4" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 5" })
        )
      ],
      count: 5,
      total: 10
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });
    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);
    expect(await screen.findByRole("button", { name: "Submit Content" })).toBeEnabled();
    await userEvent.click(screen.getByText("Deliverables"));
    expect(await screen.findByText(/Next/i)).toBeInTheDocument();

    await userEvent.click(screen.getByText(/Next/i));

    await waitFor(() => {
      expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
      expect(screen.getAllByTestId("deliverable-content-card")).toHaveLength(5);
    });
  });

  xit("shows error toast while getting new page of submitted content when clicking on next page button for a deliverable", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [aDeliverable({ id: "1234", type: "UNLIMITED", format: "WEBSITE", socialAccountTypes: null })]
        })
      })
    );
    const submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 1" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 2" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 3" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 4" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "1234", name: "Test 5" })
        )
      ],
      count: 5,
      total: 10
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    const { unmount } = renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />
    );
    expect(await screen.findByRole("button", { name: "Submit Content" })).toBeEnabled();
    await userEvent.click(screen.getByText("Deliverables"));
    submittedContentService.getSubmittedContents.mockRejectedValue({
      response: {
        data: {
          code: "view-aggregate-unknown-aggregate",
          message: "Cannot complete action",
          title: "Not Found",
          type: "https://www.w3.org//Protocols//rfc2616//rfc2616-sec10.html#sec10.4.5",
          status: 404
        },
        status: 404
      }
    });
    expect(await screen.findByText(/Next/i)).toBeInTheDocument();

    await userEvent.click(screen.getByText(/Next/i));

    await waitFor(() => expect(errorHandler).toHaveBeenCalledTimes(1));
    const toastContainer = await screen.findByRole("alert");
    const { getByRole } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent(/Oops! Something has gone wrong/i);
    expect(toastContainer).toBeInTheDocument();
    unmount();
  });

  xit("selects Deliverables tab when the modal to select a Facebook page is shown", async () => {
    let creatorWith3ExpiredAccounts = aCreatorWithExpiredAccounts({
      connectedAccounts: [aConnectedAccount({ name: "EA Test", type: "YOUTUBE" })]
    });
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [aDeliverable({ id: "1234", type: "UNLIMITED", format: "WEBSITE", socialAccountTypes: null })]
        })
      })
    );
    const participationStatus = [
      new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
    ];
    const pages = [{ id: "shjsdjf", accessToken: "terfhj", name: "hari70aTest" }];
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: participationStatus
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator: creatorWith3ExpiredAccounts
      }
    });
    const getSubmitContentResponse = {
      contents: [],
      count: 0,
      total: 0
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: getSubmitContentResponse
    });

    renderWithToast(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} pages={pages} />);

    expect(await screen.findByText("Deliverables")).toHaveClass("tab-item"); // TODO: xit should end with `-selected`
  });

  xit("xit opens only one toast message even if we click on the deliverables tab more than once", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({ id: "abcxyz123", type: "SINGLE", format: "WEBSITE", socialAccountTypes: null }),
            aDeliverable({ id: "abcxyz456", type: "SINGLE", format: "WEBSITE", socialAccountTypes: null })
          ]
        })
      })
    );
    submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "abcxyz123", status: "CHANGE_REQUESTED" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "abcxyz456", status: "CHANGE_REQUESTED" })
        )
      ],
      count: 2,
      total: 2
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });

    const { unmount } = renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />
    );
    const overViewTab = await waitFor(() => {
      expect(screen.getByText("Overview")).toBeInTheDocument();
      return screen.getByText("Overview");
    });
    const deliverablesTab = await waitFor(() => {
      expect(screen.getByText("Deliverables")).toBeInTheDocument();
      return screen.getByText("Deliverables");
    });
    await waitFor(() => {
      expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1);
      expect(screen.getByText("Deliverables")).toBeInTheDocument();
    });
    await userEvent.click(screen.getByText("Deliverables"));
    const toastContainer = await screen.findByRole("alert");
    const { getByRole } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent("Changes Required");
    await userEvent.click(overViewTab);
    expect(toastContainer).toBeInTheDocument();

    await userEvent.click(deliverablesTab);
    expect(await screen.findAllByRole("heading", { name: /Changes Required/i })).toHaveLength(1);
    await userEvent.click(deliverablesTab);
    expect(await screen.findAllByRole("heading", { name: /Changes Required/i })).toHaveLength(1);

    unmount(); // close toast message
  });

  xit("xit re-opens toast message when going back to the Deliverables tab", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        hasGameCodes: false,
        contentSubmission: aContentSubmissionWithDeliverables({
          deliverables: [
            aDeliverable({ id: "abcxyz123", type: "SINGLE", format: "WEBSITE", socialAccountTypes: null }),
            aDeliverable({ id: "abcxyz456", type: "SINGLE", format: "WEBSITE", socialAccountTypes: null })
          ]
        })
      })
    );
    submittedContent = {
      contents: [
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "abcxyz123", status: "CHANGE_REQUESTED" })
        ),
        new SubmittedContentWithDeliverableDetail(
          aSubmittedContentWithDeliverable({ deliverableId: "abcxyz456", status: "CHANGE_REQUESTED" })
        )
      ],
      count: 2,
      total: 2
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submittedContent
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: [
        new OpportunityParticipantDetails({
          participationId: "abc123",
          status: "JOINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ]
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity,
        creator
      }
    });

    const { unmount } = renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />
    );
    const overViewTab = await screen.findByText("Overview");
    const deliverablesTab = await screen.findByText("Deliverables");
    await waitFor(() => expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1));
    await user.click(deliverablesTab);
    const toastContainer = await screen.findByRole("alert");
    const { getByRole, getByText } = within(toastContainer);
    expect(getByRole("heading")).toHaveTextContent("Changes Required");
    await user.click(getByRole("button", { name: /Close/i }));
    triggerAnimationEnd(getByText("Changes Required"));
    await waitFor(() => expect(screen.queryByRole("heading", { name: /Changes Required/i })).not.toBeInTheDocument());
    await user.click(overViewTab);
    await waitFor(() => expect(screen.queryByRole("heading", { name: /Changes Required/i })).not.toBeInTheDocument());

    await user.click(deliverablesTab);

    expect(await screen.findByRole("alert")).toBeInTheDocument();
    expect(await screen.findAllByRole("heading", { name: /Changes Required/i })).toHaveLength(1);
    unmount(); // close toast message
    jest.useRealTimers();
  });

  xit("displays point of contact card when an un-flagged creator has joined the opportunity", async () => {
    const unFlaggedCreator = aCreatorWithFlaggedStatus({
      accountInformation: anAccountInformationWithFlaggedStatus({ isFlagged: false })
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: false,
            hasGameCodes: false,
            pointOfContact: aPointOfContact()
          })
        ),
        creator: unFlaggedCreator
      }
    });

    renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} pocLabels={opportunitiesLabels.pocLabels} />
    );

    await waitFor(() => {
      expect(screen.getByText(opportunitiesLabels.pocLabels.opportunityContact)).toBeInTheDocument();
      expect(screen.getByText(opportunitiesLabels.pocLabels.pointOfContact)).toBeInTheDocument();
    });
  });

  it("hides point of contact card for a flagged creator in a previously completed opportunity", async () => {
    const flaggedCreator = aCreatorWithFlaggedStatus({
      accountInformation: anAccountInformationWithFlaggedStatus({ isFlagged: true })
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({
            hasDeliverables: false,
            hasGameCodes: false,
            pointOfContact: aPointOfContact()
          })
        ),
        creator: flaggedCreator
      }
    });

    renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} pocLabels={opportunitiesLabels.pocLabels} />
    );

    await waitFor(
      () => {
        expect(screen.queryByText(opportunitiesLabels.pocLabels.opportunityContact)).not.toBeInTheDocument();
        expect(screen.queryByText(opportunitiesLabels.pocLabels.pointOfContact)).not.toBeInTheDocument();
      },
      { timeout: 5000 }
    );
  });

  describe("Opportunity Details Page", () => {
    beforeEach(() => {
      jest.clearAllMocks();
      addAPICallsTo();
      useRouter.mockImplementation(() => router);
    });

    xit.each(screenSizes)(
      "adjusts opportunity description length according to screen size %s",
      async (size, desc, shortenedDescription) => {
        useDetectScreen.mockImplementation((width) => width === size);
        const analytics = { viewedOpportunityDetails: jest.fn(), acceptedOpportunityInvitation: jest.fn() };
        const nonPayableCreator = aCreatorWithCreatorCode({ accountInformation: { isPayable: false } });
        const opportunity = new OpportunityWithDeliverables(
          aOpportunityWithPerksAndContentSubmissionWithDeliverables({ description: desc })
        );
        opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
          data: {
            opportunity,
            creator: nonPayableCreator
          }
        });
        const participationStatusResponse = [
          new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
        ];
        opportunityService.getParticipationDetails.mockResolvedValue({
          data: participationStatusResponse
        });
        const getSubmitContentResponse = {
          contents: [],
          count: 0,
          total: 0
        };
        submittedContentService.getSubmittedContents.mockResolvedValue({
          data: getSubmitContentResponse
        });

        render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

        expect(await screen.findByText(shortenedDescription)).toBeInTheDocument();
      }
    );

    xit("shows opportunity detail even when game codes are enabled with no platform and region", async () => {
      const gameCodesResponse = {
        gameCode: { id: "f84e08be-4d0c-4a1e-bf07-05847052d8c8", code: "f42953ac-6c5e-40f0-bff0-25224063e69f" }
      };
      operationsService.viewAssignedGameCode.mockResolvedValue({
        data: gameCodesResponse
      });

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1); // participation status
      });
      expect(await screen.findByRole("link", { name: /Back/i })).toBeInTheDocument();
      expect(await screen.findByRole("heading", { name: "Fifa" })).toBeInTheDocument();
      expect(document.title).toEqual(opportunity.title);
    });

    xit.each(backButtonPages)("navigates back to %s page", async (name, referer) => {
      router.back = jest.fn().mockResolvedValue(true);
      const gameCodesResponse = {
        gameCode: { id: "f84e08be-4d0c-4a1e-bf07-05847052d8c8", code: "f42953ac-6c5e-40f0-bff0-25224063e69f" }
      };
      operationsService.viewAssignedGameCode.mockResolvedValue({
        data: gameCodesResponse
      });
      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} referer={referer} />);
      await waitFor(() => {
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1); // participation status
      });
      await screen.findByRole("link", { name: "Back" });

      await userEvent.click(screen.getByRole("link", { name: "Back" }));

      expect(router.back).toHaveBeenCalledTimes(1);
    });

    xit("shows opportunity detail with game code available platforms", async () => {
      const gameCodesResponse = {
        gameCode: { id: "f84e08be-4d0c-4a1e-bf07-05847052d8c8", code: "f42953ac-6c5e-40f0-bff0-25224063e69f" }
      };

      operationsService.viewAssignedGameCode.mockResolvedValue({
        data: gameCodesResponse
      });

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByText("Free Game Code")).toBeInTheDocument();
        const gameCodeDescription = screen.getByText(
          "This game code is available on the following platforms. Please note you will receive the code immediately after joining."
        );
        expect(gameCodeDescription).toBeInTheDocument();
        // expect(screen.getByText(/xbox/i)).toBeInTheDocument();
      });
    });

    xit("shows opportunity detail with accept and decline option for invited opportunity", async () => {
      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION />);

      await waitFor(() => {
        const joinButton = screen.getAllByText("Join");
        const declineButton = screen.getAllByText("Decline");
        expect(joinButton[1]).toBeInTheDocument();
        expect(joinButton[1]).toBeInstanceOf(HTMLButtonElement);
        expect(declineButton[1]).toBeInTheDocument();
        expect(declineButton[1]).toBeInstanceOf(HTMLButtonElement);
      });
    });

    xit("doesn't show opportunity detail with accept and decline option for Declined opportunity", async () => {
      const participationStatusResponse = [
        new OpportunityParticipantDetails({
          participationId: null,
          status: "DECLINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION />);

      await waitFor(() => {
        expect(screen.queryByText("Join Opportunity")).not.toBeInTheDocument();
        expect(screen.queryByText("Decline")).not.toBeInTheDocument();
        expect(screen.getAllByText(/You’ve Declined this opportunity/i)).toHaveLength(2);
      });
    });

    xit("show 'Incomplete Payment Details' banner in a joined opportunity with payments", async () => {
      const nonPayableCreator = aCreatorWithCreatorCode({ accountInformation: { isPayable: false } });
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({ hasPayments: true })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: nonPayableCreator }
      });
      const participationStatusResponse = [
        new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });

      const analytics = { viewedOpportunityDetails: jest.fn(), acceptedOpportunityInvitation: jest.fn() };

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);

      await waitFor(() => {
        expect(screen.getByText(/please provide your payment information/i)).toBeInTheDocument();
        expect(screen.getByText(/click here/i)).toBeInTheDocument();
      });
    });

    xit("sets 'Payment Settings' tab active on Payment page on click of 'click here' link in payment information banner", async () => {
      router.push = jest.fn().mockResolvedValue(true);
      const nonPayableCreator = aCreatorWithCreatorCode({ accountInformation: { isPayable: false } });
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          hasPayments: true,
          hasGameCodes: false,
          hasDeliverables: false
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: nonPayableCreator }
      });
      const participationStatusResponse = [
        new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });

      const analytics = {
        viewedOpportunityDetails: jest.fn(),
        clickedPaymentSettingsTab: jest.fn(),
        clickedPaymentDetailsIncompleteHelperBanner: jest.fn()
      };
      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);
      await waitFor(() => {
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1); // participation status
      });
      expect(await screen.findByText(/please provide your payment information/i)).toBeInTheDocument();

      await userEvent.click(screen.getByRole("link", { name: /click here/i }));

      await waitFor(() => {
        expect(analytics.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledTimes(1);
        expect(analytics.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledWith({ locale: "en-us" });
      });
    });

    xit("shows additional information when event is enabled and has description", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          event: {
            description: "additional information description"
          },
          hasEvent: true
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({ data: { opportunity, creator } });

      const analytics = { viewedOpportunityDetails: jest.fn(), acceptedOpportunityInvitation: jest.fn() };

      renderPage(
        <OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} DECLINE_INVITATION />
      );

      await waitFor(() => {
        expect(screen.getByText(opportunitiesLabels.additionalInfo)).toBeInTheDocument();
        expect(screen.queryByText(opportunitiesLabels.eventInformation)).not.toBeInTheDocument();
        expect(screen.getByText("additional information description")).toBeInTheDocument();
      });
    });

    it("hides additional information when event is enabled with no description", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({ event: { description: null }, hasEvent: true })
      );

      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({ data: { opportunity, creator } });

      const analytics = { viewedOpportunityDetails: jest.fn(), acceptedOpportunityInvitation: jest.fn() };

      renderPage(
        <OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} DECLINE_INVITATION />
      );

      await waitFor(() => {
        expect(screen.queryByText("Additional Information")).not.toBeInTheDocument();
      });
    });

    xit("shows join and decline button with invitation for a private opportunity", async () => {
      router.push = jest.fn().mockResolvedValue(true);
      const participationStatusResponse = [
        new OpportunityParticipantDetails({
          participationId: null,
          status: "INVITED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });

      const analytics = { viewedOpportunityDetails: jest.fn(), acceptedOpportunityInvitation: jest.fn() };
      renderPage(
        <OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} DECLINE_INVITATION />
      );
      let joinButton;
      await waitFor(() => {
        joinButton = screen.queryAllByRole("button", { name: "Join" });
        const declineButton = screen.queryAllByRole("button", { name: "Decline" });
        expect(joinButton).toHaveLength(2);
        expect(joinButton[1]).toBeInstanceOf(HTMLButtonElement);
        expect(joinButton[1]).toBeEnabled();
        expect(declineButton).toHaveLength(2);
      });

      await userEvent.click(joinButton[1]);

      await waitFor(() => {
        expect(analytics.acceptedOpportunityInvitation).toHaveBeenCalledTimes(1);
        expect(analytics.acceptedOpportunityInvitation).toHaveBeenCalledWith({
          locale: "en-us",
          opportunity
        });
        expect(router.push).toHaveBeenCalledTimes(1);
        expect(router.push).toHaveBeenCalledWith(`/opportunities/${opportunity?.id}/registrations`);
      });
    });

    xit("disables join button for public opportunities with a closed registration window", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          registrationPeriod: { endDate: LocalizedDate.epochMinusDays(1) }
        })
      );
      const participationStatusResponse = [
        new OpportunityParticipantDetails({ participationId: null, status: "NA", id: "OPPO123", invitationId: null })
      ];
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({ data: { opportunity, creator } });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION />);

      await waitFor(() => {
        const joinButton = screen.getAllByText("Join");
        const declineButton = screen.queryByText("Decline");
        expect(joinButton[1]).toBeInTheDocument();
        expect(joinButton[1]).toBeInstanceOf(HTMLButtonElement);
        expect(joinButton[1]).toBeDisabled();
        expect(declineButton).not.toBeInTheDocument();
        expect(declineButton).not.toBeInstanceOf(HTMLButtonElement);
      });
    });

    xit("shows modal when decline button is clicked", async () => {
      const participationStatusResponse = [
        new OpportunityParticipantDetails({
          participationId: null,
          status: "INVITED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);
      await waitFor(() => {
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
      });
      // Wait for opportunity details to render
      await screen.findByRole("heading", { name: opportunity.title });
      const declineButtons = screen.getAllByRole("button", { name: /Decline/i });

      await userEvent.click(declineButtons[0]);

      const modal = await screen.findByRole("dialog");
      const { getByText, getByRole } = within(modal);
      expect(getByText(/Are you sure you want to decline the invitation/i)).toBeInTheDocument();
      expect(getByText(/^Decline$/)).toBeInTheDocument();
      expect(getByText(/Cancel/)).toBeInTheDocument();
      expect(getByRole("button", { name: /Close$/i })).toBeInTheDocument();
    });

    xit("closes modal when 'Cancel' button is clicked", async () => {
      const participationStatusResponse = [
        new OpportunityParticipantDetails({
          participationId: null,
          status: "INVITED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);
      await waitFor(() => {
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
      });
      // Wait for opportunity details to render
      await screen.findByRole("heading", { name: opportunity.title });
      const declineButtons = screen.getAllByRole("button", { name: /Decline/i });
      // Display modal
      await userEvent.click(declineButtons[0]);
      // Wait for modal to render
      const modal = await screen.findByRole("dialog");
      const { getByText, getByRole } = within(modal);
      expect(getByText(/Are you sure you want to decline the invitation/i)).toBeInTheDocument();
      expect(getByText(/^Decline$/)).toBeInTheDocument();
      expect(getByText(/Cancel/)).toBeInTheDocument();
      expect(getByRole("button", { name: /Close/i })).toBeInTheDocument();
      // return modal;
      // Click 'Cancel' on modal
      const cancelButton = await waitFor(() => {
        const { getByText } = within(modal);
        return getByText(/Cancel/);
      });

      await userEvent.click(cancelButton);

      // Verify the modal was closed
      await waitFor(() => expect(screen.queryByRole("dialog")).not.toBeInTheDocument());
    });

    xit("shows declined status in opportunity header for the declined opportunity", async () => {
      const participationStatus = [
        new OpportunityParticipantDetails({
          participationId: null,
          status: "DECLINED",
          id: "OPPO123",
          invitationId: "1234"
        })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatus
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION />);

      await waitFor(() => {
        const declinedStatus = screen.getByText(/^declined$/i);
        expect(declinedStatus).toBeInTheDocument();
      });
    });

    xit("shows the latest opportunity description when OPPORTUNITY_DETAILS feature flag is enabled", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          description:
            "Madden NFL is an American football video game series developed by Electronic Arts Tiburon for Electronic Arts Sports."
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({ data: { opportunity, creator } });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        const oppTitle = screen.getByText(
          "Madden NFL is an American football video game series developed by Electronic Arts Tiburon for Electronic Arts Sports."
        );
        expect(oppTitle).toBeInTheDocument();
        expect(oppTitle).toHaveClass("opportunity-description-content formatted-content");
      });
    });

    xit("declines an invitation to join an opportunity", async () => {
      jest.useFakeTimers();
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({ data: { opportunity, creator } });
      opportunityService.declineInvitation.mockImplementation(() => Promise.resolve());

      const analytics = { viewedOpportunityDetails: jest.fn(), declinedOpportunityInvitation: jest.fn() };
      const { unmount } = renderPage(
        <OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION analytics={analytics} />
      );
      await waitFor(() => {
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
      });
      // Wait for opportunity details to render
      await screen.findByRole("heading", { name: opportunity.title });
      const declineButtons = screen.getAllByRole("button", { name: /Decline/i });
      // Get modal displayed
      await user.click(declineButtons[0]);
      // Wait for modal to render
      const modal = await screen.findByRole("dialog");
      const { getByText, getByRole, findByText } = within(modal);
      expect(getByText(/Are you sure you want to decline the invitation/i)).toBeInTheDocument();
      expect(getByText(/^Decline$/)).toBeInTheDocument();
      expect(getByText(/Cancel/)).toBeInTheDocument();
      expect(getByRole("button", { name: /Close/i })).toBeInTheDocument();
      // Click 'Decline' on modal
      await user.click(await findByText(/^Decline$/));
      expect(analytics.declinedOpportunityInvitation).toHaveBeenCalledTimes(1);
      expect(analytics.declinedOpportunityInvitation).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
      await waitFor(() => {
        // Verify the modal was closed and decline invitation api was called
        expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
        expect(opportunityService.declineInvitation).toHaveBeenCalledTimes(1);
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      });
      const toastContainer = await screen.findByRole("alert");
      const toast = within(toastContainer);
      const toastCloseButton = await waitFor(() => {
        expect(toast.getByText("Invitation Declined")).toBeInTheDocument();
        return toast.getByRole("button", { name: /Close/i });
      });

      await user.click(toastCloseButton);
      triggerAnimationEnd(screen.getByText("Invitation Declined"));

      // Verify card and header were updated to declined
      await waitFor(() => {
        // Perks card appears twice, once for mobile, once for desktop
        expect(screen.getAllByText(/You’ve Declined this opportunity/i)).toHaveLength(2);
        // Check for Opportunity header status
        expect(screen.getByText(/^declined$/i)).toBeInTheDocument();
      });
      unmount();
      jest.useRealTimers();
    });

    xit("logs 'Viewed Opportunity Details' after fetching opportunities", async () => {
      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION />);

      await waitFor(() => {
        expect(analytics.viewedOpportunityDetails).toHaveBeenCalledTimes(1);
        expect(analytics.viewedOpportunityDetails).toHaveBeenCalledWith({
          locale: "en-us",
          opportunity
        });
      });
    });

    it("hides description below `To do` section for a joined opportunity", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({ data: { opportunity, creator } });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(
          screen.queryByText(/If you're interested in joining this opportunity, here is what you'll need to do:/i)
        ).not.toBeInTheDocument();
      });
    });

    xit("renders hero image in full width for a joined opportunity without content submission & game codes", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              hasDeliverables: false,
              hasGameCodes: false
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByAltText("Hero Image")).toHaveClass("opportunity-perks-hero-image-full-width");
      });
    });

    xit("renders hero image in full width for a support a creator opportunity", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: false,
              hasAttachments: true,
              type: "support_a_creator"
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByAltText("Hero Image")).toHaveClass("opportunity-perks-hero-image-full-width");
      });
    });

    xit("shows remote Event details when the event type is 'Remote Event' & creator has joined", async () => {
      const submitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              title: "Fifa",
              type: "marketing_opportunity",
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDaysWithTime(30, 0, 0),
                  endDate: LocalizedDate.epochPlusDays(60),
                  timezone: "IST"
                },
                meetingPassword: "Pass123"
              }),
              hasEvent: true
            })
          ),
          creator: aCreatorWithExpiredAccounts({ id: user.id })
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: submitContentResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        const { getByText } = within(screen.getByText(/Event Time:/i));
        expect(screen.getByRole("heading", { name: opportunitiesLabels.remoteEvent.title })).toBeInTheDocument();
        expect(screen.getByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).toBeInTheDocument();
        expect(screen.getByText("Pass123")).toBeInTheDocument();
        expect(screen.getByText(/Event Time:/i)).toBeInTheDocument();
        expect(getByText(/12:00 AM/)).toBeInTheDocument();
      });
    });

    xit("displays point of contact card when creator has joined the opportunity", async () => {
      const submitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: submitContentResponse
      });
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              pointOfContact: aPointOfContact()
            })
          )
        }
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} pocLabels={opportunitiesLabels.pocLabels} />);

      await waitFor(() => {
        expect(screen.getByText(opportunitiesLabels.pocLabels.opportunityContact)).toBeInTheDocument();
        expect(screen.getByText(opportunitiesLabels.pocLabels.pointOfContact)).toBeInTheDocument();
      });
    });

    it("hides remote Event details when the event type is not a 'Remote Event'", async () => {
      const submitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(30),
                  endDate: LocalizedDate.epochPlusDays(60),
                  timeZone: "GMT"
                },
                type: "In Person"
              }),
              hasEvent: true
            })
          )
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: submitContentResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.queryByRole("heading", { name: opportunitiesLabels.remoteEvent.title })).not.toBeInTheDocument();
        expect(screen.queryByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).not.toBeInTheDocument();
        expect(screen.queryByText("Pass123")).not.toBeInTheDocument();
        expect(screen.queryByText(/Event Time:/i)).not.toBeInTheDocument();
      });
    });

    it("hides remote Event details when the event type is 'Remote Event' but creator has not joined", async () => {
      const submitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              title: "Fifa",
              type: "marketing_opportunity",
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(30),
                  endDate: LocalizedDate.epochPlusDays(60),
                  timeZone: "GMT"
                },
                meetingPassword: "Pass123"
              }),
              hasEvent: true
            })
          ),
          creator: aCreatorWithExpiredAccounts({ id: user.id })
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: null,
            status: "INVITED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: submitContentResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.queryByRole("heading", { name: opportunitiesLabels.remoteEvent.title })).not.toBeInTheDocument();
        expect(screen.queryByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).not.toBeInTheDocument();
        expect(screen.queryByText("Pass123")).not.toBeInTheDocument();
        expect(screen.queryByText(/Event Time:/i)).not.toBeInTheDocument();
      });
    });

    xit("disables remote event when the event is over", async () => {
      const submitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              title: "Fifa",
              type: "marketing_opportunity",
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochMinusDays(50),
                  endDate: LocalizedDate.epochMinusDays(1),
                  timeZone: "GMT"
                },
                meetingPassword: "Pass123"
              }),
              hasEvent: true
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: submitContentResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByRole("heading", { name: opportunitiesLabels.remoteEvent.title })).toBeInTheDocument();
        expect(screen.getByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).toBeInTheDocument();
        expect(screen.getByText("Pass123")).toBeInTheDocument();
        expect(screen.getByText(/This event is over./i)).toBeInTheDocument();
        expect(screen.getByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).toHaveAttribute(
          "aria-disabled",
          "true"
        );
      });
    });

    xit("disables remote event when no event details added yet", async () => {
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              title: "Fifa",
              type: "marketing_opportunity",
              event: anOpportunityEvent({
                eventPeriod: {
                  startDate: LocalizedDate.epochPlusDays(1),
                  endDate: LocalizedDate.epochPlusDays(30),
                  timeZone: "GMT"
                },
                meetingLink: null
              }),
              hasEvent: true
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByRole("heading", { name: opportunitiesLabels.remoteEvent.title })).toBeInTheDocument();
        expect(screen.queryByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).not.toBeInTheDocument();
        expect(screen.queryByText("Pass123")).not.toBeInTheDocument();
        expect(screen.getByText(/Event Time/i)).toBeInTheDocument();
        expect(screen.getByText(opportunitiesLabels.remoteEvent.noEventDetailsAdded)).toBeInTheDocument();
      });
    });

    xit("sorts perks in ascending order", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              title: "Fifa",
              perks: [
                aPerk({ code: "VIP_EVENT", name: "VIP Event" }),
                aPerk({ code: "TRAVEL", name: "Travel" }),
                aPerk({ code: "COLLAB", name: "Collab" })
              ]
            })
          ),
          creator: aCreatorWithExpiredAccounts({ id: user.id })
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: {
          contents: [],
          count: 0,
          total: 0
        }
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByRole("heading", { name: /Perks/i })).toBeInTheDocument();
        const { getAllByRole } = within(
          screen.getByRole("list", {
            name: /Perks/i
          })
        );
        expect(getAllByRole("listitem")).toHaveLength(3);
        expect(getAllByRole("listitem")[0]).toHaveTextContent("Collab");
        expect(getAllByRole("listitem")[1]).toHaveTextContent("Travel");
        expect(getAllByRole("listitem")[2]).toHaveTextContent("VIP Event");
      });
    });

    xit("show perks translated labels for an opportunity", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              id: "OPPO123",
              title: "Fifa",
              perks: [
                aPerk({ code: "VIP_EVENT", name: "VIP Event" }),
                aPerk({ code: "TRAVEL", name: "Travel" }),
                aPerk({ code: "COLLAB", name: "Collab" })
              ]
            })
          ),
          creator: aCreatorWithExpiredAccounts({ id: user.id })
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: {
          contents: [],
          count: 0,
          total: 0
        }
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.getByRole("heading", { name: /Perks/i })).toBeInTheDocument();
        const { getAllByRole } = within(
          screen.getByRole("list", {
            name: /Perks/i
          })
        );
        expect(getAllByRole("listitem")).toHaveLength(3);
        expect(getAllByRole("listitem")[0]).toHaveTextContent("Collab");
        expect(getAllByRole("listitem")[1]).toHaveTextContent("Travel");
        expect(getAllByRole("listitem")[2]).toHaveTextContent("VIP Event");
      });
    });

    xit("shows skeleton loader for content submission card while opportunity participation being retrieved", async () => {
      opportunityService.getOpportunityWithDeliverables.mockImplementation(() =>
        delay(500).then(() =>
          Promise.resolve({
            data: {
              opportunity: new OpportunityWithDeliverables(
                aOpportunityWithPerksAndContentSubmissionWithDeliverables({
                  hasDeliverables: true,
                  hasGameCodes: true
                })
              ),
              creator
            }
          })
        )
      );
      opportunityService.getParticipationDetails.mockImplementation(() =>
        delay(500).then(() =>
          Promise.resolve({
            data: [
              new OpportunityParticipantDetails({
                participationId: "abc123",
                status: "JOINED",
                id: "OPPO123",
                invitationId: "1234"
              })
            ]
          })
        )
      );
      const gameCodesResponse = {
        gameCode: { id: "f84e08be-4d0c-4a1e-bf07-05847052d8c8", code: "f42953ac-6c5e-40f0-bff0-25224063e69f" }
      };
      operationsService.viewAssignedGameCode.mockImplementation(() =>
        delay(500).then(() =>
          Promise.resolve({
            data: gameCodesResponse
          })
        )
      );

      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => expect(screen.queryByRole("img", { name: /Loading/i })).not.toBeInTheDocument(), {
        timeout: 1_730
      });
      await waitFor(
        () => {
          expect(screen.getByTestId("content-submission-button-skeleton")).toBeInTheDocument();
          expect(screen.getByTestId("content-submission-text-skeleton")).toBeInTheDocument();
        },
        { timeout: 1_100 }
      );
    });

    it("hides skeleton loader for content submission card when error occurred in opportunity participation retrieval", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: true
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockRejectedValue({
        response: {}
      });

      await waitFor(() => {
        expect(screen.queryByTestId("content-submission-button-skeleton")).not.toBeInTheDocument();
        expect(screen.queryByTestId("content-submission-text-skeleton")).not.toBeInTheDocument();
      });
    });

    it("hides skeleton loader for content submission card when error occurred in game code retrieval", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: true
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      operationsService.viewAssignedGameCode.mockImplementation(() => Promise.reject());

      await waitFor(() => {
        expect(screen.queryByTestId("content-submission-button-skeleton")).not.toBeInTheDocument();
        expect(screen.queryByTestId("content-submission-text-skeleton")).not.toBeInTheDocument();
      });
    });

    it("doesn't show perks invitation card when creator has joined opportunity & content submission enabled", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: true,
              perks: [aPerk({ code: "COLLAB", name: "Collab" })]
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        expect(screen.queryByRole("heading", { name: "Perks" })).not.toBeInTheDocument();
        expect(screen.queryByText("What you'll get for joining")).not.toBeInTheDocument();
        expect(screen.queryByText("Collab")).not.toBeInTheDocument();
        expect(screen.queryByText("⭐ You’ve been invited to join")).not.toBeInTheDocument();
        expect(screen.queryByRole("heading", { name: "Join" })).not.toBeInTheDocument();
        expect(screen.queryByRole("heading", { name: "Decline" })).not.toBeInTheDocument();
      });
    });

    xit("renders disclosure policy for a support a creator opportunity", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: false,
              hasAttachments: true,
              type: "support_a_creator"
            })
          ),
          creator
        }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: [
          new OpportunityParticipantDetails({
            participationId: "abc123",
            status: "JOINED",
            id: "OPPO123",
            invitationId: "1234"
          })
        ]
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.title)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.subTitle)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.description1)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.description2)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.description3)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.description4)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.description5)).toBeInTheDocument();
      expect(await screen.findByText(opportunitiesLabels.supportACreatorDisclosure.description6)).toBeInTheDocument();
    });

    xit("sets 'Payment Settings' tab active on Payment page on click of 'click here' link in payment information banner", async () => {
      router.push = jest.fn().mockResolvedValue(true);
      const nonPayableCreator = aCreatorWithCreatorCode({ accountInformation: { isPayable: false } });
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          hasPayments: true,
          hasGameCodes: false,
          hasDeliverables: false
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: nonPayableCreator }
      });
      const participationStatusResponse = [
        new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
      ];
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });
      const analytics = {
        viewedOpportunityDetails: jest.fn(),
        clickedPaymentSettingsTab: jest.fn(),
        clickedPaymentDetailsIncompleteHelperBanner: jest.fn()
      };
      renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} analytics={analytics} />);
      await waitFor(() => {
        expect(opportunityService.getOpportunityWithDeliverables).toHaveBeenCalledTimes(1);
        expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1); // participation status
      });
      expect(await screen.findByText(/please provide your payment information/i)).toBeInTheDocument();

      await userEvent.click(screen.getByRole("link", { name: /click here/i }));

      await waitFor(() => {
        expect(analytics.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledTimes(1);
        expect(analytics.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledWith({ locale: "en-us" });
        expect(router.push).toHaveBeenCalledWith("/payment-information?tab=payment-settings");
      });
    });
  });

  function addAPICallsTo() {
    const opportunityService = {
      getOpportunityWithPaymentDetails: jest.fn().mockResolvedValue({
        data: []
      }),
      getParticipationDetails: jest.fn(),
      getOpportunityWithDeliverables: jest.fn().mockResolvedValue({
        data: { opportunity, creator }
      })
    };
    const operationsService = {
      viewAssignedGameCode: jest.fn()
    };
    const participationStatusResponse = [
      new OpportunityParticipantDetails({
        participationId: null,
        status: "INVITED",
        id: "OPPO123",
        invitationId: "1234"
      })
    ];
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationStatusResponse
    });
    const gameCodesResponse = {
      gameCode: { id: "f84e08be-4d0c-4a1e-bf07-05847052d8c8", code: "f42953ac-6c5e-40f0-bff0-25224063e69f" }
    };
    operationsService.viewAssignedGameCode.mockResolvedValue({
      data: gameCodesResponse
    });
    OpportunityService.mockImplementation(() => opportunityService);
    OperationsService.mockImplementation(() => operationsService);
  }
  describe("Submit Content Flow", () => {
    let creatorWithExpiredAccounts;
    const user = { id: "GAURAV123" };

    beforeEach(() => {
      jest.clearAllMocks();
      useRouter.mockImplementation(() => router);
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          title: "Fifa",
          hasGameCodes: true,
          hasDeliverables: true,
          gamesCodes: anOpportunityGameCode(),
          perks: [],
          registrationPeriod: {
            endDate: LocalizedDate.epochPlusMonths(2)
          }
        })
      );
      const gameCodesResponse = {
        gameCode: { id: "f84e08be-4d0c-4a1e-bf07-05847052d8c8", code: "f42953ac-6c5e-40f0-bff0-25224063e69f" }
      };
      const participationStatusResponse = [
        new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
      ];
      creatorWithExpiredAccounts = aCreatorWithExpiredAccounts({ id: user.id });

      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationStatusResponse
      });
      operationsService.viewAssignedGameCode.mockResolvedValue({
        data: gameCodesResponse
      });
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });
    });

    xit("displays creator code card when there is a creator code in participant detail", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          title: "Fifa",
          hasGameCodes: false,
          hasDeliverables: false,
          gamesCodes: null,
          perks: [],
          registrationPeriod: {
            endDate: LocalizedDate.epochPlusMonths(2)
          }
        })
      );
      const participationDetailsResponse = [
        new OpportunityParticipantDetails(
          aParticipationDetails({
            creatorCode: aCreatorCode({
              code: "TESTCREATORCODE1P340",
              activationWindow: {
                startDate: LocalizedDate.epochMinusDays(5),
                endDate: LocalizedDate.epochPlusDays(10),
                timeZone: "GMT"
              }
            })
          })
        )
      ];

      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: creatorWithExpiredAccounts }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationDetailsResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION SUPPORT_A_CREATOR />);

      await waitFor(() => {
        expect(screen.getByText(opportunitiesLabels.creatorCode.title)).toBeInTheDocument();
        expect(screen.getByText(participationDetailsResponse[0].creatorCode.code)).toBeInTheDocument();
        expect(screen.queryByTestId("game-and-creator-code-card-container")).not.toBeInTheDocument();
      });
    });

    xit("displays creator code not yet active message", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          title: "Fifa",
          hasGameCodes: false,
          hasDeliverables: false,
          gamesCodes: null,
          perks: [],
          registrationPeriod: {
            endDate: LocalizedDate.epochPlusMonths(2)
          }
        })
      );
      const participationDetailsResponse = [
        new OpportunityParticipantDetails(
          aParticipationDetails({
            creatorCode: aCreatorCode({
              code: "TESTCREATORCODE1P340",
              activationWindow: {
                startDate: LocalizedDate.epochPlusDays(5),
                endDate: LocalizedDate.epochPlusDays(10),
                timeZone: "GMT"
              }
            })
          })
        )
      ];
      const submitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };

      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: creatorWithExpiredAccounts }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationDetailsResponse
      });
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: submitContentResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION SUPPORT_A_CREATOR />);

      await waitFor(() => {
        expect(screen.getByText(opportunitiesLabels.creatorCode.codeNotYetActiveMessage)).toBeInTheDocument();
      });
    });

    xit("displays creator code expired message", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          title: "Fifa",
          hasGameCodes: false,
          hasDeliverables: false,
          gamesCodes: null,
          perks: [],
          registrationPeriod: {
            endDate: LocalizedDate.epochMinusMonths(2)
          }
        })
      );
      const participationDetailsResponse = [
        new OpportunityParticipantDetails(
          aParticipationDetails({
            creatorCode: aCreatorCode({
              code: "TESTCREATORCODE1P340",
              activationWindow: {
                startDate: LocalizedDate.epochMinusDays(5),
                endDate: LocalizedDate.epochMinusDays(2),
                timeZone: "GMT"
              }
            })
          })
        )
      ];

      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: creatorWithExpiredAccounts }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationDetailsResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION SUPPORT_A_CREATOR />);

      await waitFor(() => {
        expect(screen.getByText(opportunitiesLabels.creatorCode.codeExpiredMessage)).toBeInTheDocument();
      });
    });

    it("hides creator code card when participant detail doesn't have creator code", async () => {
      const opportunity = new OpportunityWithDeliverables(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          title: "Fifa",
          hasGameCodes: false,
          hasDeliverables: false,
          gamesCodes: null,
          perks: [],
          registrationPeriod: {
            endDate: LocalizedDate.epochPlusMonths(2)
          }
        })
      );
      const participationDetailsResponse = [
        new OpportunityParticipantDetails(
          aParticipationDetails({
            creatorCode: aCreatorCode({
              code: null,
              activationWindow: {
                startDate: LocalizedDate.epochMinusDays(5),
                endDate: LocalizedDate.epochPlusDays(10),
                timeZone: "GMT"
              }
            })
          })
        )
      ];
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: creatorWithExpiredAccounts }
      });
      opportunityService.getParticipationDetails.mockResolvedValue({
        data: participationDetailsResponse
      });

      render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} DECLINE_INVITATION />);

      await waitFor(() => {
        expect(screen.queryByText(opportunitiesLabels.creatorCode.title)).not.toBeInTheDocument();
      });
    });

    xit("shows error toast when error occurred in opportunity page", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: true
            })
          ),
          creator
        }
      });
      // Will produce a 404 error when calling GET /api/v4/submitted-content
      submittedContentService.getSubmittedContents.mockImplementation(() => Promise.reject());

      const { unmount } = renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

      await waitFor(() => {
        const toastContainer = screen.getByRole("alert");
        const { getByRole } = within(toastContainer);
        expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong");
      });
      expect(errorHandler).toHaveBeenCalledTimes(1);
      unmount();
    });

    it("shows error toast when TikTok has invalid scope in opportunity page", async () => {
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: {
          opportunity: new OpportunityWithDeliverables(
            aOpportunityWithPerksAndContentSubmissionWithDeliverables({
              hasDeliverables: true
            })
          ),
          creator
        }
      });
      const getSubmitContentResponse = {
        contents: [],
        count: 0,
        total: 0
      };
      submittedContentService.getSubmittedContents.mockResolvedValue({
        data: getSubmitContentResponse
      });

      const { unmount } = renderPage(
        <OpportunityPageWithPerks {...opportunityPageWithPerksProps} invalidTikTokScope />
      );

      await waitFor(() => {
        const toastContainer = screen.getByRole("alert");
        const { getByRole } = within(toastContainer);
        expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong");
      });
      unmount(); // close toast message
    });
  });

  xit("shows the key dates section", async () => {
    renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} pocLabels={opportunitiesLabels.pocLabels} />
    );

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: opportunitiesLabels.keyDates })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: opportunitiesLabels.viewDeliverables })).toBeInTheDocument();
      expect(screen.getByTestId("opportunity-key-dates-list")).toBeInTheDocument();
    });
  });

  xit("selects the 'Deliverables' tab when user clicks the 'View Deliverables' button", async () => {
    useDetectScreen.mockImplementation(() => 1024);
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithPaymentDetails(
          anOpportunityWithPaymentDetails({
            hasDeliverables: true
          })
        ),
        creator
      }
    });

    renderPage(
      <OpportunityPageWithPerks
        {...opportunityPageWithPerksProps}
        pocLabels={opportunitiesLabels.pocLabels}
        analytics={analytics}
      />
    );

    await userEvent.click(await screen.findByRole("button", { name: opportunitiesLabels.viewDeliverables }));

    expect(screen.getByText(/^Deliverables/i)).toHaveClass("tab-item");
    // expect(analytics.clickedDeliverablesTab).toHaveBeenCalledTimes(1);
  });

  xit("hides the 'View Deliverables' button if the opportunity doesn't have content deliverables enabled", async () => {
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithPaymentDetails(
          anOpportunityWithPaymentDetails({
            hasDeliverables: false
          })
        ),
        creator: aCreatorWithExpiredAccounts()
      }
    });
    renderPage(
      <OpportunityPageWithPerks {...opportunityPageWithPerksProps} pocLabels={opportunitiesLabels.pocLabels} />
    );

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: opportunitiesLabels.keyDates })).toBeInTheDocument();
      expect(screen.queryByRole("button", { name: opportunitiesLabels.viewDeliverables })).not.toBeInTheDocument();
      expect(screen.getByTestId("opportunity-key-dates-list")).toBeInTheDocument();
    });
  });

  xit("shows the in-person event section with all the address fields available", async () => {
    const event = {
      type: "Physical Presence",
      address: {
        city: "Hyderabad",
        country: { name: "India" },
        state: "Telangana",
        addressLine1: "11th Cross Road",
        addressLine2: "Plot No.12",
        zipCode: "123456"
      }
    };
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithPaymentDetails(
          anOpportunityWithPaymentDetails({
            hasEvent: true,
            event: anOpportunityEvent(event)
          })
        ),
        creator: aCreatorWithExpiredAccounts()
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: opportunitiesLabels.inPersonEvent })).toBeInTheDocument();
      expect(screen.getByText(opportunitiesLabels.location)).toBeInTheDocument();
      const { getByText } = within(screen.getByTestId("opportunity-in-person-event-location-details"));
      expect(getByText(event.address.country.name)).toBeInTheDocument();
      expect(getByText(`${event.address.city}, ${event.address.state}, ${event.address.zipCode}`)).toBeInTheDocument();
      expect(getByText(`${event.address.addressLine1}, ${event.address.addressLine2}`)).toBeInTheDocument();
    });
  });

  xit("shows the in-person event section with only the required address fields available", async () => {
    const event = {
      type: "Physical Presence",
      address: {
        city: null,
        country: { name: "India" },
        state: null,
        addressLine1: null,
        addressLine2: null,
        zipCode: null
      }
    };
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: {
        opportunity: new OpportunityWithPaymentDetails(
          anOpportunityWithPaymentDetails({
            hasEvent: true,
            event: anOpportunityEvent(event)
          })
        ),
        creator: aCreatorWithExpiredAccounts()
      }
    });

    renderPage(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByRole("heading", { name: opportunitiesLabels.inPersonEvent })).toBeInTheDocument();
      expect(screen.getByText(opportunitiesLabels.location)).toBeInTheDocument();
      const { getByText, queryByText } = within(screen.getByTestId("opportunity-in-person-event-location-details"));
      expect(getByText(event.address.country.name)).toBeInTheDocument();
      expect(
        queryByText(`${event.address.city}, ${event.address.state}, ${event.address.zipCode}`)
      ).not.toBeInTheDocument();
      expect(queryByText(`${event.address.addressLine1}, ${event.address.addressLine2}`)).not.toBeInTheDocument();
    });
  });

  xit("displays creator code card when there is a creator code in participant detail", async () => {
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        id: "OPPO123",
        title: "Fifa",
        hasGameCodes: false,
        hasDeliverables: false,
        gamesCodes: null,
        perks: [],
        registrationPeriod: {
          endDate: LocalizedDate.epochPlusMonths(2)
        }
      })
    );
    const participationDetailsResponse = [
      new OpportunityParticipantDetails(
        aParticipationDetails({
          creatorCode: aCreatorCode({
            code: "TESTCREATORCODE1P340",
            activationWindow: {
              startDate: LocalizedDate.epochMinusDays(5),
              endDate: LocalizedDate.epochPlusDays(10),
              timeZone: "GMT"
            }
          })
        })
      )
    ];
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationDetailsResponse
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByTestId("game-and-creator-code-card-container")).toBeInTheDocument();
      expect(screen.getByText(opportunitiesLabels.creatorCode.title)).toBeInTheDocument();
      expect(screen.getByText(participationDetailsResponse[0].creatorCode.code)).toBeInTheDocument();
    });
  });

  xit("displays creator code not yet active message", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        title: "Fifa",
        hasGameCodes: false,
        hasDeliverables: false,
        gamesCodes: null,
        perks: [],
        registrationPeriod: {
          endDate: LocalizedDate.epochPlusMonths(2)
        }
      })
    );
    const participationDetailsResponse = [
      new OpportunityParticipantDetails(
        aParticipationDetails({
          creatorCode: aCreatorCode({
            code: "TESTCREATORCODE1P340",
            activationWindow: {
              startDate: LocalizedDate.epochPlusDays(5),
              endDate: LocalizedDate.epochPlusDays(10),
              timeZone: "GMT"
            }
          })
        })
      )
    ];
    const submitContentResponse = {
      contents: [],
      count: 0,
      total: 0
    };
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator }
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationDetailsResponse
    });
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: submitContentResponse
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByText(opportunitiesLabels.creatorCode.codeNotYetActiveMessage)).toBeInTheDocument();
    });
  });

  xit("displays creator code expired message", async () => {
    const opportunity = new OpportunityWithDeliverables(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        title: "Fifa",
        hasGameCodes: false,
        hasDeliverables: false,
        gamesCodes: null,
        perks: [],
        registrationPeriod: {
          endDate: LocalizedDate.epochMinusMonths(2)
        }
      })
    );
    const participationDetailsResponse = [
      new OpportunityParticipantDetails(
        aParticipationDetails({
          creatorCode: aCreatorCode({
            code: "TESTCREATORCODE1P340",
            activationWindow: {
              startDate: LocalizedDate.epochMinusDays(5),
              endDate: LocalizedDate.epochMinusDays(2),
              timeZone: "GMT"
            }
          })
        })
      )
    ];
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator }
    });
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationDetailsResponse
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByText(opportunitiesLabels.creatorCode.codeExpiredMessage)).toBeInTheDocument();
    });
  });

  xit("shows payment information for marketing opportunity", async () => {
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        type: "marketing_opportunity",
        payment: {
          amount: 4000
        }
      })
    );
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(opportunityService.getOpportunityWithPaymentDetails).toHaveBeenCalledTimes(1);
      expect(screen.getByText(opportunitiesLabels.payment)).toBeInTheDocument();
      expect(screen.getByText("$4,000.00 USD")).toBeInTheDocument();
    });
  });

  xit("hides payment information for support a creator opportunity", async () => {
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        type: "support_a_creator",
        payment: {
          percentage: 5
        }
      })
    );
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(opportunityService.getOpportunityWithPaymentDetails).toHaveBeenCalledTimes(1);
      expect(screen.queryByText(opportunitiesLabels.payment)).not.toBeInTheDocument();
      expect(
        screen.queryByText(`${opportunity.payment.percentage}${opportunitiesLabels.sales}`)
      ).not.toBeInTheDocument();
    });
  });

  xit("shows game code card", async () => {
    const gameCodesResponse = {
      gameCode: { code: "CODE-CODE-CODE-CODE" },
      platformId: "a0BK000000MP6H5MAL"
    };
    operationsService.viewAssignedGameCode.mockResolvedValue({
      data: gameCodesResponse
    });
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        hasGameCodes: true,
        gameCodes: {
          availability: [{ platform: aPrimaryPlatform({ id: "a0BK000000MP6H5MAL" }), regions: [] }]
        }
      })
    );
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });
    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(opportunityService.getOpportunityWithPaymentDetails).toHaveBeenCalledTimes(1);
      expect(screen.getByText(opportunitiesLabels.getGameCode)).toBeInTheDocument();
      expect(screen.getByText("CODE-CODE-CODE-CODE")).toBeInTheDocument();
    });
  });

  xit("shows game code card when game code is not available", async () => {
    const gameCodesResponse = {
      gameCode: null,
      platformId: "a0BK000000MP6H5MAL"
    };
    operationsService.viewAssignedGameCode.mockResolvedValue({
      data: gameCodesResponse
    });
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        hasGameCodes: true,
        gameCodes: {
          availability: [{ platform: aPrimaryPlatform({ id: "a0BK000000MP6H5MAL" }), regions: [] }]
        }
      })
    );
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(opportunityService.getOpportunityWithPaymentDetails).toHaveBeenCalledTimes(1);
      expect(screen.getByText(opportunitiesLabels.getGameCode)).toBeInTheDocument();
      expect(screen.getByText(opportunitiesLabels.gameCodePending)).toBeInTheDocument();
    });
  });

  xit("shows event information section when event enabled", async () => {
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        type: "marketing_opportunity",
        hasEvent: true,
        event: {
          description: "<h1> Event information added <h1>"
        }
      })
    );
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(opportunityService.getOpportunityWithPaymentDetails).toHaveBeenCalledTimes(1);
      expect(screen.getByText(opportunitiesLabels.eventInformation)).toBeInTheDocument();
      expect(screen.queryByText(opportunitiesLabels.additionalInfo)).not.toBeInTheDocument();
      expect(screen.getByText("Event information added")).toBeInTheDocument();
    });
  });

  xit("shows online event details card for online events for joined opportunities", async () => {
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({
        type: "marketing_opportunity",
        hasEvent: true,
        event: anOpportunityEvent({
          eventPeriod: {
            startDate: LocalizedDate.epochMinusDays(10),
            endDate: LocalizedDate.epochPlusDays(10),
            timeZone: "GMT"
          },
          meetingPassword: "Password",
          meetingLink: "https://zoom.in",
          type: "Remote Event"
        })
      })
    );
    const participationStatusResponse = [
      new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
    ];
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationStatusResponse
    });
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });
    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByText(opportunitiesLabels.remoteEvent.title)).toBeInTheDocument();
      expect(screen.getByText("Password")).toBeInTheDocument();
      expect(screen.getByRole("link", { name: opportunitiesLabels.remoteEvent.joinEvent })).toBeInTheDocument();
    });
  });

  xit("shows opportunity description with title", async () => {
    const description = "This is a test description";
    const opportunity = new OpportunityWithPaymentDetails(
      anOpportunityWithPaymentDetails({ description: description })
    );
    opportunityService.getOpportunityWithPaymentDetails.mockResolvedValue({
      data: { opportunity, creator }
    });
    const participationStatusResponse = [
      new OpportunityParticipantDetails({ participationId: "12", status: "JOINED", id: "OPPO123" })
    ];
    opportunityService.getParticipationDetails.mockResolvedValue({
      data: participationStatusResponse
    });
    const getSubmitContentResponse = {
      contents: [],
      count: 0,
      total: 0
    };
    submittedContentService.getSubmittedContents.mockResolvedValue({
      data: getSubmitContentResponse
    });

    render(<OpportunityPageWithPerks {...opportunityPageWithPerksProps} />);

    await waitFor(() => {
      expect(screen.getByText(opportunitiesLabels.aboutThisOpportunity)).toBeInTheDocument();
      expect(screen.getByText(description)).toBeInTheDocument();
    });
  });
});
