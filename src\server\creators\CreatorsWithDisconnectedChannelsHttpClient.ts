import { Inject, Service } from "typedi";
import CreatorWithExpiredAccounts from "./CreatorWithExpiredAccounts";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithDisconnectedChannelsHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithExpiredAccounts> {
    const response = await this.client.get(`/v2/creators/${id}`);
    return Promise.resolve(CreatorWithExpiredAccounts.fromApi(response.data));
  }
}

export default CreatorsWithDisconnectedChannelsHttpClient;
