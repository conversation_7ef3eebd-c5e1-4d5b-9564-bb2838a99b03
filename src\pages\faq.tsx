import "reflect-metadata";
import { useTranslation } from "next-i18next";
import React, { useEffect, useMemo } from "react";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import { useAppContext } from "@src/context";
import { SESSION_USER } from "@src/utils";
import { useRouter } from "next/router";
import ErrorPage from "./_error";
import BrowserAnalytics, { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import Header from "@components/header/header";
import labelsDashboard from "@src/config/translations/dashboard";
import labelsCommon from "@src/config/translations/common";
import labelsMyContent from "@src/config/translations/my-content";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import { NotificationsLabels, TopNavigationPageLabels } from "@components/ProgramTopNavigation";
import Footer, { FooterPageLabels } from "@src/components/footer/ProgramFooter";
import FaqPage from "@components/FaqPage/FaqPage";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import { GetServerSidePropsResult } from "next";
import faqProps from "@src/serverprops/FaqProps";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";

export type FAQsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  analytics?: BrowserAnalytics;
  faqPage: Record<string, unknown>;
};

/**
 * A component for displaying the FAQs submitted by current logged in user.
 *
 * @param {object} user - a current logged in user details
 * @param {string} locale - a locale to translate the app
 * @returns {JSX.Element}
 */

export default function FAQ({ user, locale, faqPage }) {
  const router = useRouter();
  const { title, body } = faqPage;
  const { dispatch, state: { exceptionCode = null, sessionUser = null } = {} } = useAppContext() || {};
  const { t } = useTranslation([
    "common",
    "dashboard",
    "my-content",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { layout, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      dashboardLabels: labelsDashboard(t),
      layout: { ...labelsCommon(t), footer: { locale: router.locale, labels: labelsCommon(t).footer } },
      notificationsLabels: notificationBellLabels,
      myContentLabels: labelsMyContent(t)
    };
    return labels;
  }, [t]);

  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    signout,
    notifications,
    opportunities,
    myContent,
    documentation
  } = layout.header;
  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const labels = ({
    commonPageLabels: {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      myProfile,
      signout,
      how,
      reward,
      perks,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy,
      notifications,
      opportunities,
      myContent,
      documentation
    },
    notificationsBellLabels: notificationsLabels
  } as unknown) as { commonPageLabels: TopNavigationPageLabels; notificationsBellLabels?: NotificationsLabels };

  const footerLabels: FooterPageLabels = {
    commonPageLabels: {
      dashboard: dashboard,
      opportunities: opportunities,
      myContent: myContent,
      documentation: documentation,
      faq: faq,
      legal: legal,
      disclaimer: disclaimer,
      updates: updates,
      terms: terms,
      privacy: privacy,
      rights: rights,
      report: report,
      faqs: faqs,
      disclosure: disclosure,
      policy: policy,
      policies: policies
    }
  };

  useEffect(() => {
    if (user) dispatch({ type: SESSION_USER, data: user });
  }, [user]);

  if (exceptionCode) {
    return <ErrorPage statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutHeader pageTitle={layout.header.faqs} tabTitle={`${layout.theSims} | ${layout.header.faqs}`}>
        <Header labels={labels} user={user} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={!!user}>
        <FaqPage {...{ title, body }} />
      </LayoutBody>
      <LayoutFooter>
        <Footer locale={locale} labels={footerLabels} analytics={undefined} />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();
  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(faqProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<FAQsProps>;
};
