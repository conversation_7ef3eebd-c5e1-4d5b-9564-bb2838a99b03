import { render, screen } from "@testing-library/react";
import { aCreatorWithFlaggedStatus } from "../../../factories/creators/CreatorWithFlaggedStatus";
import { anAccountInformationWithFlaggedStatus } from "../../../factories/creators/AccountInformationWithFlaggedStatus";
import ProfileMenuTab from "../../../../src/components/profile/ProfileMenuTab";
import { profileLabels } from "../../../translations";
import { useRouter } from "next/router";

jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

xdescribe("Profile Menu Tab", () => {
  const profileMenuWebProps = {
    pocLabels: {},
    profileLabels: profileLabels.default
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => ({
      query: { section: "information" },
      locale: "en-us",
      push: jest.fn()
    }));
  });

  it("displays point of contact section for a non-flagged creator", async () => {
    const creator = aCreatorWithFlaggedStatus({
      accountInformation: anAccountInformationWithFlaggedStatus({ isFlagged: false })
    });

    render(<ProfileMenuTab {...profileMenuWebProps} creator={creator} />);

    expect(screen.getByText(profileMenuWebProps.profileLabels.pointOfContact)).toBeInTheDocument();
  });

  it("does not show point of contact section for a flagged creator", async () => {
    const creator = aCreatorWithFlaggedStatus({
      accountInformation: anAccountInformationWithFlaggedStatus({ isFlagged: true })
    });

    render(<ProfileMenuTab {...profileMenuWebProps} creator={creator} />);

    expect(screen.queryByText(profileMenuWebProps.profileLabels.pointOfContact)).not.toBeInTheDocument();
  });
});
