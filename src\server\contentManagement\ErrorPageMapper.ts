import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type ErrorPageLabels = {
  errorLabels: {
    accountDeactivateTitle: string;
    accountDeactivateSubtitle: string;
    accessErrorTitle: string;
    accessErrorSubtitle: string;
    accessErrorDescription: string;
    accountDeactivated: string;
  };
};

export class ErrorPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): ErrorPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      errorLabels: {
        accountDeactivateTitle: microCopy.get("error.accountDeactivateTitle"),
        accountDeactivateSubtitle: microCopy.get("error.accountDeactivateSubtitle"),
        accessErrorTitle: microCopy.get("error.accessErrorTitle"),
        accessErrorSubtitle: microCopy.get("error.accessErrorSubtitle"),
        accessErrorDescription: microCopy.get("error.accessErrorDescription"),
        accountDeactivated: microCopy.get("error.accountDeactivated")
      }
    };
  }
}
