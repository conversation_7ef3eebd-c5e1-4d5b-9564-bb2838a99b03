import { memo } from "react";
import NavLink from "./header/WebNavLink";
import { HeaderLabels } from "./Layout";

export type ErrorComponentProps = {
  code: number;
  title: string;
  description?: string;
  header?: Partial<HeaderLabels>;
};

export default memo(function ErrorComponent({ code, title, description, header }: ErrorComponentProps) {
  return (
    <div className="error-page">
      <div className="error-container">
        <div className="error-content-container">
          <h1 className="error-heading">{code}</h1>
          <h2 className="error-title">{title} </h2>
          {description && header && (
            <>
              <div className="error-content">{description}</div>
              <div className="error-nav">
                <span>
                  <NavLink title={header.dashboard} href="/dashboard" />
                </span>
                <span>
                  <NavLink title={header.opportunities} href="/opportunities" />
                </span>
                <span>
                  <NavLink title={header.myContent} href="/my-content" />
                </span>
                <span>
                  <NavLink title={header.documentation} href="/documentation" />
                </span>
                <span>
                  <NavLink title={header.faqs} href="/faq" />
                </span>
              </div>
            </>
          )}
        </div>
        <div className="error-character-container"></div>
      </div>
    </div>
  );
});
