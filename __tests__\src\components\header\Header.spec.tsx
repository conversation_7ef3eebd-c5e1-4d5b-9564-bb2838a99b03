import { render, screen } from "@testing-library/react";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import userEvent from "@testing-library/user-event";
import { mockMatchMedia } from "../../../helpers/window";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";
import { useDetectScreen } from "@src/utils";
import Header from "@components/header/header";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { aUser } from "__tests__/factories/User/User";

jest.mock("../../../../src/utils");
jest.mock("../../../../src/context/DependencyContext");

describe("Header", () => {
  mockMatchMedia();
  const notificationsLabels = { viewNotifications: "View Notifications" };
  const mockUseDetectScreen = useDetectScreen as jest.Mock;
  const analytics = ({} as unknown) as BrowserAnalytics;
  const headerProps = {
    labels: ({
      commonPageLabels: { signout: "Sign Out", expand: "Expand", myProfile: "My Profile" }
    } as unknown) as CommonPageLabels,
    user: aUser(),
    interestedCreator: true,
    notificationsLabels,
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      metadataClient: {},
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "the_sims",
        MENU_ITEMS: {
          the_sims: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  it("shows my-profile and signout on hover", async () => {
    mockUseDetectScreen.mockImplementation((width) => width === 1279); // size is smaller than desktop
    render(<Header {...headerProps} />);

    await userEvent.hover(screen.getByTestId(`profile-image`));

    await screen.findByRole("button", { name: /my profile/i });
    expect(screen.getByRole("button", { name: /sign out/i })).toBeInTheDocument();
  });

  it("renders correctly with default analytics", async () => {
    mockUseDetectScreen.mockImplementation((width) => width === 1279);
    render(<Header labels={headerProps.labels} user={headerProps.user} />);

    expect(screen.getByTestId("header-container")).toBeInTheDocument();

    await userEvent.hover(screen.getByTestId(`profile-image`));
    await screen.findByRole("button", { name: /my profile/i });
  });
});
