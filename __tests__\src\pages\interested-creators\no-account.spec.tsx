import React from "react";
import { useRouter } from "next/router";
import { render, screen } from "@testing-library/react";
import { useDependency } from "@src/context/DependencyContext";
import { NoAccountPageLabels } from "@src/server/contentManagement/NoAccountPageMapper";
import Random from "../../../factories/Random";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import BrowserAnalytics, { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import NoAccount from "@src/pages/interested-creators/no-account";
import { anInitialInterestedCreator } from "../../../factories/initialInterestedCreators/InitialInterestedCreator";

jest.mock("@src/context/DependencyContext");
jest.mock("next/router");

describe("NoAccount", () => {
  const locale = "en-us";
  const initialInterestedCreator = {
    nucleusId: Random.nucleusId(),
    originEmail: Random.email(),
    dateOfBirth: LocalizedDate.epochMinusMonths(240).toString(),
    defaultGamerTag: Random.email(),
    analyticsId: Random.uuid()
  };

  const mockNoAccountLabels = {
    title: "No Account Title",
    subTitle: "No Account Subtitle",
    applyNow: "Apply Now",
    exploreTitle: "Explore Title",
    creatorNetwork: "Creator Network",
    availablePerks: "Available Perks",
    subTitlePart1: "Subtitle Part 1",
    subTitlePart2: "Subtitle Part 2",
    descriptionPara1: "Description Paragraph 1",
    descriptionPara2: "Description Paragraph 2",
    descriptionPara3: "Description Paragraph 3",
    howItWorks: "How It Works",
    requestToJoin: "Request to Join"
  };

  const noAccountProps = {
    locale,
    interestedCreator: anInitialInterestedCreator(),
    analytics: ({} as unknown) as BrowserAnalytics,
    user: AuthenticatedUserFactory.fromInterestedCreator(initialInterestedCreator),
    pageLabels: {
      noAccountLabels: mockNoAccountLabels
    } as NoAccountPageLabels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({
      locale
    }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: { APPLICATIONS_MFE_BASE_URL: "http://localhost:3003" }
    });
  });

  it("renders the NoAccount component correctly", () => {
    render(<NoAccount {...noAccountProps} />);

    expect(screen.getByTestId("the-sims-creator-program")).toBeInTheDocument();
    expect(screen.getByTestId("sims-characters")).toBeInTheDocument();
  });

  it("renders the layout structure properly", () => {
    render(<NoAccount {...noAccountProps} />);

    expect(screen.getByTestId("the-sims-creator-program")).toBeInTheDocument();
    expect(screen.getByTestId("sims-characters")).toBeInTheDocument();
  });

  it("renders the expected number of images", () => {
    render(<NoAccount {...noAccountProps} />);

    expect(screen.getAllByRole("img")).toHaveLength(2);
  });
});
