import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import NotificationsPagePropController from "@src/serverprops/controllers/NotificationsPagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations");

describe("NotificationsPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the Notifications page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      headers: { referer: "/opportunities" }
    });
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    req.session = { identity };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (serverSideTranslations as jest.Mock).mockReturnValue({});
    const labels = {};
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new NotificationsPagePropController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "notifications");
    expect(props).toEqual({
      props: {
        referer: "/opportunities",
        runtimeConfiguration: configuration,
        pageLabels: labels,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        }
      }
    });
  });
});
