import "reflect-metadata";
import Loading from "@components/loading/Loading";
import MigrationLayout from "@components/migrations/migration-layout/MigrationLayout";
import { discordIcon, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import { useAppContext } from "@src/context";
import { useDependency } from "@src/context/DependencyContext";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/server/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/server/contentManagement/ConnectAccountsPageMapper";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import Error from "./_error";
import dynamic from "next/dynamic";
import React, { ComponentType, FC, useCallback, useState } from "react";
import { useRouter } from "next/router";
import { USER_NAVIGATED } from "@src/utils";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import verifyIncompleteRegistration from "@src/serverprops/middleware/VerifyIncompleteRegistration";
import communicationPreferencesProps from "@src/serverprops/CommunicationPreferencesProps";

const CommunicationPreferencesStep: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "onboarding/CommunicationPreferencesStep"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type CommunicationPreferencesProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUserFactory;
  pageLabels: CommunicationPreferencesPageLabels & CommonPageLabels & ConnectAccountsPageLabels & BreadcrumbPageLabels;
};
const CommunicationPreferences: FC<CommunicationPreferencesProps> = ({ pageLabels, user }) => {
  const { dispatch, state } = useAppContext();
  const { error: errorToast } = useToast();
  const stableDispatch = useCallback(dispatch, [dispatch]);
  const router = useRouter();
  const { onBoardingClient, creatorsClient, errorHandler, metadataClient, configuration } = useDependency();
  const { PROGRAM_CODE, DEFAULT_AVATAR_IMAGE } = configuration;
  const { exceptionCode = null, sessionUser = null, isLoading } = state;
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showMigration, setShowMigration] = useState(false);
  const [navigateToPage, setNavigateToPage] = useState("");

  const onClose = useCallback(() => {
    setShowConfirmation(!showConfirmation);
  }, [showConfirmation]);

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };
  const {
    communicationPreferencesPageLabels,
    commonPageLabels,
    connectAccountsLabels,
    breadcrumbPageLabels
  } = pageLabels;
  const translation = {
    ...communicationPreferencesPageLabels,
    lables: {
      ...communicationPreferencesPageLabels.labels,
      preferredPhoneNumberDescription: "",
      contentLanguageTitle: ""
    }
  };
  const layout = {
    main: {
      unhandledError: commonPageLabels.unhandledError
    },
    buttons: {
      ...commonPageLabels
    }
  };
  const connectAccounts = {
    ...connectAccountsLabels,
    myAccount: ""
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <MigrationLayout
      pageTitle={communicationPreferencesPageLabels.profileTitle}
      tabTitle={`${commonPageLabels.theSims} | ${commonPageLabels.preferences}`}
      theSims={commonPageLabels.theSims}
      className="onboarding-creator"
      onClose={onClose}
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        information: commonPageLabels.information,
        contract: commonPageLabels.contract,
        paymentInfo: commonPageLabels.paymentInfo,
        connectAccounts: commonPageLabels.connectedAccounts,
        preferences: commonPageLabels.preferences
      }}
      setShowMigration={setShowMigration}
      setNavigateToPage={setNavigateToPage}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      isLoading={isLoading}
      completed={commonPageLabels.completed}
      onGoBack={onGoBack}
    >
      <CommunicationPreferencesStep
        labels={{
          ...connectAccounts,
          ...commonPageLabels,
          translation: translation,
          layout: layout,
          breadcrumbLabels: breadcrumbPageLabels
        }}
        analytics={{
          completedOnboardingFlow: () => {}
        }}
        state={state}
        router={router}
        configuration={{
          onBoardingClient,
          creatorsClient,
          metadataClient,
          programCode: PROGRAM_CODE,
          DEFAULT_AVATAR_IMAGE: DEFAULT_AVATAR_IMAGE
        }}
        errorHandling={errorHandler}
        errorToast={errorToast}
        stableDispatch={stableDispatch}
        setShowConfirmation={setShowConfirmation}
        showConfirmation={showConfirmation}
        showMigration={showMigration}
        setShowMigration={setShowMigration}
        navigateToPage={navigateToPage}
        user={user}
        connectAccounts={[
          {
            value: "discord",
            accountIcon: discordIcon,
            redirectUrl: `/api/discord-login`
          }
        ]}
      />
    </MigrationLayout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(verifyIncompleteRegistration)
    .use(addLocaleCookie(locale))
    .get(communicationPreferencesProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<CommunicationPreferencesProps>;
};

export default CommunicationPreferences;
