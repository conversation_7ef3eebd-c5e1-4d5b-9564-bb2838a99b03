import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { Article, ArticleProps } from "@src/pages/articles/[...slug]";
import ArticlesHttpClient from "@src/server/contentModal/ArticlesHttpClient";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export default class ArticlePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ArticleProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly articles: ArticlesHttpClient,
    private readonly currentLocale: string,
    private readonly slug: string[],
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, _res: NextApiResponse): Promise<GetServerSidePropsResult<ArticleProps>> {
    try {
      const authenticatedUser = this.hasIdentity(req)
        ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.program, this.defaultAvatar)
        : null;
      const { data: article } = await this.articles.matching((this.slug as []).join("/"), {
        locale: this.currentLocale
      });

      return {
        props: {
          runtimeConfiguration: runtimeConfiguration(authenticatedUser),
          article: article as Article,
          slug: this.slug,
          user: authenticatedUser,
          locale: this.currentLocale,
          ...(await serverSideTranslations(this.currentLocale, [
            "common",
            "dashboard",
            "my-content",
            "notifications",
            "connect-accounts",
            "opportunities"
          ]))
        }
      };
    } catch (e) {
      if (e instanceof ArticleNotFound) return { notFound: true };
      throw e;
    }
  }
}
