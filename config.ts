import { LogLevel } from "@eait-playerexp-cn/activity-logger";
import {
  RedisCacheConfiguration,
  RedisClientOptions,
  ScaleReadType,
  SessionOptions
} from "@eait-playerexp-cn/server-kernel";
const supportedLocales = JSON.parse(process.env.SUPPORTED_LOCALES) as string[];
const dashboardPage = JSON.parse(process.env.MICROCOPY_DASHBOARD_PAGE);
const notificationsPage = JSON.parse(process.env.MICROCOPY_NOTIFICATIONS_PAGE);
const menuItems = JSON.parse(process.env.MENUITEM_NAMES);
const paymentsPage = JSON.parse(process.env.MICROCOPY_PAYMENTS_PAGE);
const communicationPreferencesPage = JSON.parse(process.env.MICROCOPY_COMMUNICATION_PREFERENCES_PAGE);
const termsAndConditionsPage = JSON.parse(process.env.MICROCOPY_TERMS_AND_CONDITION_PAGE);
const informationPage = JSON.parse(process.env.MICROCOPY_INFORMATION_PAGE);
const paymentInformationPage = JSON.parse(process.env.MICROCOPY_PAYMENT_INFORMATION_PAGE);
const notificationBaseUrls = JSON.parse(process.env.NOTIFICATION_BASE_URLS);
const documentationPage = JSON.parse(process.env.MICROCOPY_DOCUMENTATION_PAGE);

//Profile: Remove below lines after integrating with ProfileMFE
const youTubeHosts = JSON.parse(process.env.YOUTUBE_HOSTS);
const twitchHosts = JSON.parse(process.env.TWITCH_HOSTS);
const instagramHosts = JSON.parse(process.env.INSTAGRAM_HOSTS);
const facebookHosts = JSON.parse(process.env.FACEBOOK_HOSTS);
const tiktokHosts = JSON.parse(process.env.TIKTOK_HOSTS);

const validateScopes = (selectedScopes: string, requiredScopes: string) => {
  const selectedUserScopes = selectedScopes?.split(",");
  const requiredAccountScopes = requiredScopes.split(",");
  return requiredAccountScopes.filter((scope) => !selectedUserScopes.includes(scope)).length === 0;
};
const hasAllTikTokScopes = (scopes: string) => validateScopes(scopes, process.env.TIKTOK_SCOPES);
const hasAllFacebookScopes = (scopes: string) => validateScopes(scopes, process.env.FACEBOOK_SCOPES);

const redisClient = new RedisClientOptions(
  process.env.REDIS_HOST,
  +process.env.REDIS_PORT,
  process.env.REDIS_SCALE_READ as ScaleReadType
);
const noAccountPage = JSON.parse(process.env.MICROCOPY_NO_ACCOUNT_PAGE);
const ageRestrictionPage = JSON.parse(process.env.MICROCOPY_AGE_RESTRICTION_PAGE);
const errorPage = JSON.parse(process.env.MICROCOPY_ERROR_PAGE);
const signupCompletePage = JSON.parse(process.env.MICROCOPY_SIGNUP_COMPLETE_PAGE);

const config = {
  LOGIN_URL: new URL(process.env.LOGIN_URL),
  LOGOUT_URL: new URL(process.env.LOGOUT_URL),
  CREATE_ACCOUNT_URL: process.env.CREATE_ACCOUNT_URL,
  CLIENT_ID: process.env.CLIENT_ID,
  FLAG_COUNTRIES_BY_TYPE: process.env.FLAG_COUNTRIES_BY_TYPE === "true",
  PACTSAFE_ID: process.env.PACTSAFE_ID,
  ONBOARDING_MFE_BASE_URL: process.env.ONBOARDING_MFE_BASE_URL,
  ACCESS_TOKEN_BASE_URL: process.env.ACCESS_TOKEN_BASE_URL,
  OPERATIONS_API_BASE_URL: process.env.OPERATIONS_API_BASE_URL,
  CONTENT_SCANNING_API_BASE_URL: process.env.CONTENT_SCANNING_API_BASE_URL,
  METADATA_API_BASE_URL: process.env.METADATA_API_BASE_URL,
  COMMUNICATIONS_API_BASE_URL: process.env.COMMUNICATIONS_API_BASE_URL,
  LEGAL_API_BASE_URL: process.env.LEGAL_API_BASE_URL,
  CREATORS_API_BASE_URL: process.env.CREATORS_API_BASE_URL,
  CONTENT_MANAGEMENT_API_BASE_URL: process.env.CONTENT_MANAGEMENT_API_BASE_URL,
  OPPORTUNITIES_API_BASE_URL: process.env.OPPORTUNITIES_API_BASE_URL,
  CONTENT_SUBMISSION_BASE_URL: process.env.CONTENT_SUBMISSION_BASE_URL,
  API_CLIENT_ID: process.env.API_CLIENT_ID,
  API_CLIENT_SECRET: process.env.API_CLIENT_SECRET,
  SESSION_COOKIE_NAME: process.env.SESSION_COOKIE_NAME,
  SESSION_COOKIE_PASSWORD: process.env.SESSION_COOKIE_PASSWORD,
  SESSION_TTL: process.env.SESSION_TTL,
  GTM_AUTH: process.env.GTM_AUTH,
  GTM_PREVIEW: process.env.GTM_PREVIEW,
  CURRENT_ENV: process.env.CURRENT_ENV,
  CACHE_ENABLED: process.env.CACHE_ENABLED,
  SENTRY_DSN: process.env.SENTRY_DSN,
  NOTIFICATIONS_MFE_BASE_URL: process.env.NOTIFICATIONS_MFE_BASE_URL,
  ALLOWED_NOTIFICATIONS: process.env.ALLOWED_NOTIFICATIONS.split(","),
  MENU_ITEMS: menuItems,
  DISCORD_CLIENT_ID: process.env.DISCORD_CLIENT_ID,
  DISCORD_CLIENT_REDIRECT_URI: new URL(process.env.DISCORD_CLIENT_REDIRECT_URI),
  DISCORD_SCOPES: process.env.DISCORD_SCOPES,
  APP_ENV: process.env.APP_ENV,
  BASE_PATH: process.env.BASE_PATH,
  APP_DEBUG: process.env.APP_DEBUG === "true",
  YOUTUBE_CLIENT_ID: process.env.YOUTUBE_CLIENT_ID,
  YOUTUBE_CLIENT_SECRET: process.env.YOUTUBE_CLIENT_SECRET,
  YOUTUBE_CLIENT_REDIRECT_URI: process.env.YOUTUBE_CLIENT_REDIRECT_URI,
  YOUTUBE_CLIENT_REDIRECT_URL: new URL(process.env.YOUTUBE_CLIENT_REDIRECT_URI),
  YOUTUBE_SCOPES: process.env.YOUTUBE_SCOPES?.split(","),
  TWITCH_CLIENT_ID: process.env.TWITCH_CLIENT_ID,
  TWITCH_CLIENT_REDIRECT_URI: process.env.TWITCH_CLIENT_REDIRECT_URI,
  TWITCH_CLIENT_REDIRECT_URL: new URL(process.env.TWITCH_CLIENT_REDIRECT_URI),
  TWITCH_SCOPES: process.env.TWITCH_SCOPES,
  TIKTOK_CLIENT_ID: process.env.TIKTOK_CLIENT_ID,
  TIKTOK_CLIENT_REDIRECT_URI: process.env.TIKTOK_CLIENT_REDIRECT_URI,
  TIKTOK_CLIENT_REDIRECT_URL: new URL(process.env.TIKTOK_CLIENT_REDIRECT_URI),
  TIKTOK_SCOPES: process.env.TIKTOK_SCOPES,
  TIKTOK_AUTH_BASE_URI: process.env.TIKTOK_AUTH_BASE_URI,
  FACEBOOK_CLIENT_ID: process.env.FACEBOOK_CLIENT_ID,
  FACEBOOK_CLIENT_REDIRECT_URI: process.env.FACEBOOK_CLIENT_REDIRECT_URI,
  FACEBOOK_CLIENT_REDIRECT_URL: new URL(process.env.FACEBOOK_CLIENT_REDIRECT_URI),
  FACEBOOK_STATE: process.env.FACEBOOK_STATE,
  FACEBOOK_SCOPES: process.env.FACEBOOK_SCOPES,
  FACEBOOK_API_VERSION: process.env.FACEBOOK_API_VERSION,
  INSTAGRAM_CLIENT_ID: process.env.INSTAGRAM_CLIENT_ID,
  INSTAGRAM_CLIENT_REDIRECT_URI: process.env.INSTAGRAM_CLIENT_REDIRECT_URI,
  INSTAGRAM_CLIENT_REDIRECT_URL: new URL(process.env.INSTAGRAM_CLIENT_REDIRECT_URI),
  INSTAGRAM_STATE: process.env.INSTAGRAM_STATE,
  INSTAGRAM_SCOPE: process.env.INSTAGRAM_SCOPE,
  INSTAGRAM_API_VERSION: process.env.INSTAGRAM_API_VERSION,
  hasAllFacebookScopes,
  hasAllTikTokScopes,
  SUPPORTED_LOCALES: supportedLocales,
  redisClient,
  sessionOptions: {
    cookies: {
      secret: process.env.COOKIE_PASSWORD,
      httpOnly: process.env.COOKIE_HTTP_ONLY === "true",
      sameSite: process.env.COOKIE_SAME_SITE,
      domain: !!process.env.COOKIE_DOMAIN ? process.env.COOKIE_DOMAIN : undefined,
      secure: process.env.COOKIE_SECURE === "true"
    },
    ttl: +process.env.SESSION_TTL,
    proxy: process.env.SESSION_PROXY === "true"
  } as SessionOptions,
  cacheOptions: {
    cachePrefix: process.env.CACHE_PREFIX,
    redisClient
  } as RedisCacheConfiguration,
  REDIS_CLUSTER: process.env.REDIS_CLUSTER,
  LOG_LEVEL: process.env.LOG_LEVEL as LogLevel,
  TERMS_STATUS_CACHE_TTL: +process.env.TERMS_STATUS_CACHE_TTL,
  AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY,
  AMPLITUDE_ENV: process.env.AMPLITUDE_ENV,
  HTTP_REQUEST_TIMEOUT: +process.env.HTTP_REQUEST_TIMEOUT,
  ANALYTICS_SAMPLE_RATE: process.env.ANALYTICS_SAMPLE_RATE,
  DEFAULT_AVATAR_IMAGE: process.env.DEFAULT_AVATAR_IMAGE,
  SERVICE_NAME: process.env.SERVICE_NAME,
  FLAG_INITIAL_MESSAGE: process.env.FLAG_INITIAL_MESSAGE === "true",
  INTERESTED_CREATOR_REAPPLY_PERIOD: process.env.INTERESTED_CREATOR_REAPPLY_PERIOD === "true",
  LOGIN_REDIRECT_URI: new URL(process.env.LOGIN_REDIRECT_URI),
  PROGRAM_CODE: process.env.PROGRAM_CODE,
  NOTIFICATION_BASE_URLS: notificationBaseUrls,
  SINGLE_PROGRAM_NOTIFICATIONS: process.env.SINGLE_PROGRAM_NOTIFICATIONS === "true",
  DEFAULT_NOTIFICATION_PROGRAM: process.env.DEFAULT_NOTIFICATION_PROGRAM,
  RELEASE_VERSION: process.env.RELEASE_VERSION || "1.0.0.dev",
  DEFAULT_FRANCHISE_IMAGE: process.env.DEFAULT_FRANCHISE_IMAGE,

  //Profile: Remove below lines after integrating with ProfileMFE
  WATERMARKS_URL: process.env.WATERMARKS_URL,
  FLAG_COMMUNICATIONS_API_CLIENT: process.env.FLAG_COMMUNICATIONS_API_CLIENT === "true",
  YOUTUBE_HOSTS: youTubeHosts,
  TWITCH_HOSTS: twitchHosts,
  INSTAGRAM_HOSTS: instagramHosts,
  FACEBOOK_HOSTS: facebookHosts,
  TIKTOK_HOSTS: tiktokHosts,
  FLAG_OPPORTUNITIES_API_CLIENT: process.env.FLAG_OPPORTUNITIES_API_CLIENT,
  PAYMENTS_API_BASE_URL: process.env.PAYMENTS_API_BASE_URL,
  PAYMENTS_DEFAULT_START_DATE: process.env.PAYMENTS_DEFAULT_START_DATE,
  pagesMicroCopy: {
    dashboard: dashboardPage,
    notifications: notificationsPage,
    noAccount: noAccountPage,
    ageRestriction: ageRestrictionPage,
    error: errorPage,
    paymentsPage: paymentsPage,
    termsAndConditions: termsAndConditionsPage,
    communicationPreferences: communicationPreferencesPage,
    information: informationPage,
    paymentInformation: paymentInformationPage,
    signupComplete: signupCompletePage,
    documentation: documentationPage
  },
  FLAG_OBSERVABILITY: process.env.FLAG_OBSERVABILITY === "true"
};
export default config;
