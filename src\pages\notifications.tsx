import "reflect-metadata";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import { useRouter } from "next/router";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import React, { ComponentType, FC, useMemo } from "react";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import BrowserAnalytics, { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import Header from "@components/header/header";
import Loading from "@src/components/loading/Loading";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { NotificationPageLabels } from "@src/server/contentManagement/NotificationsPageMapper";
import Footer, { FooterPageLabels } from "@src/components/footer/ProgramFooter";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import labelsDashboard from "@src/config/translations/dashboard";
import labelsCommon from "@src/config/translations/common";
import labelsMyContent from "@src/config/translations/my-content";
import { useTranslation } from "react-i18next";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import checkTermsAndConditionsOutdated from "@src/serverprops/middleware/CheckTermsAndConditionsOutdated";
import notificationsProps from "@src/serverprops/NotificationsProps";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";

const NotificationCenterPage: ComponentType<Record<string, unknown>> = dynamic(
  // @ts-ignore
  () => import("notifications/NotificationCenterPage"),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export type NotificationsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  referer?: string;
  pageLabels: NotificationPageLabels & CommonPageLabels;
  user?: AuthenticatedUser;
  analytics?: BrowserAnalytics;
};

const Notifications: FC<NotificationsProps> = ({ pageLabels, user }) => {
  const { locale } = useRouter();
  const { notificationsPageLabels } = pageLabels;
  const {
    notificationsClient,
    configuration: { NOTIFICATION_BASE_URLS, SINGLE_PROGRAM_NOTIFICATIONS, PROGRAM_CODE, DEFAULT_NOTIFICATION_PROGRAM }
  } = useDependency();

  const router = useRouter();
  const { t } = useTranslation([
    "common",
    "dashboard",
    "my-content",
    "notifications",
    "connect-accounts",
    "opportunities"
  ]);
  const { layout } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      dashboardLabels: labelsDashboard(t),
      layout: { ...labelsCommon(t), footer: { locale: router.locale, labels: labelsCommon(t).footer } },
      notificationsLabels: notificationBellLabels,
      myContentLabels: labelsMyContent(t)
    };
    return labels;
  }, [t]);

  const { faqs, dashboard, opportunities, myContent, documentation } = layout.header;

  const {
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const footerLabels: FooterPageLabels = {
    commonPageLabels: {
      dashboard: dashboard,
      opportunities: opportunities,
      myContent: myContent,
      documentation: documentation,
      faq: faq,
      legal: legal,
      disclaimer: disclaimer,
      updates: updates,
      terms: terms,
      privacy: privacy,
      rights: rights,
      report: report,
      faqs: faqs,
      disclosure: disclosure,
      policy: policy,
      policies: policies
    }
  };

  return (
    <Layout>
      <LayoutHeader
        pageTitle={notificationsPageLabels.notificationPageTitle}
        tabTitle={`${layout.theSims} | ${notificationsPageLabels.notificationPageTitle}`}
      >
        <Header user={user} labels={pageLabels} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={true}>
        <NotificationCenterPage
          labels={notificationsPageLabels}
          locale={locale}
          configuration={{
            client: notificationsClient,
            programHosts: NOTIFICATION_BASE_URLS,
            program: SINGLE_PROGRAM_NOTIFICATIONS ? PROGRAM_CODE : undefined,
            defaultProgram: DEFAULT_NOTIFICATION_PROGRAM
          }}
        />
      </LayoutBody>
      <LayoutFooter>
        <Footer locale={locale} labels={footerLabels} analytics={undefined} />
      </LayoutFooter>
    </Layout>
  );
};

export default Notifications;

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsOutdated(locale))
    .get(notificationsProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<NotificationsProps>;
};
