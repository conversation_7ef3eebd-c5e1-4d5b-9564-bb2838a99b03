import React, { memo, useCallback } from "react";
import { useRouter } from "next/router";
import { setCookie } from "../../utils";
import { eaALogoSims, Footer } from "@eait-playerexp-cn/core-ui-kit";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "@src/context/DependencyContext";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";

export type FooterPageLabels = {
  commonPageLabels: {
    dashboard: string;
    opportunities: string;
    myContent: string;
    documentation: string;
    faq: string;
    policies: string;
    legal: string;
    disclaimer: string;
    updates: string;
    terms: string;
    privacy: string;
    rights: string;
    report: string;
    faqs: string;
    disclosure: string;
    policy: string;
  };
};

export type FooterProps = {
  locale: string;
  analytics: BrowserAnalytics;
  labels: FooterPageLabels;
};

export default memo(function ProgramFooter({ locale, analytics, labels }: FooterProps) {
  const { commonPageLabels } = labels;

  const footerLabels = {
    dashboard: commonPageLabels.dashboard,
    opportunities: commonPageLabels.opportunities,
    myContent: commonPageLabels.myContent,
    documentation: commonPageLabels.documentation,
    faq: commonPageLabels.faq,
    legal: commonPageLabels.legal,
    disclaimer: commonPageLabels.disclaimer,
    updates: commonPageLabels.updates,
    terms: commonPageLabels.terms,
    privacy: commonPageLabels.privacy,
    rights: commonPageLabels.rights,
    report: commonPageLabels.report,
    faqs: commonPageLabels.faqs
  };

  const router = useRouter();
  const { configuration } = useDependency();
  const programCode = configuration.PROGRAM_CODE;
  const navigateToMarketingPage = (url: string) => {
    analytics?.clickedFooterLink({ locale, url });
    router.push(url);
  };
  const dashboardLink = { label: footerLabels.dashboard, onClick: () => navigateToMarketingPage("/dashboard") };
  const opportunitiesLink = {
    label: footerLabels.opportunities,
    onClick: () => navigateToMarketingPage("/opportunities")
  };
  const myContentLink = { label: footerLabels.myContent, onClick: () => navigateToMarketingPage("/my-content") };
  const documentationLink = {
    label: footerLabels.documentation,
    onClick: () => navigateToMarketingPage("/documentation")
  };
  const faqsLink = { label: footerLabels.faqs, onClick: () => navigateToMarketingPage("/faq") };
  const legalLink = { label: footerLabels.legal, href: "https://www.ea.com/legal-notices" };
  const disclaimerLink = { label: footerLabels.disclaimer, href: "https://www.ea.com/legal/legal-disclosures" };
  const updatesLink = { label: footerLabels.updates, href: "http://www.ea.com/1/service-updates" };
  const termsLink = { label: footerLabels.terms, href: "http://tos.ea.com/legalapp/WEBTERMS/US/en/PC/" };
  const privacyLink = { label: footerLabels.privacy, href: "https://www.ea.com/privacy-policy" };
  const reportLink = {
    label: footerLabels.report,
    href: `https://help.ea.com/${locale}/help-contact-us/?product=origin&topic=report-toxicity&category=report-concerns-or-harassment&subCategory=report-player`
  };
  const footerLinksWeb = {
    internal: [dashboardLink, opportunitiesLink, myContentLink, documentationLink, faqsLink],
    external: {
      sections: [
        [legalLink, disclaimerLink, updatesLink],
        [termsLink, privacyLink, reportLink]
      ]
    }
  };

  const footerLinksMobile = {
    internal: [dashboardLink, opportunitiesLink, myContentLink, documentationLink, faqsLink],
    external: {
      sections: [[legalLink], [disclaimerLink], [updatesLink], [termsLink], [privacyLink], [reportLink]]
    }
  };

  const updateLocale = useCallback(
    (selectedItem) => {
      const { locale = undefined } = selectedItem;
      const { pathname, asPath, query } = router;
      if (locale) {
        setCookie(locale);
        localStorage.setItem("locale", JSON.stringify(selectedItem));
        router.push({ pathname, query }, asPath, { locale });
      }
    },
    [router]
  );

  return (
    <Footer
      countriesDropdown={{
        countries: [],
        onChange: updateLocale,
        defaultLocale: locale
      }}
      programIcon={eaALogoSims}
      footerLinksWeb={footerLinksWeb}
      footerLinksMobile={footerLinksMobile}
      labels={{
        rights: footerLabels.rights,
        year: `${LocalizedDate.year()}`
      }}
      programCode={programCode}
    />
  );
});
