import React, { FC } from "react";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { SignupCompletePageLabels } from "@src/server/contentManagement/SignupCompletePageMapper";

export type SignUpCompletePageProps = {
  pageLabels: SignupCompletePageLabels;
  onClick: () => void;
};

const SignUpCompletePage: FC<SignUpCompletePageProps> = ({ pageLabels, onClick }) => {
  const { signupCompleteLabels } = pageLabels;
  return (
    <div className="signup-complete-container">
      <div className="signup-complete-bg"></div>
      <div className="signup-complete-header">
        <div className="signup-complete-logo">
          <img src="/img/sims-logo.svg" alt="" data-testid="signup-complete-header-image" />
        </div>
      </div>
      <div className="signup-complete-main-content">
        <div className="signup-complete-character-section">
          <img
            src="/img/frontify-separating-rows.svg"
            alt=""
            className="signup-complete-characters"
            data-testid="signup-complete-characters"
          />
        </div>
        <div className="signup-complete-horizontal-line"></div>
        <div className="signup-complete-content-container">
          <h1 className="signup-complete-title">{signupCompleteLabels.title}</h1>
          <div className="signup-complete-subtitle">{signupCompleteLabels.subtitle}</div>
          <div className="signup-complete-body">{signupCompleteLabels.body}</div>
        </div>
        <div className="signup-complete-horizontal-line"></div>
        <div className="signup-complete-button-container">
          <Button variant="primary" size="md" onClick={onClick}>
            {signupCompleteLabels.getStarted}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SignUpCompletePage;
