import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import SearchOpportunityController from "@src/server/opportunities/SearchOpportunityController";
import { anOpportunityWithProgramCode } from "__tests__/factories/opportunities/OpportunityWithPerks";
import OpportunitiesHttpClient from "@src/server/opportunities/OpportunitiesHttpClient";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import config from "config";
import Random from "__tests__/factories/Random";

describe("SearchOpportunityController", () => {
  let controller: SearchOpportunityController;

  beforeEach(() => jest.clearAllMocks());

  it("search an opportunity with event details", async () => {
    config.PROGRAM_CODE = "sims_creator_program";
    const userId = Random.uuid();
    const criteria = { page: 1, size: 10, program: "sims_creator_program" };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/opportunities",
      body: criteria,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const opportunitiesWithEventDetails = {
      count: 1,
      opportunities: [anOpportunityWithProgramCode()],
      total: 1
    };
    const opportunities = {
      searchOpportunities: jest.fn().mockResolvedValue(opportunitiesWithEventDetails)
    };
    controller = new SearchOpportunityController((opportunities as unknown) as OpportunitiesHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(opportunitiesWithEventDetails);
    expect(opportunities.searchOpportunities).toHaveBeenCalledTimes(1);
    expect(opportunities.searchOpportunities).toHaveBeenCalledWith(userId, criteria);
  });
});
