import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import ApiContainer from "@src/ApiContainer";
import { RedirectToLoginController } from "@eait-playerexp-cn/authentication";
import onError from "../../shared/headers/AuthErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(RedirectToLoginController);
  await controller.handle(req, res);
});

export default router.handler({ onError });
