import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectAccountsPagePropController from "@src/serverprops/controllers/ConnectAccountsPagePropsController";
import { NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";

jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations");

describe("ConnectAccountsPagePropController", () => {
  const program = Random.programCode();
  const options = { program };
  const currentLocale = "en-us";
  const identity = Identity.fromStored(
    aStoredIdentity({
      programs: [aCreatorProgram({ code: program })]
    })
  );

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the Connect Accounts page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = { identity, save: jest.fn() };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (serverSideTranslations as jest.Mock).mockReturnValue({});
    const controller = new ConnectAccountsPagePropController(options, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(props).toEqual({
      props: {
        error: null,
        invalidTikTokScope: false,
        pages: [],
        runtimeConfiguration: configuration,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        }
      }
    });
  });

  it("includes OAuth error, TikTok scope error flag, and Connected Facebook Pages when present", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const invalidTikTokScope = true;
    const pages = [];
    const error = { code: "invalid-tiktok-scope", message: "Invalid Tiktok scope" };
    req.session = { identity, INVALID_TIKTOK_SCOPE: invalidTikTokScope, fbPages: { pages }, error, save: jest.fn() };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (serverSideTranslations as jest.Mock).mockReturnValue({});
    const controller = new ConnectAccountsPagePropController(options, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(props).toEqual({
      props: {
        error,
        invalidTikTokScope,
        pages,
        runtimeConfiguration: configuration,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        }
      }
    });
    expect(req.session.error).toBeUndefined();
    expect(req.session.INVALID_TIKTOK_SCOPE).toBeUndefined();
    expect(req.session.save).toHaveBeenCalledTimes(3);
  });
});
