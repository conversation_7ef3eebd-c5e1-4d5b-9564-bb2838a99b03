import { render, screen } from "@testing-library/react";
import RichText, { ImageType } from "./RichText";
import { axe } from "jest-axe";
import { useDependency } from "@src/context/DependencyContext";
import { useDetectScreen } from "@src/utils";
import { LinkType } from "@components/RichTextComponents/Link/Link";
import { ArticleType } from "@components/ArticlePage/ArticlePage";
import { Random } from "@eait-playerexp-cn/onboarding-ui";

jest.mock("@src/context/DependencyContext");
jest.mock("@src/utils");

describe("RichText", () => {
  const embeddedItems = [
    {
      sys: {
        id: "123",
        publishedAt: "2024-04-08",
        firstPublishedAt: "2024-04-08",
        publishedVersion: 1
      },
      media: {
        url: "https://example.com/image.jpg",
        title: "Example Image",
        width: 800,
        height: 600
      },
      entryType: "Image" as ImageType
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        BASE_PATH: "https://example.com"
      }
    });
    (useDetectScreen as jest.Mock).mockReturnValue(false);
  });

  it("shows nothing when rich text is not provided", () => {
    render(<RichText richText={null} embeddedItems={[]} />);

    expect(screen.queryByTestId("richText")).not.toBeInTheDocument();
  });

  it("shows paragraphs correctly", () => {
    const richText =
      '{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Sample Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Sample Text")).toHaveClass("rich-text-paragraph");
  });

  it("shows heading 1 tags correctly", () => {
    const richText =
      '{"nodeType":"heading-1","data":{},"content":[{"nodeType":"text","value":"Heading Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Heading Text")).toHaveClass("rich-text-heading1");
  });

  it("shows heading 2 tags correctly", () => {
    const richText =
      '{"nodeType":"heading-2","data":{},"content":[{"nodeType":"text","value":"Heading Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Heading Text")).toHaveClass("rich-text-heading2");
  });

  it("shows heading 3 tags correctly", () => {
    const richText =
      '{"nodeType":"heading-3","data":{},"content":[{"nodeType":"text","value":"Heading Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Heading Text")).toHaveClass("rich-text-heading3");
  });

  it("shows heading 4 tags correctly", () => {
    const richText =
      '{"nodeType":"heading-4","data":{},"content":[{"nodeType":"text","value":"Heading Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Heading Text")).toHaveClass("rich-text-heading4");
  });

  it("shows heading 5 tags correctly", () => {
    const richText =
      '{"nodeType":"heading-5","data":{},"content":[{"nodeType":"text","value":"Heading Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Heading Text")).toHaveClass("rich-text-heading5");
  });

  it("shows heading 6 tags correctly", () => {
    const richText =
      '{"nodeType":"heading-6","data":{},"content":[{"nodeType":"text","value":"Heading Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByText("Heading Text")).toHaveClass("rich-text-heading6");
  });

  it("shows unordered lists correctly", () => {
    const richText =
      '{"nodeType":"unordered-list","data":{},"content":[{"nodeType":"list-item","content":[{"nodeType":"text","value":"List Item","marks":[]}]}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    const listItem = screen.getByText("List Item");
    expect(listItem).toBeInTheDocument();
    expect(listItem.closest("ul")).toHaveClass("rich-text-unordered-list");
  });

  it("shows ordered lists correctly", () => {
    const richText =
      '{"nodeType":"ordered-list","data":{},"content":[{"nodeType":"list-item","content":[{"nodeType":"text","value":"List Item","marks":[]}]}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    const listItem = screen.getByText("List Item");
    expect(listItem).toBeInTheDocument();
    expect(listItem.closest("ol")).toHaveClass("rich-text-ordered-list");
  });

  it("shows hyperlinks correctly", () => {
    const richText =
      '{"nodeType":"hyperlink","data":{"uri":"https://example.com"},"content":[{"nodeType":"text","value":"Link Text","marks":[],"data":{}}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    const link = screen.getByText("Link Text");
    expect(link).toHaveAttribute("href", "https://example.com");
    expect(link).toHaveClass("rich-text-link");
  });

  it("shows embedded entries correctly", () => {
    const richText =
      '{"nodeType":"embedded-entry-block","data":{"target":{"sys":{"id":"123","type":"Link","linkType":"Entry"}}},"content":[]}';

    render(<RichText {...{ richText, embeddedItems }} />);

    expect(screen.getByTestId("richText-image")).toBeInTheDocument();
  });

  it("shows no Embedded blocks with no Embedded items", () => {
    const richText =
      '{"nodeType":"embedded-entry-block","data":{"target":{"sys":{"id":"123","type":"Link","linkType":"Entry"}}},"content":[]}';
    const embeddedItems = undefined;

    render(<RichText {...{ richText, embeddedItems }} />);

    expect(screen.queryByTestId("richText-image")).not.toBeInTheDocument();
  });

  it("shows no Embedded blocks with no matching Embedded entry", () => {
    const richText =
      '{"nodeType":"embedded-entry-block","data":{"target":{"sys":{"id":"123","type":"Link","linkType":"Entry"}}},"content":[]}';
    const embeddedItems = [
      {
        sys: {
          id: "1234",
          publishedAt: "2024-04-08",
          firstPublishedAt: "2024-04-08",
          publishedVersion: 1
        },
        media: {
          url: "https://example.com/image.jpg",
          title: "Example Image",
          width: 800,
          height: 600
        },
        entryType: "Image" as ImageType
      }
    ];

    render(<RichText {...{ richText, embeddedItems }} />);

    expect(screen.queryByTestId("richText-image")).not.toBeInTheDocument();
  });

  it("shows no Embedded blocks with Link type Embedded entry", () => {
    const richText =
      '{"nodeType":"embedded-entry-block","data":{"target":{"sys":{"id":"123","type":"Link","linkType":"Entry"}}},"content":[]}';
    const embeddedItems = [
      {
        sys: {
          id: "123",
          publishedAt: "2024-04-08",
          firstPublishedAt: "2024-04-08",
          publishedVersion: 1
        },
        media: {
          url: "https://example.com/image.jpg",
          title: "Example Image",
          width: 800,
          height: 600
        },
        entryType: "Link" as ImageType
      }
    ];

    render(<RichText {...{ richText, embeddedItems }} />);

    expect(screen.queryByTestId("richText-image")).not.toBeInTheDocument();
  });

  it("shows inline hyperLinks correctly", () => {
    const richText =
      '{"nodeType":"entry-hyperlink","data":{"target":{"sys":{"id":"5rkjHCqma8IzE6g26LDXTA","type":"Link","linkType":"Entry"}}},"content":[{"nodeType":"text","value":"Lorem ipsum","marks":[],"data":{}}]}';
    const embeddedItems = [
      {
        sys: {
          id: "5rkjHCqma8IzE6g26LDXTA",
          publishedAt: Random.string(),
          firstPublishedAt: Random.string(),
          publishedVersion: 10
        },
        title: Random.string(),
        administrativeTitle: Random.string(),
        url: null,
        page: {
          slug: "article-set-up-bank-info",
          type: "Article" as ArticleType
        },
        entryType: "Link" as LinkType,
        openInNewTab: Random.boolean()
      }
    ];
    const {
      configuration: { BASE_PATH }
    } = useDependency();

    render(<RichText {...{ richText, embeddedItems }} />);

    const link = screen.getByText("Lorem ipsum");
    expect(link).toHaveAttribute("href", `${BASE_PATH}/articles/${embeddedItems[0].page.slug}`);
    expect(link).toHaveClass("rich-text-link");
  });

  it("shows no inline hyperLinks without Embedded items", () => {
    const richText =
      '{"nodeType":"entry-hyperlink","data":{"target":{"sys":{"id":"5rkjHCqma8IzE6g26LDXTA","type":"Link","linkType":"Entry"}}},"content":[{"nodeType":"text","value":"Lorem ipsum","marks":[],"data":{}}]}';
    const embeddedItems = undefined;

    render(<RichText {...{ richText, embeddedItems }} />);

    expect(screen.queryByText("Lorem ipsum")).not.toBeInTheDocument();
  });

  it("shows no inline hyperLinks without matching Embedded items", () => {
    const richText =
      '{"nodeType":"entry-hyperlink","data":{"target":{"sys":{"id":"123","type":"Link","linkType":"Entry"}}},"content":[{"nodeType":"text","value":"Lorem ipsum","marks":[],"data":{}}]}';
    const embeddedItems = [
      {
        sys: {
          id: "234",
          publishedAt: Random.string(),
          firstPublishedAt: Random.string(),
          publishedVersion: 10
        },
        title: Random.string(),
        administrativeTitle: Random.string(),
        url: null,
        page: {
          slug: "article-set-up-bank-info",
          type: "Article" as ArticleType
        },
        entryType: "Link" as LinkType,
        openInNewTab: Random.boolean()
      }
    ];

    render(<RichText {...{ richText, embeddedItems }} />);

    expect(screen.queryByText("Lorem ipsum")).not.toBeInTheDocument();
  });

  it("shows tables correctly", () => {
    const richText =
      '{"nodeType":"table","data":{},"content":[{"nodeType":"table-row","data":{},"content":[{"nodeType":"table-header-cell","data":{},"content":[{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Cell Text ","marks":[],"data":{}}]}]}]}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    const cellText = screen.getByText("Cell Text");
    expect(cellText).toBeInTheDocument();
    expect(cellText.closest("table")).toHaveClass("rich-text-table");
  });

  it("shows table cell", () => {
    const richText =
      '{"nodeType":"table","data":{},"content":[{"nodeType":"table-row","data":{},"content":[{"nodeType":"table-cell","data":{},"content":[{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Cell Text ","marks":[],"data":{}}]}]}]}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    const cellText = screen.getByText("Cell Text");
    expect(cellText).toBeInTheDocument();
    expect(cellText.closest("td")).toHaveClass("rich-text-table-cell");
  });

  it("shows quotes correctly", () => {
    const richText =
      '{"nodeType":"blockquote","data":{},"content":[{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Quote Text ","marks":[],"data":{}}]}]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    const quoteText = screen.getByText("Quote Text");
    expect(quoteText).toBeInTheDocument();
    expect(quoteText.closest("blockquote")).toHaveClass("rich-text-quote");
  });

  it("shows horizontal rule correctly", () => {
    const richText = '{"nodeType":"hr","data":{},"content":[]}';

    render(<RichText richText={richText} embeddedItems={[]} />);

    expect(screen.getByRole("separator")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const richText =
      '{"nodeType":"blockquote","data":{},"content":[{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Quote Text ","marks":[],"data":{}}]}]}';
    const { container } = render(<RichText richText={richText} embeddedItems={[]} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
