import "reflect-metadata";
import React, { memo } from "react";
import ErrorComponent from "@components/ErrorComponent";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import Footer from "@components/footer/ProgramFooter";
import { footerLabels } from "./404";
import { useRouter } from "next/router";
import Header from "@components/header/header";

export const errorPageLabels = {
  commonPageLabels: {
    signIn: "Sign in",
    home: "Home",
    faqs: "FAQs"
  }
} as CommonPageLabels;

export default memo(function Custom500() {
  const errorProps = {
    code: 500,
    pageTitle: "Oops! Something went wrong",
    title: "Oops! Something has gone wrong.",
    description: "The page youre looking for seems to be broken. Here are some helpful links instead.",
    header: {
      dashboard: "Dashboard",
      opportunities: "Opportunities",
      myContent: "My Content",
      documentation: "Documentation",
      faqs: "FAQs"
    }
  };
  const { locale } = useRouter();
  return (
    <>
      <Layout>
        <LayoutHeader pageTitle={errorProps.pageTitle}>
          <Header user={null} labels={errorPageLabels} />
        </LayoutHeader>
        <LayoutBody>
          <ErrorComponent {...errorProps} />
        </LayoutBody>
        <LayoutFooter>
          <Footer analytics={undefined} locale={locale} labels={footerLabels} />
        </LayoutFooter>
      </Layout>
    </>
  );
});

export const getStaticProps = async () => {
  return {
    props: {
      runtimeConfiguration: runtimeConfiguration()
    }
  };
};
