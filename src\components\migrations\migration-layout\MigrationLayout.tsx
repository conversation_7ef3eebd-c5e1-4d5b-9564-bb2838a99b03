import { useRouter } from "next/router";
import React, { FC, memo, ReactNode, useEffect, useState } from "react";
import Loading from "../../loading/Loading";
import Header from "../header/Header";
import {
  communicationPreferences,
  connectAccounts,
  dollarPayment,
  information,
  SvgProps,
  termsAndConditions
} from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "@src/context";
import { ONBOARDING_STEPS } from "@src/utils";
import Layout, { LayoutBody, LayoutHeader } from "../../Layout";
import TopNavBar from "@components/header/TopNavBar";
import cx from "classnames";

export type NavStep = {
  icon: FC<SvgProps>;
  title: string;
  href: string;
  isCompleted?: boolean;
};

export const LayoutContext = React.createContext({});

export type Labels = {
  back: string;
  title: string;
  close: string;
};

export type MigrationLabelsType = {
  information: string;
  connectAccounts: string;
  contract: string;
  paymentInfo: string;
  preferences: string;
};

export type MigrationLayoutProps = {
  children?: ReactNode;
  onClose: () => void;
  showHeader?: boolean;
  isLoading?: boolean;
  labels: Labels;
  pageTitle?: string;
  className?: string;
  showLogo?: boolean;
  isRegistrationFlow?: boolean;
  migration?: MigrationLabelsType;
  isOnboardingFlow?: boolean;
  stableDispatch?: (action) => void;
  onGoBack?: () => void;
  setShowMigration?: (value: boolean) => void;
  setNavigateToPage?: (value: string) => void;
  completed?: string;
  theSims?: string;
  tabTitle?: string;
};

export default memo(function MigrationLayout({
  children,
  onClose,
  showHeader = true,
  isLoading = false,
  labels,
  pageTitle,
  migration,
  className = "",
  showLogo = true,
  stableDispatch,
  onGoBack,
  setShowMigration,
  setNavigateToPage,
  completed,
  theSims,
  tabTitle
}: MigrationLayoutProps) {
  const router = useRouter();
  const { locale } = router;
  const {
    state: { onboardingSteps }
  } = useAppContext();
  const [steps, setSteps] = useState([]);
  const [currentPage, setCurrentPage] = useState({});

  useEffect(() => {
    const steps: Array<NavStep> = [
      {
        icon: information,
        title: migration?.information,
        href: "/onboarding/information",
        isCompleted: false
      },
      {
        icon: connectAccounts,
        title: migration.connectAccounts,
        href: "/connect-accounts",
        isCompleted: false
      },
      {
        icon: communicationPreferences,
        title: migration.preferences,
        href: "/communication-preferences",
        isCompleted: false
      },
      {
        icon: termsAndConditions,
        title: migration?.contract,
        href: "/terms-and-conditions",
        isCompleted: false
      },
      {
        icon: dollarPayment,
        title: migration?.paymentInfo,
        href: "/payment-info",
        isCompleted: false
      }
    ];
    setSteps(steps);
    onboardingSteps?.length === 0 && stableDispatch({ type: ONBOARDING_STEPS, data: steps });
  }, []);

  useEffect(() => {
    setSteps(onboardingSteps);
  }, [onboardingSteps]);

  useEffect(() => {
    const currentPage = steps.find(({ href }) => href === router.pathname) || null;
    setCurrentPage(currentPage);
  }, [steps]);

  return (
    <Layout>
      {showLogo && (
        <LayoutHeader pageTitle={pageTitle} tabTitle={tabTitle || `${theSims} | ${pageTitle}`}>
          <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
        </LayoutHeader>
      )}
      <LayoutBody>
        {isLoading && <Loading />}
        <LayoutContext.Provider value={currentPage}>
          <div
            className={cx(`mg-container ${className}`, {
              "mg-container-header-hidden": !showHeader
            })}
          >
            {showHeader && (
              <Header
                {...{
                  onClose,
                  steps,
                  currentPage,
                  labels,
                  stableDispatch,
                  onGoBack,
                  setShowMigration,
                  setNavigateToPage,
                  completedLabel: completed
                }}
              />
            )}
            <div className="mg-bg"></div>
            {<div className="mg-page">{children}</div>}
          </div>
        </LayoutContext.Provider>
      </LayoutBody>
    </Layout>
  );
});
