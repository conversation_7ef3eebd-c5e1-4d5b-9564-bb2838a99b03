import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ViewSignedUrlController from "@src/submittedContent/ViewSignedUrlController";
import SignedUrlsHttpClient from "@src/submittedContent/SignedUrlsHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import config from "config";
import Random from "__tests__/factories/Random";
import { SignedURLRequestBody } from "@src/services/SubmittedContentService";

describe("ViewSignedUrlController", () => {
  let controller: ViewSignedUrlController;

  beforeEach(() => jest.clearAllMocks());

  it("gets a pre-signed URL to upload a new file", async () => {
    const signedURLRequestBody: SignedURLRequestBody = {
      participationId: Random.uuid(),
      creatorId: Random.uuid(),
      deliverableId: Random.uuid(),
      fileName: "title.png",
      contentTitle: "title",
      extension: ".png",
      contentType: "image",
      mimeType: "image/png",
      thumbnail: "/img/thumbnail.png",
      fileSize: 12,
      programCode: config.PROGRAM_CODE,
      contentDescription: "description",
      nucleusId: 1233,
      contentScanSourceType: "LISTING"
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      body: signedURLRequestBody
    });
    const preSignedUrlResponse = {
      url: "https://eait-playerexp-cn-content-preprod.s3.amazonaws.com/qa/unapproved/file.psd",
      id: Random.uuid()
    };
    const signedUrls = { preSignedUrlFor: jest.fn().mockResolvedValue(preSignedUrlResponse) };
    controller = new ViewSignedUrlController((signedUrls as unknown) as SignedUrlsHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(preSignedUrlResponse);
    expect(signedUrls.preSignedUrlFor).toHaveBeenCalledTimes(1);
    expect(signedUrls.preSignedUrlFor).toHaveBeenCalledWith(signedURLRequestBody);
  });
});
