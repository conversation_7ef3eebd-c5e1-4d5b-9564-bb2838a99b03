import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "@components/Layout";
import Footer from "@components/footer/ProgramFooter";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import React, { memo } from "react";
import { errorPageLabels, footerLabels } from "./404";
import { useRouter } from "next/router";
import Header from "@components/header/header";
import ErrorComponent from "@components/ErrorComponent";

export default memo(function Custom401() {
  const errorProps = {
    code: 401,
    pageTitle: "Unauthorized",
    title: "Unauthorized"
  };
  const { locale } = useRouter();
  return (
    <Layout>
      <LayoutHeader pageTitle={errorProps.pageTitle}>
        <Header user={null} labels={errorPageLabels} />
      </LayoutHeader>
      <LayoutBody>
        <ErrorComponent {...errorProps} />
      </LayoutBody>
      <LayoutFooter>
        <Footer analytics={undefined} locale={locale} labels={footerLabels} />
      </LayoutFooter>
    </Layout>
  );
});

export const getStaticProps = async () => {
  return {
    props: {
      runtimeConfiguration: runtimeConfiguration()
    }
  };
};
