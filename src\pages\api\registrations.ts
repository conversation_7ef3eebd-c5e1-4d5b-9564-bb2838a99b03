import "reflect-metadata";
import { SaveRegistrationCodeToSessionController } from "@eait-playerexp-cn/onboarding-authentication-plugins";
import ApiContainer from "@src/ApiContainer";
import session from "@src/middleware/Session";
import type { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import onError from "@src/middleware/JsonErrorHandler";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.use(session).get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(SaveRegistrationCodeToSessionController);
  await controller.handle(req, res);
});

export default router.handler({ onError });
