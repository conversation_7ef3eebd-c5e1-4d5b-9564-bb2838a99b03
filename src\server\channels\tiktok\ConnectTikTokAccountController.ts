import { NextApiResponse } from "next";
import { Service } from "typedi";
import config from "config";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import ConnectedAccountCredentials from "../ConnectedAccountCredentials";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

export type OAuthError = { code: string; message: string };

export const INVALID_TIKTOK_SCOPE = "invalid-tiktok-scope";

@Service()
class ConnectTikTokAccountController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly connectedAccounts: ConnectedAccountsHttpClient, private readonly redirectUrl: URL) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const nucleusId = this.hasSession(req, "nucleusId");
    const code = this.query(req, "code") as string;
    let credentials: ConnectedAccountCredentials;

    if (!config.hasAllTikTokScopes(this.query(req, "scopes") as string)) {
      if (nucleusId) {
        const error = { code: INVALID_TIKTOK_SCOPE, message: "Invalid Tiktok scope" };
        await this.addToSession(req, "error", error);
        this.html(res, "<script>window.close();</script>");
        return;
      } else {
        await this.addToSession(req, INVALID_TIKTOK_SCOPE, true);
        this.html(res, "<script>window.close();</script>");
        return;
      }
    }

    if (nucleusId) {
      credentials = ConnectedAccountCredentials.forInterestedCreator(
        this.session(req, "nucleusId") as number,
        code,
        this.redirectUrl
      );
    } else {
      const creator = this.identity(req);
      credentials = ConnectedAccountCredentials.forCreator(creator.id, code, this.redirectUrl);
    }

    await this.connectedAccounts.connectTikTokAccount(credentials);

    await this.addToSession(req, "accountType", "TikTok");

    this.html(res, "<script>window.close();</script>");
  }
}

export default ConnectTikTokAccountController;
