import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import SignupCompletePagePropsController from "./controllers/SignupCompletePagePropsController";

const signupCompleteProps = (locale: string) =>
  serverPropsControllerFactory(
    new SignupCompletePagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale
    )
  );

export default signupCompleteProps;
