import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type PaymentInformationPageLabels = {
  paymentInformationLabels: {
    paymentSettingsDescription: string;
    next: string;
    cancel: string;
    done: string;
  };
};

export class PaymentInformationPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>) {
    const microCopy = new MicroCopy(microCopies);
    return {
      paymentInformationLabels: {
        paymentSettingsDescription: microCopy.get("paymentInformation.paymentSettingsDescription"),
        next: microCopy.get("paymentInformation.next"),
        cancel: microCopy.get("paymentInformation.cancel"),
        done: microCopy.get("paymentInformation.done")
      }
    };
  }
}
