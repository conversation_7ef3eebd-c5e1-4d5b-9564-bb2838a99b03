export const submitSocialMediaContentTranslations = {
  name: "<PERSON>",
  formLabels: {
    contentUrl: "Please enter the Content URL:",
    contentUrlPlaceholder: "Example: http://www.youtube.com/mylink"
  },
  infoLabel: "Please ensure that the URL you are adding is from the YouTube account shown:",
  buttonLabels: { cancel: "Cancel", submit: "Submit Content", close: "Close" },
  title: "Add content from YouTube",
  errorLabels: {
    duplicateUrl: "This URL has already been submitted.",
    instagramErrorUrl:
      "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.",
    videoNotFromChannel: "This video is not from a social channel connected to your CN account.",
    youtubeVideoError: "No Youtube video found with given id.",
    genericContentError: "Please enter a valid URL.",
    unsupportedContentError: "This content which you are trying to submit doesn't match your selected option",
    cancelButton: "Cancel",
    submitButton: "Submit Content",
    contentUrlRequired: "Content URL required",
    unsafeUrlError: "You can not submit content from this website or domain.",
    urlNotFromConnectedAccount: 'The URL you entered is not from this connected "Social Channel" account.',
    urlNotFromConnectedChannel: "The URL you are trying to submit is not from the GamerJay0890 YouTube account.",
    unknownTikTokVideo:
      "The URL you are trying to submit is either not from the GamerJay0890 TikTok account or is not publicly available.",
    invalidSocialSubmission: "Cannot save social media contents because: Account id must not be blank",
    accountAuthorizationFailure: "Cannot save social media contents. Token expired or invalid.",
    cannotExpandUrl: "Expanding this short URL is taking longer than usual. Please try again later.",
    cannotSubmitContentInvalidInput: "Submitted URL is invalid",
    invalidFacebookPage: "Unable to submit. This content does not belong to the Facebook page selected.",
    unSupportedContentType:
      "Only Instagram videos and reels are allowed for submission. The content type you're trying to upload is not supported.",
    unSupportedContentTypeForMedia:
      "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.",
    duplicateScannedContentUrl: "Your Community Manager has submitted this URL."
  },
  successLabels: {
    title: "Content submission successful",
    content: "You have successfully submitted content for this opportunity!"
  }
};
