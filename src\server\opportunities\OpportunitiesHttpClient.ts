import { Inject, Service } from "typedi";
import SearchOpportunityWithPerksCriteria from "./SearchOpportunityWithPerksCriteria";
import OpportunitiesWithProgramCodePage from "./OpportunitiesWithProgramCodePage";
import OpportunityWithPaymentDetails from "./OpportunityWithPaymentDetails";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import OpportunityCriteria from "@src/server/dashboard/OpportunityCriteria";
@Service()
class OpportunitiesHttpClient {
  constructor(@Inject("opportunityClient") private readonly client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Opportunities/operation/viewOpportunityWithPayment}
   */
  async withOpportunityPayment(id: string, creatorId: string): Promise<OpportunityWithPaymentDetails> {
    const response = await this.client.get(`/v9/opportunities/${id}/creator/${creatorId}`);
    return OpportunityWithPaymentDetails.fromApi(response.data);
  }

  /**
   * @see {@link https://dev-services.cn.ea.com/cn-opportunities-api/swagger-ui/index.html?configUrl=/cn-opportunities-api/v3/api-docs/swagger-config#/view-participations-controller/viewParticipationsWithProgramCode}
   */
  async matchingWithProgramDetails(
    creatorId: string,
    criteria: OpportunityCriteria
  ): Promise<OpportunitiesWithProgramCodePage> {
    const response = await this.client.get(`/v5/creators/${creatorId}/participations`, {
      query: criteria
    });
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://opportunities-api-eait-playerexp-cn-cn-services--f205b0587239a9.gitlab.ea.com/docs/api.html#tag/Opportunities/operation/searchOpportunitiesWithProgram}
   */
  async searchOpportunities(
    id: string,
    criteria: SearchOpportunityWithPerksCriteria
  ): Promise<OpportunitiesWithProgramCodePage> {
    const response = await this.client.post(`/v5/creators/${id}/opportunities`, { body: criteria });
    return Promise.resolve(response.data);
  }
}

export default OpportunitiesHttpClient;
