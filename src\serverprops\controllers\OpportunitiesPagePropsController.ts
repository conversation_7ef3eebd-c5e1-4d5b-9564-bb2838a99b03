import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { OpportunitiesPageProps } from "../OpportunitiesProps";

export default class OpportunitiesPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<OpportunitiesPageProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<OpportunitiesPageProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
