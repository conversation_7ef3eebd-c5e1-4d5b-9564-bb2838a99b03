/* tslint:disable */
/* eslint-disable */
// @ts-nocheck
/**
 * Ampli - A strong typed wrapper for your Analytics
 *
 * This file is generated by Amplitude.
 * To update run 'ampli pull browser_typescript'
 *
 * Required dependencies: @amplitude/analytics-browser@^1.3.0
 * Tracking Plan Version: 34
 * Build: 1.0.0
 * Runtime: browser:typescript-ampli-v2
 *
 * [View Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest)
 *
 * [Full Setup Instructions](https://data.amplitude.com/itpxd/Creator%20Network/implementation/browser_typescript)
 */

import * as amplitude from "@amplitude/analytics-browser";

export type Environment = "production" | "development";

export const ApiKey: Record<Environment, string> = {
  production: "",
  development: ""
};

/**
 * Default Amplitude configuration options. Contains tracking plan information.
 */
export const DefaultConfiguration: BrowserOptions = {
  plan: {
    version: "34",
    branch: "main",
    source: "browser_typescript",
    versionId: "7d20b2a3-8ad8-472b-a31f-44cef958ab0f"
  },
  ...{
    ingestionMetadata: {
      sourceName: "browser-typescript-ampli",
      sourceVersion: "2.0.0"
    }
  }
};

export interface LoadOptionsBase {
  disabled?: boolean;
}

export type LoadOptionsWithEnvironment = LoadOptionsBase & {
  environment: Environment;
  client?: { configuration?: BrowserOptions };
};
export type LoadOptionsWithApiKey = LoadOptionsBase & { client: { apiKey: string; configuration?: BrowserOptions } };
export type LoadOptionsWithClientInstance = LoadOptionsBase & { client: { instance: BrowserClient } };

export type LoadOptions = LoadOptionsWithEnvironment | LoadOptionsWithApiKey | LoadOptionsWithClientInstance;

export interface IdentifyProperties {
  /**
   * Name of the community manager assigned to this creator
   */
  "Assigned Community Manager"?: string;
  /**
   * List of types of the social accounts connected to Creator Network
   *
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Connected Social Accounts"?: string[];
  /**
   * Comma separated list of languages that a creator speaks in their connected accounts
   */
  "Content Languages"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Creator Types"?: string[];
  /**
   * Creators that have been flagged by Community Managers in the CRM
   */
  "Is Flagged"?: boolean;
  /**
   * Creator has a completed and up-to-date Tipalti payment profile
   */
  "Is Payable"?: boolean;
  Locale?: string;
  "Primary Franchise"?: string;
  "Primary Platform"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Secondary Franchise"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Secondary Platform"?: string[];
  /**
   * Identify the current "Status" of Creators on Creator Network to determine whether they are "Active", "Flagged", or "Unregistered"
   */
  Status?: string;
  /**
   * Identify whether a user is a "Creator" or "Applicant"
   */
  Type?: string;
}

export interface AcceptedOpportunityInvitationProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface AppliedAllPaymentFiltersProperties {
  "Payment Status Selected": string;
  "Payment Type Selected": string;
  "Selected Date Range Option": string;
}

export interface AppliedDateRangeFilterProperties {
  "Selected Date Range Option": string;
}

export interface AppliedPaymentStatusFilterProperties {
  "Payment Status Selected": string;
}

export interface AppliedPaymentTypeFilterProperties {
  "Payment Type Selected": string;
}

export interface CanceledOnboardingFlowProperties {
  "Page Abandoned": string;
}

export interface CancelledCreatorApplicationProperties {
  "Application Page"?: string;
}

export interface CancelledJoinOpportunityFlowProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
  "Page Abandoned": string;
}

export interface CheckedApplicationStatusProperties {
  "Application Status"?: string;
  Source?: string;
}

export interface ClickedDeliverablesTabProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface ClickedDownloadAttachmentProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface ClickedFooterLinkProperties {
  "Link Clicked": string;
}

export interface CompletedJoinOpportunityFlowProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface ConfirmedCreatorTypeProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Selected Creator Types": string[];
}

export interface ConfirmedFranchiseProperties {
  "Primary Franchise"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Secondary Franchise"?: string[];
}

export interface ConfirmedPlatformProperties {
  "Primary Platform"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Secondary Platform"?: string[];
}

export interface ConfirmedSocialMediaChannelProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Connected Social Accounts"?: string[];
}

export interface ConnectedNewSocialAccountProperties {
  /**
   * The name of an individual Deliverable created by a Community Manager for a content submission Opportunity
   */
  "Deliverable Title"?: string;
  /**
   * Deliverable type will be used to determine whether a Deliverable is Single Submission or Unlimited Submission based on how the Community Manager configured when creating an Opportunity
   */
  "Deliverable Type"?: string;
  /**
   * (Facebook, Instagram, Twitch, YouTube)
   */
  "Social Channel Type": string;
}

export interface ContinuedCreatorApplicationProperties {
  "Application Page"?: string;
  "Final Step"?: boolean;
}

export interface ContinuedJoinOpportunityFlowProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface DeclinedOpportunityInvitationProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface ReceivedContentSubmissionErrorMessageProperties {
  /**
   * The name of an individual Deliverable created by a Community Manager for a content submission Opportunity
   */
  "Deliverable Title"?: string;
  /**
   * Deliverable type will be used to determine whether a Deliverable is Single Submission or Unlimited Submission based on how the Community Manager configured when creating an Opportunity
   */
  "Deliverable Type"?: string;
  "Error Code"?: string;
  "Error Message"?: string;
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Enum Values | FILE, WEBSITE, SOCIAL |
   */
  "Type of Content Submission"?: "FILE" | "WEBSITE" | "SOCIAL";
}

export interface ReceivedErrorMessageProperties {
  "Error Code"?: string;
  "Error Message"?: string;
}

export interface RemovedPaymentFilterProperties {
  /**
   * It can be either "Date Range", "Opportunity Type" or "Payment Status"
   */
  "Removed Filter Type": string;
  "Removed Filtered Value": string;
}

export interface SignedTermsAndConditionsProperties {
  "Agreed to T&C's": boolean;
}

export interface StartedContentSubmissionFlowProperties {
  /**
   * The name of an individual Deliverable created by a Community Manager for a content submission Opportunity
   */
  "Deliverable Title"?: string;
  /**
   * Deliverable type will be used to determine whether a Deliverable is Single Submission or Unlimited Submission based on how the Community Manager configured when creating an Opportunity
   */
  "Deliverable Type"?: string;
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface StartedCreatorApplicationProperties {
  "Application Page"?: string;
}

export interface StartedJoinOpportunityFlowProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface StartedOnboardingFlowProperties {
  Source?: string;
}

export interface SubmittedFileUploadProperties {
  /**
   * Content-Type: (Image, Video, Audio, Blog, Text, etc.)
   */
  "Content Type": string;
  /**
   * The name of an individual Deliverable created by a Community Manager for a content submission Opportunity
   */
  "Deliverable Title"?: string;
  /**
   * Deliverable type will be used to determine whether a Deliverable is Single Submission or Unlimited Submission based on how the Community Manager configured when creating an Opportunity
   */
  "Deliverable Type"?: string;
  "File Extension": string;
  "File Size": string;
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface SubmittedSocialContentProperties {
  /**
   * The name of an individual Deliverable created by a Community Manager for a content submission Opportunity
   */
  "Deliverable Title"?: string;
  /**
   * Deliverable type will be used to determine whether a Deliverable is Single Submission or Unlimited Submission based on how the Community Manager configured when creating an Opportunity
   */
  "Deliverable Type"?: string;
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
  /**
   * (Facebook, Instagram, Twitch, YouTube)
   */
  "Social Channel Type": string;
}

export interface SubmittedWebsiteUrlProperties {
  /**
   * Content-Type: (Image, Video, Audio, Blog, Text, etc.)
   */
  "Content Type": string;
  /**
   * The name of an individual Deliverable created by a Community Manager for a content submission Opportunity
   */
  "Deliverable Title"?: string;
  /**
   * Deliverable type will be used to determine whether a Deliverable is Single Submission or Unlimited Submission based on how the Community Manager configured when creating an Opportunity
   */
  "Deliverable Type"?: string;
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
  "Website Domain Name": string;
}

export interface UpdatedCreatorTypesInProfileProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Added Creator Types": string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Removed Creator Types": string[];
}

export interface UpdatedPrimaryFranchiseProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Selected Franchise(s)": string[];
}

export interface UpdatedPrimaryPlatformInProfileProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Selected Platform(s)": string[];
}

export interface UpdatedSecondaryFranchisesProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Selected Franchise(s)": string[];
}

export interface UpdatedSecondaryPlatformsInProfileProperties {
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Removed Platforms": string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Selected Platform(s)": string[];
}

export interface ViewedMarketingPageProperties {
  "Page Displayed": string;
}

export interface ViewedOpportunityDetailsProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface ContentSubmissionProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface JoinedOpportunityProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export interface OpportunityInviteProperties {
  "Opportunity Franchise"?: string;
  "Opportunity ID"?: string;
  "Opportunity Name"?: string;
  "Opportunity Perks"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Platform"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Opportunity Type"?: string[];
  "Opportunity Visibility"?: string;
}

export class Identify implements BaseEvent {
  event_type = amplitude.Types.SpecialEventType.IDENTIFY;

  constructor(public event_properties?: IdentifyProperties) {
    this.event_properties = event_properties;
  }
}

export class AcceptedOpportunityInvitation implements BaseEvent {
  event_type = "Accepted Opportunity Invitation";

  constructor(public event_properties?: AcceptedOpportunityInvitationProperties) {
    this.event_properties = event_properties;
  }
}

export class AppliedAllPaymentFilters implements BaseEvent {
  event_type = "Applied All Payment Filters";

  constructor(public event_properties: AppliedAllPaymentFiltersProperties) {
    this.event_properties = event_properties;
  }
}

export class AppliedDateRangeFilter implements BaseEvent {
  event_type = "Applied Date Range Filter";

  constructor(public event_properties: AppliedDateRangeFilterProperties) {
    this.event_properties = event_properties;
  }
}

export class AppliedPaymentStatusFilter implements BaseEvent {
  event_type = "Applied Payment Status Filter";

  constructor(public event_properties: AppliedPaymentStatusFilterProperties) {
    this.event_properties = event_properties;
  }
}

export class AppliedPaymentTypeFilter implements BaseEvent {
  event_type = "Applied Payment Type Filter";

  constructor(public event_properties: AppliedPaymentTypeFilterProperties) {
    this.event_properties = event_properties;
  }
}

export class CanceledOnboardingFlow implements BaseEvent {
  event_type = "Canceled Onboarding Flow";

  constructor(public event_properties: CanceledOnboardingFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class CancelledCreatorApplication implements BaseEvent {
  event_type = "Cancelled Creator Application";

  constructor(public event_properties?: CancelledCreatorApplicationProperties) {
    this.event_properties = event_properties;
  }
}

export class CancelledJoinOpportunityFlow implements BaseEvent {
  event_type = "Cancelled Join Opportunity Flow";

  constructor(public event_properties: CancelledJoinOpportunityFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class CheckedApplicationStatus implements BaseEvent {
  event_type = "Checked Application Status";

  constructor(public event_properties?: CheckedApplicationStatusProperties) {
    this.event_properties = event_properties;
  }
}

export class ClickedDeliverablesTab implements BaseEvent {
  event_type = 'Clicked "Deliverables" tab';

  constructor(public event_properties?: ClickedDeliverablesTabProperties) {
    this.event_properties = event_properties;
  }
}

export class ClickedDownloadAttachment implements BaseEvent {
  event_type = 'Clicked "Download Attachment"';

  constructor(public event_properties?: ClickedDownloadAttachmentProperties) {
    this.event_properties = event_properties;
  }
}

export class ClickedEmailPocLink implements BaseEvent {
  event_type = "Clicked Email POC Link";
}

export class ClickedFooterLink implements BaseEvent {
  event_type = "Clicked Footer Link";

  constructor(public event_properties: ClickedFooterLinkProperties) {
    this.event_properties = event_properties;
  }
}

export class ClickedOpportunityDescription implements BaseEvent {
  event_type = "Clicked Opportunity Description";
}

export class ClickedPaidOpportunitiesWhenThereIsNoTransaction implements BaseEvent {
  event_type = "Clicked Paid Opportunities When There Is No Transaction";
}

export class ClickedPaymentDetailsIncompleteHelperBanner implements BaseEvent {
  event_type = "Clicked Payment Details Incomplete Helper Banner";
}

export class ClickedPaymentDetailsIncompleteTooltip implements BaseEvent {
  event_type = "Clicked Payment Details Incomplete Tooltip";
}

export class ClickedPaymentInformationInMyProfile implements BaseEvent {
  event_type = "Clicked Payment Information In My Profile";
}

export class ClickedPaymentSettingsTab implements BaseEvent {
  event_type = "Clicked Payment Settings Tab";
}

export class CompletedJoinOpportunityFlow implements BaseEvent {
  event_type = "Completed Join Opportunity Flow";

  constructor(public event_properties?: CompletedJoinOpportunityFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class CompletedOnboardingFlow implements BaseEvent {
  event_type = "Completed Onboarding Flow";
}

export class ConfirmedCommunicationPreferences implements BaseEvent {
  event_type = "Confirmed Communication Preferences";
}

export class ConfirmedCreatorType implements BaseEvent {
  event_type = "Confirmed Creator Type";

  constructor(public event_properties: ConfirmedCreatorTypeProperties) {
    this.event_properties = event_properties;
  }
}

export class ConfirmedFranchise implements BaseEvent {
  event_type = "Confirmed Franchise";

  constructor(public event_properties?: ConfirmedFranchiseProperties) {
    this.event_properties = event_properties;
  }
}

export class ConfirmedPlatform implements BaseEvent {
  event_type = "Confirmed Platform";

  constructor(public event_properties?: ConfirmedPlatformProperties) {
    this.event_properties = event_properties;
  }
}

export class ConfirmedSocialMediaChannel implements BaseEvent {
  event_type = "Confirmed Social Media Channel";

  constructor(public event_properties?: ConfirmedSocialMediaChannelProperties) {
    this.event_properties = event_properties;
  }
}

export class ConnectedNewSocialAccount implements BaseEvent {
  event_type = "Connected New Social Account";

  constructor(public event_properties: ConnectedNewSocialAccountProperties) {
    this.event_properties = event_properties;
  }
}

export class ContinuedCreatorApplication implements BaseEvent {
  event_type = "Continued Creator Application";

  constructor(public event_properties?: ContinuedCreatorApplicationProperties) {
    this.event_properties = event_properties;
  }
}

export class ContinuedJoinOpportunityFlow implements BaseEvent {
  event_type = "Continued Join Opportunity Flow";

  constructor(public event_properties?: ContinuedJoinOpportunityFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class DeclinedOpportunityInvitation implements BaseEvent {
  event_type = "Declined Opportunity Invitation";

  constructor(public event_properties?: DeclinedOpportunityInvitationProperties) {
    this.event_properties = event_properties;
  }
}

export class DownloadedPaymentContract implements BaseEvent {
  event_type = "Downloaded Payment Contract";
}

export class EmailSentToPoc implements BaseEvent {
  event_type = "Email Sent to POC";
}

export class OpenedPaymentsFiltersForm implements BaseEvent {
  event_type = "Opened Payments Filters Form";
}

export class ReceivedContentSubmissionErrorMessage implements BaseEvent {
  event_type = "Received content submission error message";

  constructor(public event_properties?: ReceivedContentSubmissionErrorMessageProperties) {
    this.event_properties = event_properties;
  }
}

export class ReceivedErrorMessage implements BaseEvent {
  event_type = "Received Error Message";

  constructor(public event_properties?: ReceivedErrorMessageProperties) {
    this.event_properties = event_properties;
  }
}

export class RemovedPaymentFilter implements BaseEvent {
  event_type = "Removed Payment Filter";

  constructor(public event_properties: RemovedPaymentFilterProperties) {
    this.event_properties = event_properties;
  }
}

export class SignedOutOfCreatorNetwork implements BaseEvent {
  event_type = "Signed Out Of Creator Network";
}

export class SignedTermsAndConditions implements BaseEvent {
  event_type = "Signed Terms And Conditions";

  constructor(public event_properties: SignedTermsAndConditionsProperties) {
    this.event_properties = event_properties;
  }
}

export class StartedContentSubmissionFlow implements BaseEvent {
  event_type = "Started Content Submission Flow";

  constructor(public event_properties?: StartedContentSubmissionFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class StartedCreatorApplication implements BaseEvent {
  event_type = "Started Creator Application";

  constructor(public event_properties?: StartedCreatorApplicationProperties) {
    this.event_properties = event_properties;
  }
}

export class StartedJoinOpportunityFlow implements BaseEvent {
  event_type = "Started Join Opportunity Flow";

  constructor(public event_properties?: StartedJoinOpportunityFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class StartedOnboardingFlow implements BaseEvent {
  event_type = "Started Onboarding Flow";

  constructor(public event_properties?: StartedOnboardingFlowProperties) {
    this.event_properties = event_properties;
  }
}

export class SubmittedFileUpload implements BaseEvent {
  event_type = "Submitted File Upload";

  constructor(public event_properties: SubmittedFileUploadProperties) {
    this.event_properties = event_properties;
  }
}

export class SubmittedSocialContent implements BaseEvent {
  event_type = "Submitted Social Content";

  constructor(public event_properties: SubmittedSocialContentProperties) {
    this.event_properties = event_properties;
  }
}

export class SubmittedWebsiteUrl implements BaseEvent {
  event_type = "Submitted Website URL";

  constructor(public event_properties: SubmittedWebsiteUrlProperties) {
    this.event_properties = event_properties;
  }
}

export class UpdatedBasicInformation implements BaseEvent {
  event_type = "Updated Basic Information";
}

export class UpdatedCreatorTypesInProfile implements BaseEvent {
  event_type = "Updated Creator Types in Profile";

  constructor(public event_properties: UpdatedCreatorTypesInProfileProperties) {
    this.event_properties = event_properties;
  }
}

export class UpdatedPrimaryFranchise implements BaseEvent {
  event_type = "Updated Primary Franchise";

  constructor(public event_properties: UpdatedPrimaryFranchiseProperties) {
    this.event_properties = event_properties;
  }
}

export class UpdatedPrimaryPlatformInProfile implements BaseEvent {
  event_type = "Updated Primary Platform in Profile";

  constructor(public event_properties: UpdatedPrimaryPlatformInProfileProperties) {
    this.event_properties = event_properties;
  }
}

export class UpdatedSecondaryFranchises implements BaseEvent {
  event_type = "Updated Secondary Franchises";

  constructor(public event_properties: UpdatedSecondaryFranchisesProperties) {
    this.event_properties = event_properties;
  }
}

export class UpdatedSecondaryPlatformsInProfile implements BaseEvent {
  event_type = "Updated Secondary Platforms in Profile";

  constructor(public event_properties: UpdatedSecondaryPlatformsInProfileProperties) {
    this.event_properties = event_properties;
  }
}

export class ViewedMarketingPage implements BaseEvent {
  event_type = "Viewed Marketing Page";

  constructor(public event_properties: ViewedMarketingPageProperties) {
    this.event_properties = event_properties;
  }
}

export class ViewedOpportunityDetails implements BaseEvent {
  event_type = "Viewed Opportunity Details";

  constructor(public event_properties?: ViewedOpportunityDetailsProperties) {
    this.event_properties = event_properties;
  }
}

export class VisitedMyProfile implements BaseEvent {
  event_type = "Visited My Profile";
}

export type PromiseResult<T> = { promise: Promise<T | void> };

const getVoidPromiseResult = () => ({ promise: Promise.resolve() });

// prettier-ignore
export class Ampli {
  private disabled: boolean = false;
  private amplitude?: BrowserClient;

  get client(): BrowserClient {
    this.isInitializedAndEnabled();
    return this.amplitude!;
  }

  get isLoaded(): boolean {
    return this.amplitude != null;
  }

  private isInitializedAndEnabled(): boolean {
    if (!this.amplitude) {
      console.error('ERROR: Ampli is not yet initialized. Have you called ampli.load() on app start?');
      return false;
    }
    return !this.disabled;
  }

  /**
   * Initialize the Ampli SDK. Call once when your application starts.
   *
   * @param options Configuration options to initialize the Ampli SDK with.
   */
  load(options: LoadOptions): PromiseResult<void> {
    this.disabled = options.disabled ?? false;

    if (this.amplitude) {
      console.warn('WARNING: Ampli is already intialized. Ampli.load() should be called once at application startup.');
      return getVoidPromiseResult();
    }

    let apiKey: string | null = null;
    if (options.client && 'apiKey' in options.client) {
      apiKey = options.client.apiKey;
    } else if ('environment' in options) {
      apiKey = ApiKey[options.environment];
    }

    if (options.client && 'instance' in options.client) {
      this.amplitude = options.client.instance;
    } else if (apiKey) {
      this.amplitude = amplitude.createInstance();
      const configuration = (options.client && 'configuration' in options.client) ? options.client.configuration : {};
      return this.amplitude.init(apiKey, undefined, { ...DefaultConfiguration, ...configuration });
    } else {
      console.error("ERROR: ampli.load() requires 'environment', 'client.apiKey', or 'client.instance'");
    }

    return getVoidPromiseResult();
  }

  /**
   * Identify a user and set user properties.
   *
   * @param userId The user's id.
   * @param properties The user properties.
   * @param options Optional event options.
   */
  identify(
    userId: string | undefined,
    properties?: IdentifyProperties,
    options?: EventOptions,
  ): PromiseResult<Result> {
    if (!this.isInitializedAndEnabled()) {
      return getVoidPromiseResult();
    }

    if (userId) {
      options = {...options,  user_id: userId};
    }

    const amplitudeIdentify = new amplitude.Identify();
    const eventProperties = properties;
    if (eventProperties != null) {
      for (const [key, value] of Object.entries(eventProperties)) {
        amplitudeIdentify.set(key, value);
      }
    }
    return this.amplitude!.identify(
      amplitudeIdentify,
      options,
    );
  }

 /**
  * Flush the event.
  */
  flush() : PromiseResult<Result> {
    if (!this.isInitializedAndEnabled()) {
      return getVoidPromiseResult();
    }

    return this.amplitude!.flush();
  }

  /**
   * Track event
   *
   * @param event The event to track.
   * @param options Optional event options.
   */
  track(event: Event, options?: EventOptions): PromiseResult<Result> {
    if (!this.isInitializedAndEnabled()) {
      return getVoidPromiseResult();
    }

    return this.amplitude!.track(event, undefined, options);
  }

  /**
   * Accepted Opportunity Invitation
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Accepted%20Opportunity%20Invitation)
   *
   * **TRIGGER: User is invited to an opportunity and clicks Join button on Opportunity Details page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  acceptedOpportunityInvitation(
    properties?: AcceptedOpportunityInvitationProperties,
    options?: EventOptions,
  ) {
    return this.track(new AcceptedOpportunityInvitation(properties), options);
  }

  /**
   * Applied All Payment Filters
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Applied%20All%20Payment%20Filters)
   *
   * Trigger: Creator clicked "Apply" button on Filter dropdown
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Payment Status Selected)
   * @param options Amplitude event options.
   */
  appliedAllPaymentFilters(
    properties: AppliedAllPaymentFiltersProperties,
    options?: EventOptions,
  ) {
    return this.track(new AppliedAllPaymentFilters(properties), options);
  }

  /**
   * Applied Date Range Filter
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Applied%20Date%20Range%20Filter)
   *
   * Trigger: Creator clicked Date Range and selected preset dropdown option
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Selected Date Range Option)
   * @param options Amplitude event options.
   */
  appliedDateRangeFilter(
    properties: AppliedDateRangeFilterProperties,
    options?: EventOptions,
  ) {
    return this.track(new AppliedDateRangeFilter(properties), options);
  }

  /**
   * Applied Payment Status Filter
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Applied%20Payment%20Status%20Filter)
   *
   * Trigger: Creator clicked Payment Status and selected Payment Status from dropdown
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Payment Status Selected)
   * @param options Amplitude event options.
   */
  appliedPaymentStatusFilter(
    properties: AppliedPaymentStatusFilterProperties,
    options?: EventOptions,
  ) {
    return this.track(new AppliedPaymentStatusFilter(properties), options);
  }

  /**
   * Applied Payment Type Filter
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Applied%20Payment%20Type%20Filter)
   *
   * Trigger: Creator clicked Type and selected specific Payment Type from Dropdown
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Payment Type Selected)
   * @param options Amplitude event options.
   */
  appliedPaymentTypeFilter(
    properties: AppliedPaymentTypeFilterProperties,
    options?: EventOptions,
  ) {
    return this.track(new AppliedPaymentTypeFilter(properties), options);
  }

  /**
   * Canceled Onboarding Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Canceled%20Onboarding%20Flow)
   *
   * TRIGGER: Creator clicks the Cancel button and Confirm Cancel button on Modal to exit the flow.
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Page Abandoned)
   * @param options Amplitude event options.
   */
  canceledOnboardingFlow(
    properties: CanceledOnboardingFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new CanceledOnboardingFlow(properties), options);
  }

  /**
   * Cancelled Creator Application
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Cancelled%20Creator%20Application)
   *
   * **User clicked Cancel and exited the application flow**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Application Page)
   * @param options Amplitude event options.
   */
  cancelledCreatorApplication(
    properties?: CancelledCreatorApplicationProperties,
    options?: EventOptions,
  ) {
    return this.track(new CancelledCreatorApplication(properties), options);
  }

  /**
   * Cancelled Join Opportunity Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Cancelled%20Join%20Opportunity%20Flow)
   *
   * **TRIGGER: User clicked Cancel and exited Join Opportunity flow**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  cancelledJoinOpportunityFlow(
    properties: CancelledJoinOpportunityFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new CancelledJoinOpportunityFlow(properties), options);
  }

  /**
   * Checked Application Status
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Checked%20Application%20Status)
   *
   * **User clicked Apply or Sign In, entered EA credentials and landed on Application Status page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Application Status)
   * @param options Amplitude event options.
   */
  checkedApplicationStatus(
    properties?: CheckedApplicationStatusProperties,
    options?: EventOptions,
  ) {
    return this.track(new CheckedApplicationStatus(properties), options);
  }

  /**
   * Clicked "Deliverables" tab
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20%22Deliverables%22%20tab)
   *
   * **TRIGGER: User clicked "Deliverables" tab on Opportunity Details page and landed on Deliverables page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  clickedDeliverablesTab(
    properties?: ClickedDeliverablesTabProperties,
    options?: EventOptions,
  ) {
    return this.track(new ClickedDeliverablesTab(properties), options);
  }

  /**
   * Clicked "Download Attachment"
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20%22Download%20Attachment%22)
   *
   * **TRIGGER: User clicked "Download Attachment" button to download zip file provided by Community Manager**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  clickedDownloadAttachment(
    properties?: ClickedDownloadAttachmentProperties,
    options?: EventOptions,
  ) {
    return this.track(new ClickedDownloadAttachment(properties), options);
  }

  /**
   * Clicked Email POC Link
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Email%20POC%20Link)
   *
   * **TRIGGER: User clicked email POC link in profile and Contact Modal is displayed**
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedEmailPocLink(
    options?: EventOptions,
  ) {
    return this.track(new ClickedEmailPocLink(), options);
  }

  /**
   * Clicked Footer Link
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Footer%20Link)
   *
   * TRIGGER: Creator clicked a link in the footer and landed on another Marketing Page
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Link Clicked)
   * @param options Amplitude event options.
   */
  clickedFooterLink(
    properties: ClickedFooterLinkProperties,
    options?: EventOptions,
  ) {
    return this.track(new ClickedFooterLink(properties), options);
  }

  /**
   * Clicked Opportunity Description
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Opportunity%20Description)
   *
   * Trigger: Creator clicked Description link (Opportunity Name or Thumbnail) in payments table and landed on Opportunity Page
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedOpportunityDescription(
    options?: EventOptions,
  ) {
    return this.track(new ClickedOpportunityDescription(), options);
  }

  /**
   * Clicked Paid Opportunities When There Is No Transaction
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Paid%20Opportunities%20When%20There%20Is%20No%20Transaction)
   *
   * Trigger: Creator clicked on the "paid opportunities" link when the payment transaction table is in an empty state and landed on the "Opportunities" page
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedPaidOpportunitiesWhenThereIsNoTransaction(
    options?: EventOptions,
  ) {
    return this.track(new ClickedPaidOpportunitiesWhenThereIsNoTransaction(), options);
  }

  /**
   * Clicked Payment Details Incomplete Helper Banner
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Payment%20Details%20Incomplete%20Helper%20Banner)
   *
   * Trigger: Creator clicked the "click here" link in the Payment Details Incomplete Helper Banner and landed on the "Payment Settings" tab
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedPaymentDetailsIncompleteHelperBanner(
    options?: EventOptions,
  ) {
    return this.track(new ClickedPaymentDetailsIncompleteHelperBanner(), options);
  }

  /**
   * Clicked Payment Details Incomplete Tooltip
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Payment%20Details%20Incomplete%20Tooltip)
   *
   * TRIGGER: Creator clicked "Click Here" while hovering on Payment Details Incomplete tooltip and landed on Payment Settings page
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedPaymentDetailsIncompleteTooltip(
    options?: EventOptions,
  ) {
    return this.track(new ClickedPaymentDetailsIncompleteTooltip(), options);
  }

  /**
   * Clicked Payment Information In My Profile
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Payment%20Information%20In%20My%20Profile)
   *
   * **Trigger: Creator clicked "Payment Information" on My Profile left-hand) navigation and landed on the "Payment Information" tab**
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedPaymentInformationInMyProfile(
    options?: EventOptions,
  ) {
    return this.track(new ClickedPaymentInformationInMyProfile(), options);
  }

  /**
   * Clicked Payment Settings Tab
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Clicked%20Payment%20Settings%20Tab)
   *
   * Trigger: Creator clicked on the "Payment Settings" tab in the "Payment Information" section and landed on the "Payment Settings" page.
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  clickedPaymentSettingsTab(
    options?: EventOptions,
  ) {
    return this.track(new ClickedPaymentSettingsTab(), options);
  }

  /**
   * Completed Join Opportunity Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Completed%20Join%20Opportunity%20Flow)
   *
   * **TRIGGER: User clicked Join/Confirm button in Joined Opp flow and  landed the Confirmation page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  completedJoinOpportunityFlow(
    properties?: CompletedJoinOpportunityFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new CompletedJoinOpportunityFlow(properties), options);
  }

  /**
   * Completed Onboarding Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Completed%20Onboarding%20Flow)
   *
   * TRIGGER: Creator completes all steps of onboarding, submits, and successfully lands on My Dashboard for first time
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  completedOnboardingFlow(
    options?: EventOptions,
  ) {
    return this.track(new CompletedOnboardingFlow(), options);
  }

  /**
   * Confirmed Communication Preferences
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Confirmed%20Communication%20Preferences)
   *
   * TRIGGER: Creator successfully added Communication Preferences in onboarding and clicked Next button to land on the next page of the flow
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  confirmedCommunicationPreferences(
    options?: EventOptions,
  ) {
    return this.track(new ConfirmedCommunicationPreferences(), options);
  }

  /**
   * Confirmed Creator Type
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Confirmed%20Creator%20Type)
   *
   * TRIGGER: Creator successfully Selected one or multiple Creator Types in onboarding and clicked the Next button to land on the next page of the flow
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Selected Creator Types)
   * @param options Amplitude event options.
   */
  confirmedCreatorType(
    properties: ConfirmedCreatorTypeProperties,
    options?: EventOptions,
  ) {
    return this.track(new ConfirmedCreatorType(properties), options);
  }

  /**
   * Confirmed Franchise
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Confirmed%20Franchise)
   *
   * TRIGGER: Creator successfully Selected one or multiple Franchise/Game Titles in onboarding and clicked the Next button to land on the next page of the flow
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Primary Franchise)
   * @param options Amplitude event options.
   */
  confirmedFranchise(
    properties?: ConfirmedFranchiseProperties,
    options?: EventOptions,
  ) {
    return this.track(new ConfirmedFranchise(properties), options);
  }

  /**
   * Confirmed Platform
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Confirmed%20Platform)
   *
   * TRIGGER: Creator successfully Selected a one or multiple Platforms in onboarding and clicked the Next button to land on the next page of the flow
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Primary Platform)
   * @param options Amplitude event options.
   */
  confirmedPlatform(
    properties?: ConfirmedPlatformProperties,
    options?: EventOptions,
  ) {
    return this.track(new ConfirmedPlatform(properties), options);
  }

  /**
   * Confirmed Social Media Channel
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Confirmed%20Social%20Media%20Channel)
   *
   * TRIGGER: Creator successfully connected one or multiple Social Channels in onboarding and clicked the Next button to land on the next page of the flow
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Connected Social Accounts)
   * @param options Amplitude event options.
   */
  confirmedSocialMediaChannel(
    properties?: ConfirmedSocialMediaChannelProperties,
    options?: EventOptions,
  ) {
    return this.track(new ConfirmedSocialMediaChannel(properties), options);
  }

  /**
   * Connected New Social Account
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Connected%20New%20Social%20Account)
   *
   * TRIGGER: When Creator connects a new social channel from Add Content dropdown menu and successfully connects a new account.
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Deliverable Title)
   * @param options Amplitude event options.
   */
  connectedNewSocialAccount(
    properties: ConnectedNewSocialAccountProperties,
    options?: EventOptions,
  ) {
    return this.track(new ConnectedNewSocialAccount(properties), options);
  }

  /**
   * Continued Creator Application
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Continued%20Creator%20Application)
   *
   * **User clicks the Next button and lands on Creator Type and/or Franchises you play pagse**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Application Page)
   * @param options Amplitude event options.
   */
  continuedCreatorApplication(
    properties?: ContinuedCreatorApplicationProperties,
    options?: EventOptions,
  ) {
    return this.track(new ContinuedCreatorApplication(properties), options);
  }

  /**
   * Continued Join Opportunity Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Continued%20Join%20Opportunity%20Flow)
   *
   * **TRIGGER: User clicked Next/Accept Button on Opportunity T&C (or another page in flow) and continued Join Opportunity flow**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  continuedJoinOpportunityFlow(
    properties?: ContinuedJoinOpportunityFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new ContinuedJoinOpportunityFlow(properties), options);
  }

  /**
   * Declined Opportunity Invitation
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Declined%20Opportunity%20Invitation)
   *
   * **TRIGGER: User is invited to join an Opportunity but clicked Decline button on Opportunity Details page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  declinedOpportunityInvitation(
    properties?: DeclinedOpportunityInvitationProperties,
    options?: EventOptions,
  ) {
    return this.track(new DeclinedOpportunityInvitation(properties), options);
  }

  /**
   * Downloaded Payment Contract
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Downloaded%20Payment%20Contract)
   *
   * Trigger: Creator clicked on the download contract icon from the payments table and the Opportunity contract was downloaded
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  downloadedPaymentContract(
    options?: EventOptions,
  ) {
    return this.track(new DownloadedPaymentContract(), options);
  }

  /**
   * Email Sent to POC
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Email%20Sent%20to%20POC)
   *
   * **TRIGGER: Creator clicked Send button in contact POC modal**
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  emailSentToPoc(
    options?: EventOptions,
  ) {
    return this.track(new EmailSentToPoc(), options);
  }

  /**
   * Opened Payments Filters Form
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Opened%20Payments%20Filters%20Form)
   *
   * Trigger: Creator clicked Filter button on the Payment Information page and filter dropdown was displayed
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  openedPaymentsFiltersForm(
    options?: EventOptions,
  ) {
    return this.track(new OpenedPaymentsFiltersForm(), options);
  }

  /**
   * Received content submission error message
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Received%20content%20submission%20error%20message)
   *
   * **TRIGGER: Content submission URL was scanned and deemed Malicious/unsafe, invalid, or from a social account not connected to Creator profile**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Deliverable Title)
   * @param options Amplitude event options.
   */
  receivedContentSubmissionErrorMessage(
    properties?: ReceivedContentSubmissionErrorMessageProperties,
    options?: EventOptions,
  ) {
    return this.track(new ReceivedContentSubmissionErrorMessage(properties), options);
  }

  /**
   * Received Error Message
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Received%20Error%20Message)
   *
   * **TRIGGER: Error message triggered and displayed.**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Error Code)
   * @param options Amplitude event options.
   */
  receivedErrorMessage(
    properties?: ReceivedErrorMessageProperties,
    options?: EventOptions,
  ) {
    return this.track(new ReceivedErrorMessage(properties), options);
  }

  /**
   * Removed Payment Filter
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Removed%20Payment%20Filter)
   *
   * TRIGGER: Creator removed a selected payment filter by clicking the "X" button within the Filter Pill
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Removed Filter Type)
   * @param options Amplitude event options.
   */
  removedPaymentFilter(
    properties: RemovedPaymentFilterProperties,
    options?: EventOptions,
  ) {
    return this.track(new RemovedPaymentFilter(properties), options);
  }

  /**
   * Signed Out Of Creator Network
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Signed%20Out%20Of%20Creator%20Network)
   *
   * **TRIGGER: The user clicked Sign Out button from My Profile dropdown and landed on the home marketing page**
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  signedOutOfCreatorNetwork(
    options?: EventOptions,
  ) {
    return this.track(new SignedOutOfCreatorNetwork(), options);
  }

  /**
   * Signed Terms And Conditions
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Signed%20Terms%20And%20Conditions)
   *
   * TRIGGER: Creator Signed and Agreed to T&C's in onboarding and clicked Next/Submit button at the bottom of the page
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Agreed to T&C's)
   * @param options Amplitude event options.
   */
  signedTermsAndConditions(
    properties: SignedTermsAndConditionsProperties,
    options?: EventOptions,
  ) {
    return this.track(new SignedTermsAndConditions(properties), options);
  }

  /**
   * Started Content Submission Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Started%20Content%20Submission%20Flow)
   *
   * **TRIGGER: User clicks Submit Content/Add Content button on any Joined Opportunities Detail page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Deliverable Title)
   * @param options Amplitude event options.
   */
  startedContentSubmissionFlow(
    properties?: StartedContentSubmissionFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new StartedContentSubmissionFlow(properties), options);
  }

  /**
   * Started Creator Application
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Started%20Creator%20Application)
   *
   * **This event fires when the user first starts their application after logging in**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Application Page)
   * @param options Amplitude event options.
   */
  startedCreatorApplication(
    properties?: StartedCreatorApplicationProperties,
    options?: EventOptions,
  ) {
    return this.track(new StartedCreatorApplication(properties), options);
  }

  /**
   * Started Join Opportunity Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Started%20Join%20Opportunity%20Flow)
   *
   * **TRIGGER: User clicked Join Opportunity on Opportunities Details page and  lands on first step of the Join Opportunity flow**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  startedJoinOpportunityFlow(
    properties?: StartedJoinOpportunityFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new StartedJoinOpportunityFlow(properties), options);
  }

  /**
   * Started Onboarding Flow
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Started%20Onboarding%20Flow)
   *
   * **TRIGGER: Creator landed on the First Step of the Onboarding Flow and page loaded successfully**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Source)
   * @param options Amplitude event options.
   */
  startedOnboardingFlow(
    properties?: StartedOnboardingFlowProperties,
    options?: EventOptions,
  ) {
    return this.track(new StartedOnboardingFlow(properties), options);
  }

  /**
   * Submitted File Upload
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Submitted%20File%20Upload)
   *
   * TRIGGER: When a Creator clicks "Upload" when submitting an uploaded file submission for an opportunity.
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Content Type)
   * @param options Amplitude event options.
   */
  submittedFileUpload(
    properties: SubmittedFileUploadProperties,
    options?: EventOptions,
  ) {
    return this.track(new SubmittedFileUpload(properties), options);
  }

  /**
   * Submitted Social Content
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Submitted%20Social%20Content)
   *
   * TRIGGER: When a Creator clicks "Submit" when submitting content from a connected social account during the content submission process.
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Deliverable Title)
   * @param options Amplitude event options.
   */
  submittedSocialContent(
    properties: SubmittedSocialContentProperties,
    options?: EventOptions,
  ) {
    return this.track(new SubmittedSocialContent(properties), options);
  }

  /**
   * Submitted Website URL
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Submitted%20Website%20URL)
   *
   * TRIGGER: When a Creator clicks "Submit" when submitting content from a website (non-social) during the content submission process.
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Content Type)
   * @param options Amplitude event options.
   */
  submittedWebsiteUrl(
    properties: SubmittedWebsiteUrlProperties,
    options?: EventOptions,
  ) {
    return this.track(new SubmittedWebsiteUrl(properties), options);
  }

  /**
   * Updated Basic Information
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Updated%20Basic%20Information)
   *
   * TRIGGER: User clicked Edit on Information page in My Profile, updated field(s), and clicked Save button, locking the fields
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  updatedBasicInformation(
    options?: EventOptions,
  ) {
    return this.track(new UpdatedBasicInformation(), options);
  }

  /**
   * Updated Creator Types in Profile
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Updated%20Creator%20Types%20in%20Profile)
   *
   * **TRIGGER: User clicked Edit in the Creator Types section, updated Creator Types, and clicked Save button at the top of the page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Added Creator Types)
   * @param options Amplitude event options.
   */
  updatedCreatorTypesInProfile(
    properties: UpdatedCreatorTypesInProfileProperties,
    options?: EventOptions,
  ) {
    return this.track(new UpdatedCreatorTypesInProfile(properties), options);
  }

  /**
   * Updated Primary Franchise
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Updated%20Primary%20Franchise)
   *
   * **TRIGGER: User clicked Edit in Primary Franchise section, updated Franchise, and clicked Save button**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Selected Franchise(s))
   * @param options Amplitude event options.
   */
  updatedPrimaryFranchise(
    properties: UpdatedPrimaryFranchiseProperties,
    options?: EventOptions,
  ) {
    return this.track(new UpdatedPrimaryFranchise(properties), options);
  }

  /**
   * Updated Primary Platform in Profile
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Updated%20Primary%20Platform%20in%20Profile)
   *
   * **User clicked Edit in Primary Platform section, updated Primary Platform, and clicked Save**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Selected Platform(s))
   * @param options Amplitude event options.
   */
  updatedPrimaryPlatformInProfile(
    properties: UpdatedPrimaryPlatformInProfileProperties,
    options?: EventOptions,
  ) {
    return this.track(new UpdatedPrimaryPlatformInProfile(properties), options);
  }

  /**
   * Updated Secondary Franchises
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Updated%20Secondary%20Franchises)
   *
   * **TRIGGER: User clicked Edit in Secondary Franchise section, updated Franchise(s), and clicked Save button**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Selected Franchise(s))
   * @param options Amplitude event options.
   */
  updatedSecondaryFranchises(
    properties: UpdatedSecondaryFranchisesProperties,
    options?: EventOptions,
  ) {
    return this.track(new UpdatedSecondaryFranchises(properties), options);
  }

  /**
   * Updated Secondary Platforms in Profile
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Updated%20Secondary%20Platforms%20in%20Profile)
   *
   * **TRIGGER: User clicked Edit in Secondary Platform section, updated Secondary Platform, and clicked Save button**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Removed Platforms)
   * @param options Amplitude event options.
   */
  updatedSecondaryPlatformsInProfile(
    properties: UpdatedSecondaryPlatformsInProfileProperties,
    options?: EventOptions,
  ) {
    return this.track(new UpdatedSecondaryPlatformsInProfile(properties), options);
  }

  /**
   * Viewed Marketing Page
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Viewed%20Marketing%20Page)
   *
   * **TRIGGER: User landed on one of the marketing pages**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Page Displayed)
   * @param options Amplitude event options.
   */
  viewedMarketingPage(
    properties: ViewedMarketingPageProperties,
    options?: EventOptions,
  ) {
    return this.track(new ViewedMarketingPage(properties), options);
  }

  /**
   * Viewed Opportunity Details
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Viewed%20Opportunity%20Details)
   *
   * **TRIGGER: User clicked on an individual opportunity and  landed on the Opportunity Details page**
   *
   * Owner: Sky Powell
   *
   * @param properties The event's properties (e.g. Opportunity Franchise)
   * @param options Amplitude event options.
   */
  viewedOpportunityDetails(
    properties?: ViewedOpportunityDetailsProperties,
    options?: EventOptions,
  ) {
    return this.track(new ViewedOpportunityDetails(properties), options);
  }

  /**
   * Visited My Profile
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Visited%20My%20Profile)
   *
   * **TRIGGER: The user clicked My Profile button and landed on the Information page**
   *
   * Owner: Sky Powell
   *
   * @param options Amplitude event options.
   */
  visitedMyProfile(
    options?: EventOptions,
  ) {
    return this.track(new VisitedMyProfile(), options);
  }
}

export const ampli = new Ampli();

// BASE TYPES
type BrowserOptions = amplitude.Types.BrowserOptions;

export type BrowserClient = amplitude.Types.BrowserClient;
export type BaseEvent = amplitude.Types.BaseEvent;
export type IdentifyEvent = amplitude.Types.IdentifyEvent;
export type GroupEvent = amplitude.Types.GroupIdentifyEvent;
export type Event = amplitude.Types.Event;
export type EventOptions = amplitude.Types.EventOptions;
export type Result = amplitude.Types.Result;
