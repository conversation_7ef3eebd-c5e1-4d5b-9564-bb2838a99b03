import { Service } from "typedi";
import { NextApiResponse } from "next";
import User from "../../authentication/User";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import CreatorsWithProgramsCodeHttpClient from "../creators/CreatorsWithProgramsCodeHttpClient";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ViewCreatorProfileController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly creators: CreatorsWithProgramsCodeHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const user = this.identity(req);
    const data = await this.creators.withId(user.id);

    const updatedUser = User.from(req.session.user as User);
    await this.addToSession(req, "user", updatedUser);

    this.json(res, data);
  }
}

export default ViewCreatorProfileController;
