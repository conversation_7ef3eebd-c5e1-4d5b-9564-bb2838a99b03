import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import FaqPagePropsController from "@src/serverprops/controllers/FaqPagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ArticlesHttpClient from "@src/server/contentModal/ArticlesHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";

jest.mock("@src/server/contentModal/ArticlesHttpClient");
jest.mock("@src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations", () => ({
  serverSideTranslations: jest.fn().mockResolvedValue({
    _nextI18Next: {
      initialI18nStore: {},
      initialLocale: "en-us",
      userConfig: {}
    }
  })
}));

describe("FaqPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const articles = new ArticlesHttpClient({} as any);
  const currentLocale = "en-us";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns props with FAQ page data when authenticated", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = Identity.fromStored(aStoredIdentity());
    req.session = { identity };
    const faqData = { title: "FAQ Page" };
    const configuration = { someConfig: true };

    (articles.matching as jest.Mock).mockResolvedValue({ data: faqData });
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);

    const controller = new FaqPagePropsController(options, articles, currentLocale, "");
    const result = await controller.handle(req, res);

    expect(articles.matching).toHaveBeenCalledWith("faq", { locale: currentLocale });
    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        faqPage: faqData,
        user: expect.any(Object),
        _nextI18Next: expect.any(Object)
      }
    });
  });

  it("returns props with FAQ page data when not authenticated", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = {};
    const faqData = { title: "FAQ Page" };
    const configuration = { someConfig: true };

    (articles.matching as jest.Mock).mockResolvedValue({ data: faqData });
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);

    const controller = new FaqPagePropsController(options, articles, currentLocale, "");
    const result = await controller.handle(req, res);

    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        faqPage: faqData,
        user: null,
        _nextI18Next: expect.any(Object)
      }
    });
  });

  it("returns notFound when article is not found", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = {};

    (articles.matching as jest.Mock).mockRejectedValue(new ArticleNotFound("faq"));

    const controller = new FaqPagePropsController(options, articles, currentLocale, "");
    const result = await controller.handle(req, res);

    expect(result).toEqual({ notFound: true });
  });

  it("throws error when unexpected error occurs", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = {};

    const error = new Error("Unexpected error");
    (articles.matching as jest.Mock).mockRejectedValue(error);

    const controller = new FaqPagePropsController(options, articles, currentLocale, "");

    await expect(controller.handle(req, res)).rejects.toThrow(error);
  });
});
