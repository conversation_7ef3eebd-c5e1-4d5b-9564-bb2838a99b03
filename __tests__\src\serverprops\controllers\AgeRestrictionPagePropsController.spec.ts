import AgeRestrictionPagePropsController from "@src/serverprops/controllers/AgeRestrictionPagePropsController";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");

describe("AgeRestrictionPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props", async () => {
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {
      header: { title: "Age Restriction" },
      content: {
        message: "You must be at least 18 years old",
        button: "I understand"
      }
    };
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new AgeRestrictionPagePropsController(options, contents, currentLocale);

    const props = await controller.handle();

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "ageRestriction");
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels: labels,
        locale: currentLocale
      }
    });
  });
});
