import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import LegacyOpportunitiesHttpClient from "@src/server/opportunities/LegacyOpportunitiesHttpClient";
import OpportunityWithDeliverables from "@src/server/opportunities/OpportunityWithDeliverables";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "__tests__/factories/opportunities/OpportunityWithPerks";
import Random from "__tests__/factories/Random";

describe("LegacyOpportunitiesHttpClient", () => {
  beforeEach(() => jest.clearAllMocks());

  it("finds opportunity with deliverables", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables();
    const creatorId = Random.uuid();
    const opportunityWithDeliverablesDetails = OpportunityWithDeliverables.fromApi(opportunity);
    const client = { get: jest.fn().mockReturnValue({ data: opportunity }) };
    const legacyOpportunity = new LegacyOpportunitiesHttpClient((client as unknown) as TraceableHttpClient);

    const opportunityWithDeliverables = await legacyOpportunity.withOpportunityDeliverables(opportunity.id, creatorId);

    expect(opportunityWithDeliverablesDetails).toEqual(opportunityWithDeliverables);
    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v8/opportunities/${opportunity.id}/creator/${creatorId}`);
  });
});
