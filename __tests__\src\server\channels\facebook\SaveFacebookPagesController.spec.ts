import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import SaveFacebookPagesController from "@src/server/channels/facebook/SaveFacebookPagesController";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import config from "config";

describe("SaveFacebookPagesController", () => {
  let controller: SaveFacebookPagesController;
  const redirectUrl = new URL("http://localhost:3040/api/facebook-connect");
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    config.hasAllFacebookScopes = jest.fn().mockReturnValue(true);
  });

  it("saves all Facebook pages from an account in session", async () => {
    const code = "9544341a-a010-4dc9-a796-5535428d1502";
    const facebookGrantedScopes =
      "email,read_insights,pages_show_list,pages_manage_metadata,pages_read_user_content,pages_manage_ads,public_profile,pages_read_engagement,business_management";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/facebook-connect?code=${code}&granted_scopes=${facebookGrantedScopes}`,
      session
    });
    const creatorFacebookPages = {
      pages: [{ id: "2347sdhfjsd", accessToken: "asfdhjsadh235667887", name: "HariTest" }]
    };
    const connectedAccounts = ({
      facebookPages: jest.fn().mockResolvedValue(creatorFacebookPages)
    } as unknown) as ConnectedAccountsHttpClient;
    controller = new SaveFacebookPagesController(connectedAccounts, redirectUrl);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
    expect(req.session.fbPages).toEqual(creatorFacebookPages);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });

  it("saves invalid scopes error in session when no pages are selected", async () => {
    const code = "9544341a-a010-4dc9-a796-5535428d1502";
    const facebookGrantedScopes = "email,read_insights,pages_manage_metadata,pages_read_user_content,pages_manage_ads";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/facebook-connect?code=${code}&granted_scopes=${facebookGrantedScopes}`,
      session
    });
    const creatorFacebookPages = [];
    const connectedAccounts = ({
      facebookPages: jest.fn().mockResolvedValue(creatorFacebookPages)
    } as unknown) as ConnectedAccountsHttpClient;
    (config.hasAllFacebookScopes as jest.Mock).mockReturnValue(false);
    controller = new SaveFacebookPagesController(connectedAccounts, redirectUrl);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
    expect(req.session.error).toEqual({
      code: "invalid-facebook-scope",
      message: "Cannot connect a Facebook Page, not enough authorization scopes were selected"
    });
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });

  it("closes the login window when user just cancels it", async () => {
    const code = "";
    const facebookGrantedScopes =
      "email,read_insights,pages_manage_metadata,pages_read_user_content,pages_manage_ads,public_profile";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/facebook-connect?code=${code}&granted_scopes=${facebookGrantedScopes}&error=access_denied`
    });
    const creatorFacebookPages = [];
    const connectedAccounts = ({
      facebookPages: jest.fn().mockResolvedValue(creatorFacebookPages)
    } as unknown) as ConnectedAccountsHttpClient;
    controller = new SaveFacebookPagesController(connectedAccounts, redirectUrl);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
  });
});
