import React, { memo, useCallback, useEffect } from "react";
import Form from "../Form";
import JoinO<PERSON>ortunityFooter from "../opportunities/JoinOpportunityFooter";
import { Controller, useFormContext } from "react-hook-form";
import { useAppContext } from "@src/context";
import {
  ERROR,
  JOIN_OPPORTUNITY_FORM,
  onToastClose,
  PARTICIPATION,
  toastContent,
  UGX_OPPORTUNITY,
  useAsync,
  VALIDATION_ERROR
} from "../../utils";
import { useRouter } from "next/router";
import OperationsService from "@src/services/OperationsService";
import { Checkbox, Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";

const JoinOpportunityInput = ({ criteriaLabels }) => {
  const { register, control } = useFormContext();
  register("criteria-checkbox", { required: true });

  return (
    <Controller
      control={control}
      name="criteria-checkbox"
      defaultValue={false}
      render={({ field: { onChange } }) => (
        <Checkbox
          options={[
            { id: "confirmation", label: criteriaLabels.confirmation, isChecked: false, onChange: onChange, dark: true }
          ]}
        />
      )}
    />
  );
};

export default memo(function OpportunityCriteria({ opportunity, analytics, layout, criteriaLabels }) {
  const { errorHandler, client } = useDependency();
  const operationsService = new OperationsService(client);
  const {
    main: { unhandledError }
  } = layout;
  const router = useRouter();
  const { dispatch, state: { isError, isValidationError, joinOpportunitySteps = [] } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(isValidationError)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, dispatch)
      });
    }
  }, [isError, isValidationError, unhandledError]);

  const submitHandle = useCallback(
    async (data) => {
      if (opportunity.hasGameCodes || opportunity.hasDeliverables) {
        analytics.startedJoinOpportunityFlow({
          locale: router.locale,
          opportunity
        });
        stableDispatch({ type: JOIN_OPPORTUNITY_FORM, data: data });
        operationsService.navigateToJoinOpportunityNextSteps(opportunity, router);
      } else {
        try {
          analytics.completedJoinOpportunityFlow({
            opportunity,
            locale: router.locale
          });
          const participation = await operationsService.saveParticipation(data, opportunity);
          stableDispatch({ type: PARTICIPATION, data: participation.data });
          operationsService.navigateToJoinOpportunityNextSteps(opportunity, router);
        } catch (e) {
          errorHandler(dispatch, e);
        } finally {
          stableDispatch({ type: JOIN_OPPORTUNITY_FORM, data: null });
        }
      }
    },
    [opportunity, stableDispatch, joinOpportunitySteps]
  );

  const { pending, execute: onSubmit } = useAsync(submitHandle, false);

  return (
    <div className="criteria-opportunity">
      <div className="criteria-scroll-container">
        <div className="criteria-opportunity-card">
          {opportunity.type === UGX_OPPORTUNITY ? (
            <>
              <p>{criteriaLabels.reviewYourCreatorKitAgreement}</p>
              <p>{criteriaLabels.acknowledgement}</p>
            </>
          ) : (
            <>
              <div>{criteriaLabels.message}</div>
              <ul className="criteria-opportunity-list" aria-labelledby="opportunity-criterias">
                <li>{criteriaLabels.disclosure}</li>
                <li>{criteriaLabels.contentCreation}</li>
                <li>{criteriaLabels.embargoPeriod}</li>
                <li>{criteriaLabels.trustAndSafety}</li>
              </ul>
            </>
          )}
        </div>
      </div>
      <h5 id="opportunity-criterias" className="criteria-opportunity-subTitle">
        {criteriaLabels.title}
      </h5>

      <Form mode="onChange" onSubmit={onSubmit}>
        <JoinOpportunityInput {...{ criteriaLabels, opportunity }} />
        <JoinOpportunityFooter
          {...{
            button: opportunity.hasGameCodes || opportunity.hasDeliverables ? layout.buttons.next : layout.buttons.join,
            pending
          }}
        />
      </Form>
    </div>
  );
});
