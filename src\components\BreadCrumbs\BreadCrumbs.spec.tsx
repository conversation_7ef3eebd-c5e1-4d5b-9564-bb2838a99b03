import { render, screen } from "@testing-library/react";
import BreadCrumbs from "./BreadCrumbs";
import { axe } from "jest-axe";

describe("BreadCrumbs", () => {
  const breadCrumbs = [
    { label: "Documentation", link: "/documentation", isActive: false },
    { label: "Article", link: "/articles/test", isActive: true }
  ];

  it("renders all breadcrumb items", () => {
    render(<BreadCrumbs items={breadCrumbs} />);

    expect(screen.getByText("Documentation")).toBeInTheDocument();
    expect(screen.getByText("Article")).toBeInTheDocument();
  });

  it("styles active breadcrumb item", () => {
    render(<BreadCrumbs items={breadCrumbs} />);

    expect(screen.getByRole("link", { name: /Article/i })).toHaveClass("breadcrumbs-listitem-link-active");
  });

  it("styles inactive breadcrumb item", () => {
    render(<BreadCrumbs items={breadCrumbs} />);

    expect(screen.getByRole("link", { name: /Documentation/i })).toHaveClass("breadcrumbs-listitem-link-inactive");
  });

  it("renders chevron icon between items", () => {
    render(<BreadCrumbs items={breadCrumbs} />);

    const icon = screen.getAllByTestId("breadcrumbs-icon");
    expect(icon.length).toBe(1);
  });

  it("is accessible", async () => {
    const { container } = render(<BreadCrumbs items={breadCrumbs} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
