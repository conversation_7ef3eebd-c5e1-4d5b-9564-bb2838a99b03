import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { MyContentPageProps } from "@src/pages/my-content";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export default class MyContentPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<MyContentPageProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<MyContentPageProps>> {
    const authenticatedUser = this.hasIdentity(req)
      ? AuthenticatedUserFactory.fromIdentity(this.identity(req), this.program, this.defaultAvatar)
      : null;

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        locale: this.currentLocale,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "dashboard",
          "my-content",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
