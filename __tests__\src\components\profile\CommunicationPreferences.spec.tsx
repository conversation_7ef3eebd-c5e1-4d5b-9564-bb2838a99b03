import { screen, waitFor, within } from "@testing-library/react";
import { aCreatorWithPayableStatus } from "../../../factories/creators/CreatorWithPayableStatus";
import userEvent from "@testing-library/user-event";
import { clearValueFor, enterValueFor } from "../../../helpers/forms";
import { renderWithToast, triggerAnimationEnd } from "../../../helpers/toast";
import { aCommunicationPreferences } from "../../../factories/creators/CommunicationPreferences";
import { useAppContext } from "@src/context";
import { commonTranslations as layout } from "../../../translations";
import { onToastClose } from "@src/utils";
import { useDependency } from "@src/context/DependencyContext";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import CommunicationPreferences from "@src/components/profile/CommunicationPreferences";
import CreatorsService from "@src/services/CreatorsService";

jest.mock("@eait-playerexp-cn/creators-http-client");
jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("@src/context/DependencyContext");
jest.mock("@src/services/CreatorsService");
jest.mock("@src/context", () => ({
  ...jest.requireActual("@src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("@src/utils", () => ({
  ...jest.requireActual("@src/utils"),
  onToastClose: jest.fn()
}));

xdescribe("CommunicationPreferences", () => {
  const communicationPreferencesProps = {
    translations: {
      messages: {},
      labels: {
        preferredEmailAddressTitle: "Preferred Email Address for Communication",
        preferredEmail: "Email Address"
      },
      success: {
        preferredEmail: "Your preferred email has been updated",
        preferredPhoneNumber: "Your preferred phone number has been updated",
        preferredLanguage: "Your preferred language has been updated",
        contentLanguage: "Your content language has been updated"
      }
    },
    buttons: { edit: "Edit", cancel: "Cancel", save: "Save" },
    labels: { modal: {} },
    layout: layout.default,
    analytics: {},
    locale: "en-us"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      metadataClient: {},
      configuration: {}
    });
    (MetadataService as jest.Mock).mockReturnValue({
      getLanguages: jest.fn().mockResolvedValue([]),
      getLocales: jest.fn().mockResolvedValue([])
    });
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: {}
    });
  });

  it("updates preferred email", async () => {
    const analytics = { confirmedCommunicationPreferences: jest.fn() };
    const creator = aCreatorWithPayableStatus({
      communicationPreferences: aCommunicationPreferences({
        contentLanguages: [aLanguage({ name: "English", label: "English" })]
      })
    });
    (CreatorsService.getCreatorWithPayableStatus as jest.Mock).mockResolvedValue({
      data: creator
    });

    (CreatorsService.update as jest.Mock).mockImplementation(() => Promise.resolve());
    const { unmount } = renderWithToast(
      <CommunicationPreferences {...{ ...communicationPreferencesProps, analytics }} />
    );
    await waitFor(() => expect(CreatorsService.getCreatorWithPayableStatus).toHaveBeenCalledTimes(1));
    const editButtons = await screen.findAllByRole("button", { name: "Edit" });
    expect(editButtons).toHaveLength(4);
    const preferredEmailEditButton = editButtons[0];
    await userEvent.click(preferredEmailEditButton);
    await clearValueFor(/^Email Address/i);
    await enterValueFor(/^Email Address/i, "<EMAIL>");

    await userEvent.click(screen.getByRole("button", { name: /Save/i }));

    expect(await screen.findByText(/Your preferred email has been updated/i)).toBeInTheDocument();
    expect(analytics.confirmedCommunicationPreferences).toHaveBeenCalledTimes(1);
    expect(analytics.confirmedCommunicationPreferences).toHaveBeenCalledWith({
      contentLanguages: "English",
      locale: "en-us"
    });
    unmount();
  });

  it("closes error toast when clicking on the toast close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch,
      state: { isError: errorMessage }
    });
    const analytics = { confirmedCommunicationPreferences: jest.fn() };
    const creator = aCreatorWithPayableStatus({
      communicationPreferences: aCommunicationPreferences({
        contentLanguages: [aLanguage({ name: "English", label: "English" })]
      })
    });
    (CreatorsService.getCreatorWithPayableStatus as jest.Mock).mockResolvedValue({
      data: creator
    });
    const { unmount } = renderWithToast(
      <CommunicationPreferences {...{ ...communicationPreferencesProps, analytics }} />
    );
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong.");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(screen.getByText("Oops! Something has gone wrong."));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });
});
