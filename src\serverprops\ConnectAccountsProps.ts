import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ConnectAccountsPagePropController from "@src/serverprops/controllers/ConnectAccountsPagePropsController";
import config from "config";

const connectAccountsProps = (locale: string) =>
  serverPropsControllerFactory(
    new ConnectAccountsPagePropController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default connectAccountsProps;
