{"title": "Envoyer du contenu depuis YouTube", "subTitle": "Pour envoyer du contenu depuis YouTube, veuillez suivre les instructions ci-dessous.", "imageDesc1": "Le Creator Network n’accepte que les URL complètes de YouTube :", "imageDesc2": "Les URL courtes, comme l’exemple ci-dessous, ne sont pas prises en charge par le Creator Network :", "pointsToNote1": "Les vidéos ne doivent pas être privées.", "pointsToNote2": "Les vidéos doivent appartenir à la chaîne connectée au Creator Network.", "pointsToNote3": "Si votre nom d’utilisateur a changé depuis la dernière connexion de votre compte au Creator Network, rendez-vous dans Mon profil et reconnectez-le.", "pointsToNote4": "Le Creator Network ne prend pas en charge le contenu envoyé depuis IG Live", "pointsToNote5": "Le compte Instagram connecté doit être en mode professionnel et lié à une page Facebook.", "gotIt": "C’est bon !", "pointsToNote6": "Le contenu dépassant les 30 jours ne peut pas être envoyé au Creator Network", "pointsToNote7": "Les photos Instagram ne sont pas prises en charge.", "footNote": "*VOD (video on demand) signifie vidéo à la demande.", "fileUpload": "Chargement de fichiers", "chooseFile": "<PERSON><PERSON> le <PERSON>", "noFileChoosen": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi", "acceptedFormats": "Formats de fichiers acceptés", "maxFileSize": "<PERSON><PERSON> de <PERSON>er maximale", "maxLimitMessage": "Veuillez sélectionner le fichier dans les limites de taille autorisées.", "invalidFileTypeMessage": "Veuillez sélectionner le fichier avec les extensions autorisées.", "fileSelected": "<PERSON><PERSON><PERSON>", "fileUploading": "Chargement du fichier...", "success": {"title": "<PERSON><PERSON><PERSON> fichier a été envoyé", "content": "Votre fichier pour cette opportunité a été envoyé !"}, "contentTitle": "Titre du contenu", "contentTitlePlaceholder": "A<PERSON>ter le titre du contenu", "contentUrl": "Veuillez saisir l’URL du contenu :", "contentUrlPlaceholder": "Exemple : https://www.mywebsite.com/mylink", "contentType": "Type du contenu", "contentTitleRequired": "Titre du contenu requis", "contentUrlRequired": "URL du contenu requise", "contentTypeRequired": "Type du contenu requis", "duplicateUrlMessage": "Cette URL a déjà été envoyée.", "unsafeUrlMessage": "Vous ne pouvez pas envoyer de contenu à partir de ce site Web ou de ce domaine", "validUrlMessage": "Veuillez saisir une URL valide.", "websiteUrlMessage": "Les URL des réseaux sociaux suivants ne sont pas acceptées : (Facebook, Twitch, Instagram, et YouTube)", "selectContentType": "Veuillez sélectionner un élément", "contentTitleLongMessage": "Le titre du contenu est trop long", "contentGuideLine": "Politique de divulgation", "linkSubmissionGuideLine": "Link Submission", "nonPaid": {"contentGuideLineSubTitle": "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:", "contentGuideLineDescription1": "Prominently include “EA Partner” “Sponsored” “Sponsored by EA” or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #EAPartner, #Sponsored, #SponsoredbyEA, or #Ad are also acceptable.", "contentGuideLineDescription2": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for inviting me to this early access event/for the game code” (or similar).", "linkSubmissionGuideLineSubTitle1": "If you create content, please return to this page after it is live on your channel and submit the links for content you produce.", "linkSubmissionGuideLineSubTitle2": "If you have any questions, please reach out to your Community Manager."}, "paid": {"contentGuideLineSubTitle": "Your EA Creator Network Agreement requires you to include the following disclosures in the content you create and post to comply with federal regulations:", "contentGuideLineDescription1": "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your video or stream.", "contentGuideLineDescription2": "Prominently include “Sponsored by EA” “Sponsored”, or “Ad” at the beginning of the video description/info tab (not behind a “see more” button) or within the title of the content/stream. #SponsoredbyEA, #Sponsored, or #Ad are also acceptable.", "contentGuideLineDescription3": "Use one clearly audible statement in the first 5 seconds of your video or stream stating “Thanks to EA for sponsoring this video.” (or similar).", "contentGuideLineDescription4": "Ensure correct use of content platforms disclosure tools such as YouTube’s “includes paid promotion” and Instagram’s “Partnership” options.", "linkSubmissionGuideLineSubTitle1": "After the content is live on your channel please return to this page and submit the links for any content you produce.", "linkSubmissionGuideLineSubTitle2": "If you have any questions, please reach out to your Creator Partner Manager."}, "accountInformation": "Pour obtenir de l’aide pour envoyer la bonne URL des réseaux sociaux, sélectionnez un réseau →", "downloadEaLogo": "Télécharger le filigrane d’EA", "websiteTitle": "Site web", "websiteDescription": "Tous les autres sites Web", "uploadFileTitle": "Charger un fichier depuis votre appareil", "uploadFileDescription": "Envoyer des fichiers vidéo, audio ou image", "submitContentInformation": "<PERSON><PERSON> pouvez envoyer du contenu depuis un compte connecté sur Facebook, Instagram, Youtube ou Twitch et depuis un autre site Web.", "submissionOpens": "Ouverture de l'envoi :", "submissionCloses": "Fin de l’envoi :", "contentRequirements": "Conditions requises pour l'envoi de contenu", "availableResources": "Ressources disponibles :", "downloadAttachments": "Télécharger des pièces jointes", "submissionOpensWithNoContent": "La fenêtre d’envoi de contenu pour cette opportunité n’est pas encore ouverte. Vous recevrez une notification au moment d’envoyer le contenu.", "submissionClosedWithNoContent": "La fenêtre d’envoi de contenu pour cette opportunité est fermée et aucun nouveau contenu ne peut être envoyé.", "submissionClosedWithContent": "La fenêtre d’envoi de contenu pour cette opportunité est fermée et aucun nouveau contenu ne peut être envoyé. Les envois existants peuvent être mis à jour si des modifications sont nécessaires.", "disclosureParagraph1": "As part of the agreement for EA Creator Network you are required to comply with any applicable regulatory guidelines about endorsements and testimonials in advertising and other applicable consumer laws, and that at a minimum you apply the following disclosures:", "disclosureParagraph2": "Include the ‘Sponsored by EA’ watermark in the first 5 seconds of your stream. Use one clearly audible statement in the first 5 seconds of your stream stating “Thanks to EA for Sponsoring this stream” or similar <br> Social content must include #SponsoredByEA <br> Include the following tracking link: TBC", "linkSubmissionContent": "After the content is live on your channel please return to this page and submit the links for any content you produce, content needs to be uploaded for approval before payment can be processed. In case you have any issues, please reach out to your community manager.", "needsChanges": " needs changes.", "changesRequiredTitle": "Changes Required", "contentDeliverables": "Content Deliverables", "deliverablesInstruction": "Please join this opportunity to submit content.", "notYetSubmitted": "Not yet submitted", "pendingApproval": "Pending approval", "changesRequired": "Changes Required", "completed": "Completed", "rejected": "Rejected", "notApproved": "Not approved", "submitUnlimitedContent": "Submit unlimited content", "submissionWindowClosed": "Submission window closed"}