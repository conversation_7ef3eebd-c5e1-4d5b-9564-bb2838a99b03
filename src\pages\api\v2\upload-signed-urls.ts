import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";
import ViewSignedUrlController from "@src/submittedContent/ViewSignedUrlController";
import ApiContainer from "@src/ApiContainer";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ViewSignedUrlController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
