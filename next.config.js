// This file sets a custom webpack configuration to use your Next.js app
// with Sentry.
// https://nextjs.org/docs/api-reference/next.config.js/introduction
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
const { withSentryConfig } = require("@sentry/nextjs");
const NextFederationPlugin = require("@module-federation/nextjs-mf");
const { i18n } = require("./next-i18next.config");

module.exports = {
  i18n,
  reactStrictMode: false,
  sentry: {
    hideSourceMaps: true
  },
  output: "standalone",
  webpack(config, _options) {
    config.output.environment = {
      ...config.output.environment,
      dynamicImport: true
    };
    const { isServer, plugins } = config;
    const location = isServer ? "ssr" : "chunks";
    plugins.push(
      new NextFederationPlugin({
        name: "sims-ugx",
        filename: "sims-ugx.js",
        remotes: {
          onboarding: `onboarding@${process.env.ONBOARDING_MFE_BASE_URL}/_next/static/${location}/onboarding-mfe.js`,
          notifications: `notifications@${process.env.NOTIFICATIONS_MFE_BASE_URL}/_next/static/${location}/notifications-mfe.js`
        }
      })
    );
    return config;
  },
  publicRuntimeConfig: {
    CURRENT_ENV: process.env.CURRENT_ENV,
    BUILD_VERSION: process.env.RELEASE_VERSION,
    SENTRY_DSN: process.env.SENTRY_DSN,
    APP_ENV: process.env.APP_ENV,
    HTTP_REQUEST_TIMEOUT: +process.env.HTTP_REQUEST_TIMEOUT,
    FLAG_OBSERVABILITY: process.env.FLAG_OBSERVABILITY
  },
  experimental: {
    instrumentationHook: true,
    serverComponentsExternalPackages: ["@opentelemetry/sdk-node"]
  }
};

module.exports = withSentryConfig(module.exports, { silent: true });
