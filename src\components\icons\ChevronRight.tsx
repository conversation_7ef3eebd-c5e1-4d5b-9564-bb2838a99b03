import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { FC } from "react";

const ChevronRight: FC<SvgProps> = ({ className, ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      className={className}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.3497 1.00963L9.12662 5.7404C9.26508 5.87887 9.26508 6.10963 9.12662 6.2481L4.3497 10.9789C4.21124 11.1173 3.98047 11.1173 3.84201 10.9789L3.33431 10.4712C3.19585 10.3327 3.19585 10.1019 3.33431 9.96348L7.09585 6.2481C7.23431 6.10963 7.23431 5.87887 7.09585 5.7404L3.35739 2.02502C3.21893 1.88656 3.21893 1.65579 3.35739 1.51733L3.86508 1.00963C4.00355 0.89425 4.21124 0.89425 4.3497 1.00963Z"
        fill="#706E6B"
      />
    </svg>
  );
};

export default ChevronRight;
