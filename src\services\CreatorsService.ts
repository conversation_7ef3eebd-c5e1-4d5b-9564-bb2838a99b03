import { AxiosResponse } from "axios";
import AccountInformationWithFlaggedStatus from "@src/server/creators/AccountInformationWithFlaggedStatus";
import client from "./Client";
import CreatorWithCreatorCode from "@src/server/creators/CreatorWithCreatorCode";
import { FormDataBody } from "@eait-playerexp-cn/http";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorCodeResponse } from "@src/server/creators/CreatorWithMultiplePOC";
import AdditionalInformationWithMultiplePOC from "@src/server/creators/AdditionalInformationWithMultiplePOC";

/** @deprecated */
export class CreatorProfile {
  readonly accountInformation;
  readonly creatorTypes: string[];
  preferredPrimaryFranchises;
  preferredPrimaryPlatforms;
  preferredSecondaryFranchises;
  preferredSecondaryPlatforms;
  connectedChannels;

  constructor(creator) {
    Object.assign(this, creator);
    creator.accountInformation.registrationDate = new LocalizedDate(creator.accountInformation.registrationDate);
    creator.accountInformation.dateOfBirth = new LocalizedDate(creator.accountInformation.dateOfBirth);
  }

  formattedRegistrationDate(locale: string): string {
    return this.accountInformation.registrationDate.formatLong(locale);
  }

  dateOfBirth(): Date {
    return this.accountInformation.dateOfBirth.toDate();
  }

  preferredPrimaryFranchiseLabel(): string {
    return this.preferredPrimaryFranchises.label;
  }

  preferredSecondaryFranchisesLabels(): string[] {
    return this.preferredSecondaryFranchises.map((franchise) => franchise.label);
  }

  preferredPrimaryPlatformLabel(): string {
    return this.preferredPrimaryPlatforms.label;
  }

  preferredSecondaryPlatformsLabels(): string[] {
    return this.preferredSecondaryPlatforms.map((platform) => platform.label);
  }

  socialAccountTypes(): string[] {
    return this.connectedChannels.map((account) => account.type);
  }

  addedCreatorTypes(selectedTypes: string[]): string[] {
    return this.difference(selectedTypes, this.creatorTypes);
  }

  removedCreatorTypes(selectedTypes: string[]): string[] {
    return this.difference(this.creatorTypes, selectedTypes);
  }

  updatedSecondaryFranchises(selectedFranchises: string[]): boolean {
    return (
      this.difference(
        selectedFranchises,
        this.preferredSecondaryFranchises.map((platform) => platform.label)
      ).length > 0 ||
      this.difference(
        this.preferredSecondaryFranchises.map((platform) => platform.label),
        selectedFranchises
      ).length > 0
    );
  }

  updatedPrimaryFranchise(selectedFranchise: string): boolean {
    return this.preferredPrimaryFranchises.label !== selectedFranchise;
  }

  updatedSecondaryPlatforms(selectedPlatforms: string[]): boolean {
    return (
      this.difference(
        selectedPlatforms,
        this.preferredSecondaryPlatforms.map((platform) => platform.label)
      ).length > 0 ||
      this.difference(
        this.preferredSecondaryPlatforms.map((platform) => platform.label),
        selectedPlatforms
      ).length > 0
    );
  }

  addedSecondaryPlatforms(selectedPlatforms: string[]): string[] {
    return this.difference(
      selectedPlatforms,
      this.preferredSecondaryPlatforms.map((platform) => platform.label)
    );
  }

  removedSecondaryPlatforms(selectedPlatforms: string[]): string[] {
    return this.difference(
      this.preferredSecondaryPlatforms.map((platform) => platform.label),
      selectedPlatforms
    );
  }

  updatedPrimaryPlatform(selectedPlatform: string): boolean {
    return this.preferredPrimaryPlatforms.label !== selectedPlatform;
  }

  /** It will return all elements in `minuend` that aren't in `subtrahend` */
  private difference(minuend: string[], subtrahend: string[]): string[] {
    return minuend.filter((element) => !subtrahend.includes(element));
  }
}

export class CreatorWithExpiredAccountsProfile extends CreatorProfile {
  public readonly accounts = [];

  constructor(creator) {
    super(creator);
    this.accounts = creator.connectedAccounts.filter((account) => !account.disconnected);
  }

  socialAccountTypes(): string[] {
    return this.accounts.map((account) => account.type);
  }
}

export class CreatorWithPayableStatusProfile extends CreatorProfile {
  public readonly accounts = [];

  constructor(creator) {
    super(creator);
    this.accounts = creator.connectedAccounts?.filter((account) => !account.disconnected);
  }

  socialAccountTypes(): string[] {
    return this.accounts.map((account) => account.type);
  }
}

export class CreatorWithCreatorCodeProfile extends CreatorProfile {
  public readonly accounts = [];

  constructor(creator) {
    super(creator);
    this.accounts = creator.connectedAccounts?.filter((account) => !account.disconnected);
  }

  socialAccountTypes(): string[] {
    return this.accounts.map((account) => account.type);
  }
}

export class CreatorWithFlaggedStatusProfile extends CreatorWithCreatorCodeProfile {
  public readonly accountInformation: AccountInformationWithFlaggedStatus;

  constructor(creator) {
    super(creator);
    this.accountInformation = creator.accountInformation;
  }
}

export class CreatorWithPrograms extends CreatorWithFlaggedStatusProfile {
  public readonly programs: string[];
  public readonly socialLinks: string[];

  constructor(creator) {
    super(creator);
    this.programs = creator?.programs ? creator?.programs : [];
    this.socialLinks = creator?.socialLinks ? creator?.socialLinks : [];
  }
}

export class CreatorWithMultiplePOC extends CreatorWithPrograms {
  public readonly additionalInformation: AdditionalInformationWithMultiplePOC;
  public readonly creatorCode: CreatorCodeResponse;

  constructor(creator) {
    super(creator);
    this.additionalInformation = creator?.additionalInformation;
    this.creatorCode = creator?.creatorCode;
  }
}

const getCreatorWithExpiredAccounts = async (): Promise<AxiosResponse<CreatorWithExpiredAccountsProfile>> => {
  return client.get("/api/v2/creators").then((response: AxiosResponse<CreatorWithExpiredAccountsProfile>) => {
    response.data = new CreatorWithExpiredAccountsProfile(response.data);
    return response;
  });
};

// Creator With PaymentInfo
const getCreatorWithPayableStatus = async (): Promise<AxiosResponse<CreatorWithPayableStatusProfile>> => {
  return client.get("/api/v3/creators").then((response: AxiosResponse<CreatorWithPayableStatusProfile>) => {
    response.data = new CreatorWithPayableStatusProfile(response.data);
    return response;
  });
};

const getCreatorWithCreatorCode = async (client): Promise<AxiosResponse<CreatorWithCreatorCodeProfile>> => {
  return client.get("/api/v4/creators").then((response: AxiosResponse<CreatorWithCreatorCodeProfile>) => {
    response.data = new CreatorWithCreatorCodeProfile(response.data);
    return response;
  });
};

// Creator With Tier
const getCreatorWithTier = async (): Promise<AxiosResponse<CreatorWithCreatorCodeProfile>> => {
  return client.get("/api/v5/creators").then((response: AxiosResponse<CreatorWithCreatorCodeProfile>) => {
    response.data = new CreatorWithCreatorCodeProfile(response.data);
    return response;
  });
};

// Creator with flagged status
const getCreatorWithFlaggedStatus = async (): Promise<AxiosResponse<CreatorWithFlaggedStatusProfile>> => {
  return client.get("/api/v6/creators").then((response: AxiosResponse<CreatorWithFlaggedStatusProfile>) => {
    response.data = new CreatorWithFlaggedStatusProfile(response.data);
    return response;
  });
};

const getCreatorWithPrograms = async (client): Promise<AxiosResponse<CreatorWithPrograms>> => {
  return client.get("/api/v7/creators").then((response: AxiosResponse<CreatorWithPrograms>) => {
    response.data = new CreatorWithPrograms(response.data);
    return response;
  });
};

const getCreatorWithMultiplePOC = async (client): Promise<AxiosResponse<CreatorWithMultiplePOC>> => {
  return client.get("/api/v8/creators").then((response: AxiosResponse<CreatorWithMultiplePOC>) => {
    response.data = new CreatorWithMultiplePOC(response.data);
    return response;
  });
};

const register = async (creator: CreatorWithCreatorCode) => {
  return client.post("/api/creators", { body: creator });
};

const update = async (creator: CreatorWithCreatorCode): Promise<AxiosResponse<void>> => {
  return (await client.put("/api/creators", { body: creator })) as AxiosResponse<void>;
};

const updatePrefferedValues = async (client, creator: CreatorWithCreatorCode): Promise<AxiosResponse<void>> => {
  return client.put("/api/v2/creators", { body: creator });
};

const sendEmailToPOC = async (email: { subject: string; body: string }): Promise<AxiosResponse<void>> => {
  return (await client.post("/api/creators/poc-email", { body: email })) as AxiosResponse<void>;
};

const updateProfilePicture = async (formData: FormDataBody): Promise<AxiosResponse<void>> => {
  return (await client.upload("/api/avatar", { body: formData })) as AxiosResponse<void>;
};

const CreatorsService = {
  getCreatorWithCreatorCode,
  getCreatorWithPayableStatus,
  getCreatorWithExpiredAccounts,
  register,
  update,
  sendEmailToPOC,
  getCreatorWithTier,
  getCreatorWithFlaggedStatus,
  getCreatorWithPrograms,
  updateProfilePicture,
  updatePrefferedValues,
  getCreatorWithMultiplePOC
};

export default CreatorsService;
