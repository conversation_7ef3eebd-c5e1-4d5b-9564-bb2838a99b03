export type CommonLabelsProps = {
  footer?: {
    how?: string;
    reward?: string;
    perks?: string;
    faq?: string;
    policies?: string;
    legal?: string;
    disclaimer?: string;
    updates?: string;
    terms?: string;
    privacy?: string;
    rights?: string;
    report?: string;
    copyright?: string;
    faqs?: string;
    disclosure?: string;
    policy?: string;
  };
  header?: {
    creatorNetwork?: string;
    home?: string;
    how?: string;
    works?: string;
    perks?: string;
    rewards?: string;
    signIn?: string;
    logIn?: string;
    apply?: string;
    applyNow?: string;
    requestToJoin?: string;
    dashboard?: string;
    opportunities?: string;
    opportunitiesTitle?: string;
    myContent?: string;
    about?: string;
    notifications?: string;
    myProfile?: string;
    signout?: string;
    faq?: string;
    faqs?: string;
    disclosure?: string;
    policy?: string;
    messages?: string;
  };
  buttons?: {
    cancel?: string;
    discard?: string;
    next?: string;
    save?: string;
    browse?: string;
    yes?: string;
    no?: string;
    notifyMe?: string;
    logout?: string;
    remove?: string;
    prev?: string;
    submit?: string;
    connect?: string;
    edit?: string;
    join?: string;
    back?: string;
    send?: string;
    declineTermsAndCondition?: string;
    gotit?: string;
    submitContent?: string;
    upload?: string;
    closeHeader?: string;
  };
  main?: {
    pageNotFound?: string;
    pageNotFoundContent?: string;
    unhandledError?: string;
    unhandledErrorMessage?: string;
    unauthorized?: string;
  };
  labels?: {
    emailAddress?: string;
  };
  contentCard?: {
    approved?: string;
    rejected?: string;
    approvalNotRequired?: string;
    submitted?: string;
    pendingApproval?: string;
    video?: string;
  };
  contentSubmissionStatuses?: {
    submitted?: string;
    justNow?: string;
    today?: string;
    approved?: string;
    rejected?: string;
    approvalNotRequired?: string;
    pendingApproval?: string;
  };
  toasts?: {
    contentSubmittedTitle?: string;
    contentSubmittedDescription?: string;
    declineInvitationTitle?: string;
    declineInvitationDescription?: string;
  };
  months?: {
    January?: string;
    February?: string;
    March?: string;
    April?: string;
    May?: string;
    June?: string;
    July?: string;
    August?: string;
    September?: string;
    October?: string;
    November?: string;
    December?: string;
  };
  toolTip?: {
    badge?: string;
  };
  heroCard?: {
    welcome?: string;
  };
  theSims?: string;
  documentationCenter?: string;
};

export default function labelsCommon(t) {
  return {
    footer: {
      how: t("how"),
      reward: t("reward"),
      perks: t("perks"),
      faq: t("faq"),
      policies: t("policies"),
      legal: t("legal"),
      disclaimer: t("disclaimer"),
      updates: t("updates"),
      terms: t("terms"),
      privacy: t("privacy"),
      rights: t("rights"),
      report: t("report"),
      copyright: t("copyright"),
      faqs: t("faqs"),
      disclosure: t("disclosure"),
      policy: t("policy")
    },
    header: {
      creatorNetwork: t("creatorNetwork"),
      home: t("home"),
      how: t("how"),
      works: t("works"),
      perks: t("perks"),
      rewards: t("opportunities"),
      signIn: t("signIn"),
      logIn: t("logIn"),
      apply: t("apply"),
      applyNow: t("applyNow"),
      requestToJoin: t("requestToJoin"),
      dashboard: t("dashboard"),
      opportunities: t("opportunities"),
      opportunitiesTitle: t("opportunitiesTitle"),
      myContent: t("myContent"),
      documentation: "Documentation",
      about: t("about"),
      notifications: t("notifications"),
      myProfile: t("myProfile"),
      signout: t("signout"),
      faq: t("faq"),
      faqs: t("faqs"),
      disclosure: t("disclosure"),
      policy: t("policy"),
      calendar: t("calendar"),
      topNavigation: t("topNavigation"),
      creatorNetworkHomepage: t("creatorNetworkHomepage"),
      messages: t("messages"),
      paymentInformation: t("paymentInformation")
    },
    buttons: {
      cancel: t("cancel"),
      discard: t("discard"),
      next: t("next"),
      save: t("save"),
      browse: t("browse"),
      yes: t("yes"),
      no: t("no"),
      notifyMe: t("notifyMe"),
      logout: t("logout"),
      remove: t("remove"),
      prev: t("prev"),
      submit: t("submit"),
      connect: t("connect"),
      edit: t("edit"),
      join: t("join"),
      back: t("back"),
      send: t("send"),
      declineTermsAndCondition: t("declineTermsAndCondition"),
      gotit: t("gotit"),
      submitContent: t("submitContent"),
      upload: t("upload"),
      ok: t("ok"),
      close: t("close"),
      closeHeader: t("closeHeader"),
      expand: t("expand"),
      update: t("update")
    },
    main: {
      pageNotFound: t("pageNotFound"),
      pageNotFoundContent: t("pageNotFoundContent"),
      unhandledError: t("unhandledError"),
      unhandledErrorMessage: t("unhandledErrorMessage"),
      unauthorized: t("unauthorized")
    },
    labels: {
      emailAddress: t("emailAddress")
    },
    contentCard: {
      approved: t("approved"),
      rejected: t("rejected"),
      notApproved: t("notApproved"),
      approvalNotRequired: t("approvalNotRequired"),
      submitted: t("submitted"),
      updated: t("updated"),
      pendingApproval: t("pendingApproval"),
      video: t("video"),
      changesRequired: t("changesRequired"),
      inReview: t("inReview"),
      viewChangesRequired: t("viewChangesRequired"),
      inScan: t("inScan"),
      hideChangesRequired: t("hideChangesRequired"),
      sentOn: t("sentOn"),
      additionalDescription: t("additionalDescription"),
      file: t("file"),
      url: t("url"),
      update: t("update"),
      from: t("from"),
      contentNotApproved: t("contentNotApproved"),
      contentApproved: t("contentApproved"),
      viewDetails: t("viewDetails"),
      hideDetails: t("hideDetails")
    },
    contentSubmissionStatuses: {
      submitted: t("submitted"),
      justNow: t("justNow"),
      today: t("today"),
      approved: t("approved"),
      rejected: t("rejected"),
      approvalNotRequired: t("approvalNotRequired"),
      pendingApproval: t("pendingApproval")
    },
    toasts: {
      contentSubmittedTitle: t("toasts.contentSubmittedTitle"),
      contentSubmittedDescription: t("toasts.contentSubmittedDescription"),
      declineInvitationTitle: t("toasts.declineInvitationTitle"),
      declineInvitationDescription: t("toasts.declineInvitationDescription")
    },
    months: {
      January: t("January"),
      February: t("February"),
      March: t("March"),
      April: t("April"),
      May: t("May"),
      June: t("June"),
      July: t("July"),
      August: t("August"),
      September: t("September"),
      October: t("October"),
      November: t("November"),
      December: t("December")
    },
    toolTip: {
      badge: t("toolTip.badge")
    },
    heroCard: {
      welcome: t("welcome")
    },
    theSims: t("theSims"),
    documentationCenter: t("documentationCenter")
  };
}
