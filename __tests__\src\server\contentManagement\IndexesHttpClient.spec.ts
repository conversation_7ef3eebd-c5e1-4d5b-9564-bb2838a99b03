import IndexesHttpClient, { ContentCriteria } from "@src/server/contentModal/IndexesHttpClient";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import Random from "__tests__/factories/Random";
import { documentationPageData } from "__tests__/factories/documentation/documentation";

describe("IndexesHttpClient", () => {
  const indexResponse = documentationPageData();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("finds documentation details with page slug", async () => {
    const slug = "documentation";
    const client = { get: jest.fn().mockReturnValue({ data: indexResponse }) };
    const documentation = new IndexesHttpClient((client as unknown) as TraceableHttpClient);

    const criteria: ContentCriteria = { locale: Random.locale() };

    const response = await documentation.matching(slug, criteria);

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v1/indexes/${slug}`, { query: { ...criteria, published: true } });
    expect(response).toEqual(indexResponse);
  });
});
