import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { PageLabels } from "@src/pages/onboarding/information";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/server/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/server/contentManagement/ConnectAccountsPageMapper";
import { InformationPageLabels } from "@src/server/contentManagement/InformationPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import featureFlags from "@src/utils/feature-flags";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class InformationPagePropController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<PageLabels> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, _res: NextApiResponse): Promise<GetServerSidePropsResult<PageLabels>> {
    try {
      const pageLabels = (await this.contents.getPageLabels(
        this.currentLocale,
        "information"
      )) as BreadcrumbPageLabels &
        CommunicationPreferencesPageLabels &
        CommonPageLabels &
        InformationPageLabels &
        ConnectAccountsPageLabels;
      const futureCreator = this.hasSession(req, `${this.program}.futureCreator`)
        ? this.session(req, `${this.program}.futureCreator`)
        : null;
      const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
        this.identity(req),
        this.program,
        this.defaultAvatar
      );
      const registrationCode = this.hasSession(req, `${this.program}.registrationCode`)
        ? this.session(req, `${this.program}.registrationCode`)
        : null;

      return {
        props: {
          runtimeConfiguration: runtimeConfiguration(),
          pageLabels,
          user: authenticatedUser,
          FLAG_COUNTRIES_BY_TYPE: featureFlags.isCountriesByTypeEnabled(),
          futureCreator: futureCreator as Record<string, unknown>,
          registrationCode: registrationCode as string
        }
      };
    } catch (error) {
      console.error("Error in InformationPagePropController:", error);
      return {
        notFound: true
      };
    }
  }
}
