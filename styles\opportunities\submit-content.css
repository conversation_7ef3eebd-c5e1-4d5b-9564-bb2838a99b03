.submit-content-container {
  @apply absolute z-0 flex w-full flex-col overflow-y-hidden bg-migration-default sm:px-meas8 md:py-meas8;
}
.submit-content-page {
  @apply m-auto mt-meas4 flex h-full min-h-screen w-full max-w-[850px] flex-col items-center px-meas8 font-text-regular text-white md:mt-meas12 md:px-[64px];
}
.submit-content-header-container {
  @apply flex w-full flex-col pt-meas2 xl:px-meas37;
}
.submit-content-header {
  @apply flex w-full flex-row pb-[10px] text-gray-10;
}
.submit-content-header-close {
  @apply mr-meas7 mt-[19px] flex flex-1 justify-end md:mr-[51px] xl:mr-meas8 xl:mt-[17px];
}
.submit-content-page-title {
  @apply w-full border-b border-navy-60 pb-meas9 text-center font-display-bold text-gray-10 xs:text-mobile-h3 md:border-white md:border-opacity-30 md:pb-[25px] md:text-tablet-h3 lg:text-desktop-h3;
}
.submit-content-page-content-guidelines-container {
  @apply mt-[28px] md:mt-[38px] xl:mt-[24px];
}
.submit-content-page-content-guidelines-title {
  @apply mb-meas4 font-display-bold xs:text-mobile-h4 md:mb-[28px] md:text-tablet-h4 lg:text-desktop-h4 xl:mb-meas10;
}
.submit-content-page-content-guidelines-description {
  @apply mb-meas10  text-gray-10 xs:text-mobile-body-large md:mb-meas13 md:text-tablet-body-large lg:text-desktop-body-large xl:mb-meas10;
}
.submit-content-page-content-guidelines-pointer {
  @apply mb-meas4 flex  xs:text-mobile-body-large md:mb-meas13 md:text-tablet-body-large lg:text-desktop-body-large xl:mb-meas10;
}
.submit-content-page-content-guidelines-pointer-text {
  @apply pl-meas8 pr-meas4;
}
.submit-content-page-content-guidelines-pointer-icon {
  @apply mt-[3px] h-meas10 w-meas10;
}
.submit-content-page-content-guidelines-sub-description {
  @apply mt-meas12  xs:text-mobile-body-large md:mt-meas16 md:text-tablet-body-large lg:text-desktop-body-large xl:mt-meas10;
}
.submit-content-page-content-url {
  @apply mt-meas13 flex w-full xl:mt-meas10;
}
.submit-content-page-content-input-url-container {
  @apply mb-meas13 w-full border-b border-navy-60 md:mb-meas10 md:border-white md:border-opacity-30 xl:mb-meas12;
}
.content-url-input-box .input-box-label {
  @apply text-gray-10;
}
.submit-content-page-content-url .input-box {
  @apply mr-[50px] w-full md:mr-meas0 md:w-[404px];
}
.content-url-input-box .input-box {
  @apply h-[48px];
}
.content-url-input-box input {
  @apply h-[48px];
}
.submit-content-page-content-url-delete {
  @apply ml-meas2 mt-meas10 h-[48px] cursor-pointer rounded border border-gray-10 p-[14px];
}
.submit-content-page-content-url-icon {
  @apply h-[18px] text-gray-10;
}
.submit-content-page-content-url-add-more {
  @apply mb-meas18 mt-meas13 flex cursor-pointer md:mb-[74px] xl:mb-[60px] xl:mt-meas16;
}
.submit-content-page-content-url-add-more-icon {
  @apply h-meas10 w-meas10 text-gray-60;
}
.submit-content-page-content-url-add-more-text {
  @apply ml-[6px] self-center font-display-bold text-gray-10 xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.submit-content-page-content-footer-button {
  @apply w-full md:mb-[40px] md:text-right xl:mb-[150px];
}
.submit-content-page-content-footer {
  @apply mb-meas9 w-full;
}
.submit-content-outline-close {
  @apply h-[20px] w-[20px] text-gray-90;
}
.submit-content-outline-check {
  @apply h-[20px] w-[20px] text-success-60;
}
