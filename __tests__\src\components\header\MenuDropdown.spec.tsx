import MenuDropdown from "@components/header/MenuDropdown";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react";
import { axe } from "jest-axe";
import { useRouter } from "next/router";

describe("MenuDropdown", () => {
  const mockAnalytics = ({
    visitedMyProfile: jest.fn(),
    signedOutOfCreatorNetwork: jest.fn()
  } as unknown) as BrowserAnalytics;

  const labels = {
    myProfile: "My Profile",
    signout: "Sign Out"
  };

  const mockChildren = <div data-testid="avatar">Avatar</div>;
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    document.querySelectorAll = jest.fn().mockImplementation(() => {
      const elements = [{ focus: jest.fn() }, { focus: jest.fn() }];
      elements.indexOf = jest.fn().mockReturnValue(0);
      return elements;
    });
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush, locale: "en-us" });
  });

  it("renders correctly in initial closed state", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    expect(screen.getByTestId("avatar")).toBeInTheDocument();

    // Menu should be hidden initially
    const menu = screen.getByRole("button", { name: /My Profile/i }).parentElement;
    expect(menu).toHaveClass("ea-header-web-profile-menu-hide");
  });

  it("opens menu on mouse over", async () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const container = screen.getByTestId("avatar").parentElement;
    fireEvent.mouseOver(container);

    await waitFor(() => {
      const menu = screen.getByRole("button", { name: /My Profile/i }).parentElement;
      expect(menu).toHaveClass("ea-header-web-profile-menu");
    });
  });

  it("closes menu on blur if no key event", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const container = screen.getByTestId("avatar").parentElement;
    fireEvent.mouseOver(container);
    fireEvent.blur(container);

    const menu = screen.getByRole("button", { name: /My Profile/i }).parentElement;
    expect(menu).toHaveClass("ea-header-web-profile-menu-hide");
  });

  it("keeps menu open on blur if key event was called", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const container = screen.getByTestId("avatar").parentElement;
    fireEvent.mouseOver(container);

    // Trigger Enter key which should set isKeyDownEventCalled to true
    fireEvent.keyDown(container, { key: "Enter" });

    // Blur should not close the menu due to isKeyDownEventCalled being true
    fireEvent.blur(container);

    const menu = screen.getByRole("button", { name: /My Profile/i }).parentElement;
    expect(menu).toHaveClass("ea-header-web-profile-menu");
  });

  it("navigates to profile when profile button is clicked", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const profileButton = screen.getByRole("button", { name: /My Profile/i });
    fireEvent.mouseDown(profileButton);

    expect(mockAnalytics.visitedMyProfile).toHaveBeenCalledWith({ locale: "en-us" });
    expect(mockPush).toHaveBeenCalledWith("/profile");
  });

  it("logs out when logout button is clicked", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const logoutButton = screen.getByRole("button", { name: /Sign Out/i });
    fireEvent.mouseDown(logoutButton);

    expect(mockAnalytics.signedOutOfCreatorNetwork).toHaveBeenCalledWith({ locale: "en-us" });
    expect(mockPush).toHaveBeenCalledWith("/api/logout");
  });

  it("navigates to profile on Enter key with profile menu item", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const profileButton = screen.getByRole("button", { name: /My Profile/i });
    fireEvent.keyDown(profileButton, { key: "Enter" });

    expect(mockAnalytics.visitedMyProfile).toHaveBeenCalledWith({ locale: "en-us" });
    expect(mockPush).toHaveBeenCalledWith("/profile");
  });

  it("logs out on Enter key with logout menu item", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const logoutButton = screen.getByRole("button", { name: /Sign Out/i });
    fireEvent.keyDown(logoutButton, { key: "Enter" });

    expect(mockAnalytics.signedOutOfCreatorNetwork).toHaveBeenCalledWith({ locale: "en-us" });
    expect(mockPush).toHaveBeenCalledWith("/api/logout");
  });

  it("closes menu on Tab key", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const container = screen.getByTestId("avatar").parentElement;
    fireEvent.mouseOver(container);
    fireEvent.keyDown(container, { key: "Tab" });

    const menu = screen.getByRole("button", { name: /My Profile/i }).parentElement;
    expect(menu).toHaveClass("ea-header-web-profile-menu-hide");
  });

  it("navigates menu with arrow keys", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const container = screen.getByTestId("avatar").parentElement;

    // Simulate arrow down key navigation
    fireEvent.keyDown(container, { key: "ArrowDown" });

    // Simulate arrow up key navigation
    fireEvent.keyDown(container, { key: "ArrowUp" });

    // Both should call document.querySelectorAll
    expect(document.querySelectorAll).toHaveBeenCalledTimes(2);
  });

  it("correctly handles tab key on profile button", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const profileButton = screen.getByRole("button", { name: /My Profile/i });
    fireEvent.mouseOver(profileButton.parentElement?.parentElement!);
    fireEvent.keyDown(profileButton, { key: "Tab" });

    const menu = profileButton.parentElement;
    expect(menu).toHaveClass("ea-header-web-profile-menu-hide");
  });

  it("correctly handles tab key on logout button", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const logoutButton = screen.getByRole("button", { name: /Sign Out/i });
    fireEvent.mouseOver(logoutButton.parentElement?.parentElement!);
    fireEvent.keyDown(logoutButton, { key: "Tab" });

    const menu = logoutButton.parentElement;
    expect(menu).toHaveClass("ea-header-web-profile-menu-hide");
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("applies correct focus style when menu is open", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const container = screen.getByTestId("avatar").parentElement;
    fireEvent.mouseOver(container);

    expect(container).toHaveClass("ea-header-web-profile-image-container-focus");
    expect(container).not.toHaveClass("ea-header-web-profile-image-container");
  });

  it("doesn't navigate to profile when mouse down is not primary click", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const profileButton = screen.getByRole("button", { name: /My Profile/i });
    fireEvent.mouseDown(profileButton, { button: 1 }); // Right click

    expect(mockAnalytics.visitedMyProfile).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });

  it("doesn't logout when mouse down is not primary click", () => {
    render(
      <MenuDropdown labels={labels} analytics={mockAnalytics}>
        {mockChildren}
      </MenuDropdown>
    );

    const logoutButton = screen.getByRole("button", { name: /Sign Out/i });
    fireEvent.mouseDown(logoutButton, { button: 2 }); // Middle click

    expect(mockAnalytics.signedOutOfCreatorNetwork).not.toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
  });
});
