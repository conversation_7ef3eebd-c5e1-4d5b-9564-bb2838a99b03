import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import ConnectedDiscordAccount from "@src/server/channels/discord/ConnectedDiscordAccount";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .delete(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const params = req.query;
    const identity = req.session.identity;
    const discordAccount: ConnectedDiscordAccount = ApiContainer.get("discordAccount");
    await discordAccount.disconnectDiscordAccount(identity.id, params.id as string);

    res.status(200);
    res.end();
  });

export default router.handler({ onError });
