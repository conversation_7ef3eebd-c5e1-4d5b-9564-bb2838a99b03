import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import IndexesHttpClient from "@src/server/contentModal/IndexesHttpClient";
import { NextApiResponse } from "next";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import DocumentationPagePropsController from "@src/serverprops/controllers/DocumentationPagePropsController";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";
import ContentManagementService from "@src/services/ContentManagementService";

jest.mock("@src/analytics/BrowserAnalytics");
jest.mock("@src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations");

describe("DocumentationPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const documentationClient = ({
    matching: jest.fn()
  } as unknown) as IndexesHttpClient;
  const contentManagementClient = ({
    getPageLabels: jest.fn()
  } as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => {
    jest.clearAllMocks();
    (serverSideTranslations as jest.Mock).mockResolvedValue({});
  });

  it("gets server side props for documentation page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = { id: "123" };
    const user = { id: "123", name: "Test User" };
    const indexPage = { title: "Test Documentation" };
    const configuration = {};
    const pageLabels = {};

    req.session = { identity: identity };
    (AuthenticatedUserFactory.fromIdentity as jest.Mock).mockReturnValue(user);
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (documentationClient.matching as jest.Mock).mockResolvedValue({ data: indexPage });
    (contentManagementClient.getPageLabels as jest.Mock).mockResolvedValue(pageLabels);

    const controller = new DocumentationPagePropsController(
      options,
      documentationClient,
      contentManagementClient,
      currentLocale,
      ""
    );
    const result = await controller.handle(req, res);

    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        indexPage: indexPage,
        pageLabels,
        user: user,
        locale: currentLocale
      }
    });
  });

  it("gets server side props for unauthenticated user", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const indexPage = { title: "Test Documentation" };
    const configuration = {};
    const pageLabels = {};

    req.session = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (documentationClient.matching as jest.Mock).mockResolvedValue({ data: indexPage });
    (contentManagementClient.getPageLabels as jest.Mock).mockResolvedValue(pageLabels);

    const controller = new DocumentationPagePropsController(
      options,
      documentationClient,
      contentManagementClient,
      currentLocale,
      ""
    );
    const result = await controller.handle(req, res);

    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels,
        indexPage: indexPage,
        user: null,
        locale: currentLocale
      }
    });
  });

  it("returns notFound when documentation is not found", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = { id: "123" };
    req.session = { identity: identity };

    (documentationClient.matching as jest.Mock).mockRejectedValue(new ArticleNotFound("Documentation not found"));
    (contentManagementClient.getPageLabels as jest.Mock).mockResolvedValue({ pageLabels: {} });

    const controller = new DocumentationPagePropsController(
      options,
      documentationClient,
      contentManagementClient,
      currentLocale,
      ""
    );
    const result = await controller.handle(req, res);

    expect(result).toEqual({ notFound: true });
  });

  it("throws error for unexpected exceptions", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const error = new Error("Unexpected error");
    const identity = { id: "123" };
    req.session = { identity: identity };

    (documentationClient.matching as jest.Mock).mockRejectedValue(error);
    (contentManagementClient.getPageLabels as jest.Mock).mockResolvedValue({ pageLabels: {} });

    const controller = new DocumentationPagePropsController(
      options,
      documentationClient,
      contentManagementClient,
      currentLocale,
      ""
    );

    await expect(controller.handle(req, res)).rejects.toThrow(error);
  });
});
