import { renderHook, waitFor } from "@testing-library/react";
import { usePayableStatus } from "@src/components/pages/payment-information/usePayableStatus";
import CreatorsService from "@src/services/CreatorsService";
import PaymentInformationService from "@src/services/paymentInformation/PaymentInformationService";
import {
  DefaultPaymentDateRange,
  PaymentStatusType,
  ProgramCodeType
} from "@src/services/paymentsStatistics/PaymentsService";
import { useDependency } from "@src/context/DependencyContext";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { aCreatorWithPayableStatus } from "__tests__/factories/creators/CreatorWithPayableStatus";
import { aPaymentHistoryDetail } from "__tests__/factories/paymentsHistory/aPaymentHistoryDetail";
import { DollarAmount } from "@src/services/paymentInformation/DollarAmount";

jest.mock("../../../../src/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../../../src/services/CreatorsService"),
    getCreatorWithPrograms: jest.fn()
  };
});
jest.mock("../../../../src/services/paymentInformation/PaymentInformationService");
jest.mock("../../../../src/context/DependencyContext");

xdescribe("usePayableStatus", () => {
  const stableDispatch = jest.fn();
  const initializePagination = jest.fn();
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ errorHandler });
  });

  const defaultPaymentDateRange = new DefaultPaymentDateRange("2021-09-01");
  const paymentCriteria = {
    startDate: defaultPaymentDateRange.startDate,
    endDate: defaultPaymentDateRange.endDate,
    page: 1,
    size: 1
  };
  it("should initialize with default values", async () => {
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentDetailResponse = aPaymentHistoryDetail({
      count: 0,
      total: 0,
      totalPaidAmount: new DollarAmount("0.00"),
      totalPendingAmount: new DollarAmount("0.00"),
      paymentsHistory: []
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentDetailResponse
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    const { result } = renderHook(() =>
      usePayableStatus("PAYEE_ONBOARDING", "en-US", stableDispatch, paymentCriteria, initializePagination)
    );

    await waitFor(() => {
      expect(result.current.paymentsIframe).toEqual({ id: null, embeddableUrl: null });
      expect(result.current.isPayable).toBeNull();
      expect(result.current.paymentsHistory).toEqual({});
      expect(result.current.isCreatorCodeAssigned).toBe(false);
    });
  });

  it("should fetch creator information and set state", async () => {
    const defaultPaymentDateRange = new DefaultPaymentDateRange("2021-09-01");
    const paymentCriteria = {
      startDate: defaultPaymentDateRange.startDate,
      endDate: defaultPaymentDateRange.endDate,
      page: 1,
      size: 1
    };
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentDetailResponse = aPaymentHistoryDetail();
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        ...paymentDetailResponse,
        count: 1
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    const { result } = renderHook(() =>
      usePayableStatus("PAYMENT_HISTORY", "en-US", stableDispatch, paymentCriteria, initializePagination)
    );

    await waitFor(() => {
      expect(result.current.paymentsHistory.count).toEqual(1);
    });
  });

  it("should display the iframe id", async () => {
    const payableCreatorWithCode = aCreatorWithPayableStatus({
      accountInformation: {
        isPayable: true,
        creatorCode: "ABCD1234",
        dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1))
      }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreatorWithCode
    });
    const paymentDetailResponse = aPaymentHistoryDetail({
      count: 0,
      total: 0,
      totalPaidAmount: new DollarAmount("0.00"),
      totalPendingAmount: new DollarAmount("0.00"),
      paymentsHistory: []
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentDetailResponse
      }),
      getPaymentsIFrameUrl: jest.fn().mockResolvedValue({
        data: {
          embeddableUrl: "about:blank",
          id: "001K000001iEZ3bIAG"
        }
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    const { result } = renderHook(() =>
      usePayableStatus("PAYEE_ONBOARDING", "en-US", stableDispatch, paymentCriteria, initializePagination)
    );

    await waitFor(() => {
      expect(result.current.paymentsIframe.id).toEqual("001K000001iEZ3bIAG");
    });
  });

  it("verify programcode with payment crtiteria", async () => {
    const defaultPaymentDateRange = new DefaultPaymentDateRange("2021-09-01");
    const paymentCriteria = {
      startDate: defaultPaymentDateRange.startDate,
      endDate: defaultPaymentDateRange.endDate,
      page: 1,
      size: 10,
      programCode: "affiliate" as ProgramCodeType,
      status: "ALL" as PaymentStatusType
    };
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentDetailResponse = aPaymentHistoryDetail();
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        ...paymentDetailResponse,
        count: 1
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderHook(() => {
      usePayableStatus("PAYMENT_HISTORY", "en-US", stableDispatch, paymentCriteria, initializePagination);
    });

    await waitFor(() => {
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledWith({
        ...paymentCriteria
      });
    });
  });
});
