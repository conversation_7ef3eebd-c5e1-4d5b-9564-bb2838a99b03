import { render, screen } from "@testing-library/react";
import { useRouter } from "next/router";
import { axe } from "jest-axe";
import Custom500 from "../../../src/pages/500";
import { useDependency } from "@src/context/DependencyContext";
import { mockMatchMedia } from "__tests__/helpers/window";
import config from "config";

jest.mock("next/config", () => () => ({
  publicRuntimeConfig: {
    SUPPORTED_LOCALES: JSON.stringify(config.SUPPORTED_LOCALES)
  }
}));
jest.mock("../../../src/context/DependencyContext");
describe("Custom500", () => {
  mockMatchMedia();
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  const errorPageLabels = {
    code: "500",
    title: "Oops! Something has gone wrong.",
    description: "The page youre looking for seems to be broken. Here are some helpful links instead.",
    header: {
      dashboard: "Dashboard",
      home: "Home",
      works: "How it works",
      faqs: "FAQs"
    }
  };

  it("shows title and description", () => {
    render(<Custom500 />);

    expect(screen.getByText(errorPageLabels.code)).toBeInTheDocument();
    expect(screen.getByText(errorPageLabels.title)).toBeInTheDocument();
    expect(screen.getByText(errorPageLabels.description)).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<Custom500 />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
