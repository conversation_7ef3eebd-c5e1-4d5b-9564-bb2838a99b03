.payments-details {
  @apply border-t-[2px] border-card-border;
}

.payment-details-banner {
  @apply mt-meas12;
}
.payment-details-banner .banner-wrapper {
  @apply mt-meas10 items-start sm:mt-meas16;
}
.payment-details-heading {
  @apply font-text-bold leading-6 xs:text-mobile-body-default md:text-[24px] md:text-tablet-body-default md:!leading-7 md:tracking-[1px] lg:text-desktop-body-default;
}

.payment-details-overview {
  @apply my-meas16;
}

.payment-details-date-range {
  @apply pb-[20px] !leading-7 xs:text-mobile-body-small md:text-tablet-body-default lg:text-desktop-body-default;
}

.payment-details-amounts-wrapper {
  @apply flex flex-col md:flex-row;
}
.payment-details-amounts-wrapper .amount-card-heading-text {
  @apply text-desktop-body-large;
}

.payment-details-amounts-section {
  @apply flex-1;
}
.payment-details-amounts-section .amount-card-wrapper {
  @apply mb-meas0;
}
.payment-details-amounts-section .amount-card-wrapper.amount-card-paid {
  @apply mb-[15px] md:mb-meas0;
}
.payment-details-amounts-section .amount-card-wrapper.disabled {
  @apply opacity-50;
}
.payment-details-amounts-section .amount-card-medium-text {
  @apply leading-[26px] xl:leading-10;
}

.payment-details-amounts-section.pending-data {
  @apply md:ml-[25px];
}

.payment-details-transaction-heading {
  @apply mb-meas5 mt-[20px];
}

.payment-details-transaction-history-text {
  @apply mb-meas12 xs:text-mobile-body-small md:text-tablet-body-default md:!leading-7;
}

.payment-details-link {
  @apply cursor-pointer font-bold underline;
}

.payment-details-transaction-history-grid .content-grid-last-record {
  @apply border-b-[1px];
}

.payment-details-transaction-history-grid .content-grid-last-record td {
  @apply pb-meas8;
}

.payment-details-transaction-history-grid .pagination-container {
  @apply my-auto mb-[22px] mt-[6px];
}

.payment-details-transaction-history-grid.no-records .content-grid-head {
  @apply md:hidden;
}
.payment-details-transaction-history-grid.no-records .content-grid-wrapper {
  @apply w-full;
}
.payment-details-transaction-history-grid.no-records .transaction-grid-no-data-section {
  @apply md:pb-meas8;
}

.payment-details-transaction-history-wrapper .transaction-record-opportunityTitle {
  @apply mb-meas4;
}

.payment-details-transaction-history-wrapper .pagination-container {
  @apply my-auto mb-meas34 mt-meas24;
}

.payment-details-overview-header {
  @apply inline-flex w-full items-start justify-between;
}

.payment-details-filters .filter-pill-wrapper {
  @apply mb-meas4 mr-meas4;
}

.payment-details-filters {
  @apply mt-meas2 inline-flex flex-wrap items-center pb-[10px];
}

.payments-filter-title {
  @apply mb-meas4 mr-meas2;
}

/* Adding for bug fix. This will be removed once we implement new calendar component. This will go into core-ui-kit calendar css */
* {
  font-variant-ligatures: none;
}
