import "reflect-metadata";
import ViewFacebookPagesController from "@src/server/channels/facebook/ViewFacebookPagesController";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";

describe("ViewFacebookPagesController", () => {
  let controller: ViewFacebookPagesController;

  beforeEach(() => jest.clearAllMocks());

  it("gets all Facebook pages from session", async () => {
    const creatorFacebookPages = {
      pages: [{ id: "2347sdhfjsd", accessToken: "asfdhjsadh235667887", name: "HariTest" }]
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/facebook-pages",
      session: { fbPages: creatorFacebookPages }
    });
    controller = new ViewFacebookPagesController();

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creatorFacebookPages);
  });

  it("return 404 if pages are not in session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/facebook-pages",
      session: {}
    });
    controller = new ViewFacebookPagesController();

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(404);
  });
});
