import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../../src/ApiContainer";
import { createRouter } from "next-connect";
import session from "@src/middleware/Session";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ViewSubmittedContentController from "@src/submittedContent/ViewSubmittedContentController";
import onError from "@src/middleware/JsonErrorHandler";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ViewSubmittedContentController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
