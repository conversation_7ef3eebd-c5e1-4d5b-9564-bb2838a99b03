import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiResponse } from "next";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import AccountDeactivatedPagePropsController from "@src/serverprops/controllers/AccountDeactivatedPagePropsController";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");

describe("AccountDeactivatedPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the account deactivated page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const deactivatedAccount = {
      defaultGamerTag: "083123"
    };
    req.session = { [`${program}.deactivatedAccount`]: deactivatedAccount };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {};
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new AccountDeactivatedPagePropsController(options, contents, currentLocale);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "error");
    expect(props).toEqual({
      props: {
        pageLabels: labels,
        runtimeConfiguration: configuration,
        deactivatedAccount
      }
    });
  });

  it("returns Not Foud if the creator account is not deactivated", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = {};
    const controller = new AccountDeactivatedPagePropsController(options, contents, currentLocale);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledTimes(0);
    expect(runtimeConfiguration).toHaveBeenCalledTimes(0);
    expect(props).toEqual({ notFound: true });
  });
});
