import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectTikTokAccountController from "@src/server/channels/tiktok/ConnectTikTokAccountController";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import config from "config";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";

describe("ConnectTikTokAccountController", () => {
  let controller: ConnectTikTokAccountController;
  const redirectUrl = new URL("http://localhost:3040/api/tiktok-connect");
  let connectedAccounts;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    connectedAccounts = ({ connectTikTokAccount: jest.fn() } as unknown) as ConnectedAccountsHttpClient;
    controller = new ConnectTikTokAccountController(connectedAccounts, redirectUrl);
    config.hasAllTikTokScopes = jest.fn().mockReturnValue(true);
  });

  it("connects an TikTok account for a creator", async () => {
    const code = "41b16e33-7fcc-4ec2-abf3-2d04c2662ece";
    const creatorId = "8c4db184-9735-4a07-8ebc-9f7f6d8b46c4";
    const scopes = "user.info.profile,user.info.stats,video.list";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/tiktok-connect?code=${code}&scopes=${scopes}`,
      session: {
        ...session,
        identity: Identity.fromStored({
          type: "CREATOR",
          id: creatorId
        } as StoredIdentity)
      }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
    expect(req.session.accountType).toEqual("TikTok");
    expect(req.session.save).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectTikTokAccount).toHaveBeenCalledWith({
      code,
      creatorId,
      redirectUri: redirectUrl.toString(),
      nucleusId: undefined
    });
  });

  it("closes current window and adds error to session if TikTok scopes are not present", async () => {
    const code = "41b16e33-7fcc-4ec2-abf3-2d04c2662ece";
    const creatorId = "8c4db184-9735-4a07-8ebc-9f7f6d8b46c4";
    const scopes = "user.info.basic";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/tiktok-connect?code=${code}&scopes=${scopes}`,
      session: { ...session }
    });
    (config.hasAllTikTokScopes as jest.Mock).mockReturnValue(false);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session["invalid-tiktok-scope"]).toEqual(true);
  });

  it("closes current window and adds error to session for on interested creator if TikTok scopes are not present", async () => {
    const code = "41b16e33-7fcc-4ec2-abf3-2d04c2662ece";
    const nucleusId = "8bfc4cad-611c-4f54-8214-fac9d43f320f";
    const scopes = "user.info.basic";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/tiktok-connect?code=${code}&scopes=${scopes}`,
      session: { ...session, nucleusId }
    });
    (config.hasAllTikTokScopes as jest.Mock).mockReturnValue(false);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.error).toEqual({
      code: "invalid-tiktok-scope",
      message: "Invalid Tiktok scope"
    });
  });
});
