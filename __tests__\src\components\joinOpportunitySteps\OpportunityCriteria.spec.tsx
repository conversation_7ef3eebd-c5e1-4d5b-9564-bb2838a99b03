import { render, screen, waitFor, within } from "@testing-library/react";
import OpportunityCriteria from "@components/joinOpportunitySteps/OpportunityCriteria";
import { anOpportunityWithPerksPaymentsAndGameCodes } from "../../../factories/opportunities/OpportunityWithPerks";
import { opportunityCriteriaLabels as criteriaLabels, commonTranslationsLabels as layout } from "../../../translations";
import { useAppContext } from "@src/context";
import { renderWithToast, triggerAnimationEnd } from "../../../helpers/toast";
import userEvent from "@testing-library/user-event";
import { onToastClose, UGX_OPPORTUNITY } from "../../../../src/utils";
import { useDependency } from "@src/context/DependencyContext";
import { renderPage } from "__tests__/helpers/page";
import { useRouter } from "next/router";
import Random from "__tests__/factories/Random";

jest.mock("../../../../src/context", () => ({
  ...(jest.requireActual("../../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../../src/utils", () => ({
  ...(jest.requireActual("../../../../src/utils") as Record<string, unknown>),
  onToastClose: jest.fn()
}));
jest.mock(".../../../../src/context/DependencyContext");

describe("OpportunityCriteria", () => {
  const opportunityWithoutCodes = anOpportunityWithPerksPaymentsAndGameCodes({
    id: "a59a18a8-58ad-49f6-b838-4aae2e989236",
    hasGameCodes: true,
    hasDeliverables: true
  });
  const analytics = { startedJoinOpportunityFlow: jest.fn(), completedJoinOpportunityFlow: jest.fn() };
  const opportunityCriteriaProps = {
    criteriaLabels,
    opportunity: opportunityWithoutCodes,
    layout,
    analytics,
    participationStatus: { invitationId: Random.uuid() }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ errorHandler: jest.fn() });
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en_us" }));
  });

  it("shows updated join opportunity creator network terms", async () => {
    render(<OpportunityCriteria {...opportunityCriteriaProps} />);

    expect(screen.getByRole("heading", { name: criteriaLabels.title })).toBeInTheDocument();
    expect(screen.getByText(criteriaLabels.message)).toBeInTheDocument();
    const { getAllByRole } = within(
      screen.getByRole("list", {
        name: criteriaLabels.title
      })
    );
    expect(getAllByRole("listitem")).toHaveLength(4);
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch,
      state: { isError: errorMessage }
    });
    const { unmount } = renderWithToast(<OpportunityCriteria {...opportunityCriteriaProps} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong.");
    expect(getByRole("button", { name: /Close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(await screen.findByText("Oops! Something has gone wrong."));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it('renders criteria labels when opportunity type is "the_sims_pro"', () => {
    const opportunityCriteriaProps = {
      criteriaLabels,
      opportunity: { ...opportunityWithoutCodes, type: UGX_OPPORTUNITY },
      layout,
      analytics,
      participationStatus: { invitationId: Random.uuid() }
    };

    renderPage(<OpportunityCriteria {...opportunityCriteriaProps} />);

    expect(screen.getByText(criteriaLabels.reviewYourCreatorKitAgreement)).toBeInTheDocument();
    expect(screen.getByText(criteriaLabels.acknowledgement)).toBeInTheDocument();
    expect(screen.queryByText(criteriaLabels.disclosure)).not.toBeInTheDocument();
    expect(screen.queryByText(criteriaLabels.contentCreation)).not.toBeInTheDocument();
    expect(screen.queryByText(criteriaLabels.embargoPeriod)).not.toBeInTheDocument();
    expect(screen.queryByText(criteriaLabels.trustAndSafety)).not.toBeInTheDocument();
  });

  it('renders the criteria list for opportunity types other than "the_sims_pro"', async () => {
    const otherOpportunityCriteriaProps = {
      criteriaLabels,
      opportunity: { ...opportunityWithoutCodes, type: "other_prop" },
      layout,
      analytics,
      participationStatus: { invitationId: Random.uuid() }
    };

    renderPage(<OpportunityCriteria {...otherOpportunityCriteriaProps} />);

    expect(screen.getByText(criteriaLabels.disclosure)).toBeInTheDocument();
    expect(screen.getByText(criteriaLabels.contentCreation)).toBeInTheDocument();
    expect(screen.getByText(criteriaLabels.embargoPeriod)).toBeInTheDocument();
    expect(screen.getByText(criteriaLabels.trustAndSafety)).toBeInTheDocument();
    expect(screen.getByText(criteriaLabels.message)).toBeInTheDocument();
    expect(screen.queryByText(criteriaLabels.reviewYourCreatorKitAgreement)).not.toBeInTheDocument();
    expect(screen.queryByText(criteriaLabels.acknowledgement)).not.toBeInTheDocument();
  });
});
