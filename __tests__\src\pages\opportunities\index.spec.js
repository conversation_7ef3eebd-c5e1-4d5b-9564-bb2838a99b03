import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";
import "next/config";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { aPerk, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { mockMatchMedia } from "__tests__/helpers/window";
import { aParticipationStatusWithSubmissionStatus } from "__tests__/factories/opportunities/ParticipationStatusWithSubmissionStatus";
import {
  anOpportunityWithActivationWindow,
  aOpportunitiesWithoutSearch
} from "__tests__/factories/opportunities/OpportunityWithPerks";
import OpportunityService, { OpportunityWithActivationWindow } from "@src/services/OpportunityService";
import OperationsService from "@src/services/OperationsService";
import { useAppContext } from "@src/context";
import { renderPage } from "__tests__/helpers/page";
import Opportunities from "@src/pages/opportunities";
import { useDependency } from "../../../../src/context/DependencyContext";
import { anOpportunityEvent } from "../../../factories/opportunities/OpportunityEvent";
import { aContentSubmission } from "../../../factories/opportunities/OpportunityContentSubmission";
import { aParticipationDetails } from "../../../factories/opportunities/ParticipationDetails";
import { aCreatorCode } from "../../../factories/opportunities/CreatorCode";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../../../src/context/DependencyContext");
jest.mock("../../../../src/context", () => ({
  ...jest.requireActual("../../../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: { isLoading: false } })
}));
jest.mock("../../../../src/services/OpportunityService", () => {
  return {
    ...jest.requireActual("../../../../src/services/OpportunityService"),
    __esModule: true,
    default: jest.fn()
  };
});
jest.mock("../../../../src/services/OperationsService", () => ({
  ...jest.requireActual("../../../../src/services/OperationsService"),
  __esModule: true,
  default: jest.fn()
}));

describe("Opportunities Page", () => {
  mockMatchMedia();
  let initialOpportunities;
  let initialOpportunitiesParticipationStatus;
  let analytics = {};
  const router = {
    locale: "en-us",
    push: jest.fn(),
    isReady: true
  };
  const opportunityProps = {
    OPPORTUNITY_WITH_PERKS: true,
    user: {},
    analytics: analytics
  };
  const perks = [
    aPerk({ name: "Paid", code: "PAID" }),
    aPerk({ name: "VIP Event", code: "VIP_EVENT" }),
    aPerk({ name: "Collab", code: "COLLAB" }),
    aPerk({ name: "Exclusive Preview", code: "EXCLUSIVE_PREVIEW" })
  ];
  const metadataService = {
    getPlatformsMatching: jest.fn().mockResolvedValue([aPlatform({ label: "XBOX" }), aPlatform({ label: "PC" })])
  };
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    const participationId = "OPPO123";
    initialOpportunitiesParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null }),
      aParticipationStatusWithSubmissionStatus({ id: "983", participationId, status: "JOINED" }),
      aParticipationStatusWithSubmissionStatus({ id: "939", participationId: "OPPO234", status: "NA" }),
      aParticipationStatusWithSubmissionStatus({ id: "940", status: "DECLINED", participationId: null })
    ];
    initialOpportunities = aOpportunitiesWithoutSearch({
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa123",
            perks: [
              { name: "Paid", code: "PAID" },
              { name: "Collab", code: "COLLAB" }
            ]
          })
        ),
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "983",
            perks: [
              { name: "Collab", code: "COLLAB" },
              { name: "VIP Event", code: "VIP_EVENT" }
            ],
            title: "Simple Joined opp #983",
            hasDeliverables: true
          })
        ),
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "939",
            title: "Simple open opp",
            perks: [{ name: "VIP Event", code: "VIP_EVENT" }],
            hasGameCodes: false
          })
        ),
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "940",
            title: "Simple opp declined",
            perks: [{ name: "Design Council", code: "DESIGN_COUNCIL" }],
            description: "Simple declined description"
          })
        )
      ],
      count: 4,
      total: 4
    });
    useDependency.mockReturnValue({
      metadataClient: {},
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["sims_creator_program"] }
      },
      errorHandler
    });
    MetadataService.mockReturnValue(metadataService);
    const searchOpportunities = jest.fn().mockResolvedValue({
      data: initialOpportunities
    });
    const getParticipationStatusWithSubmissionInformation = jest.fn().mockResolvedValue({
      data: initialOpportunitiesParticipationStatus
    });

    const opportunityService = {
      searchOpportunities,
      getParticipationStatusWithSubmissionInformation
    };
    OpportunityService.mockReturnValue(opportunityService);
    MetadataService.mockReturnValue(metadataService);
    const operationsService = { viewAssignedGameCodes: jest.fn().mockResolvedValue([]) };
    OperationsService.mockReturnValue(operationsService);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [] }
    });
  });

  it("shows opportunities without perks", async () => {
    const participationStatuses = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    const opportunitiesPage = {
      opportunities: [
        new OpportunityWithActivationWindow(
          anOpportunityWithActivationWindow({
            id: "123",
            title: "fifa 123",
            perks: [{ name: "Paid", code: "PAID" }]
          })
        )
      ],
      count: 1,
      total: 1
    };
    const opportunityService = {
      searchOpportunities: jest.fn().mockImplementation((criteria) => {
        if (JSON.stringify(criteria) === JSON.stringify({ page: 1, size: 10 })) {
          return Promise.resolve({
            data: initialOpportunities
          });
        } else {
          return Promise.resolve({
            data: opportunitiesPage
          });
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: participationStatuses
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByTestId("opportunities-list")).toBeInTheDocument();
    });
  });

  it("shows opportunity card v2 for 'Invited' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/dashboard:invited/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /fifa123/i })).toHaveLength(2);
    });
  });

  it("shows opportunity card v2 for 'Open' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/dashboard:open/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /Simple open opp/i })).toHaveLength(2);
    });
  });

  it("navigates to opportunity details page when clicked on a opportunity card", async () => {
    renderPage(<Opportunities {...opportunityProps} />);
    const opportunityButton = (await screen.findAllByRole("button", { name: /fifa123/i }))[0];

    await userEvent.click(opportunityButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/123"));
  });

  it("shows new page of all opportunities when clicking on page number", async () => {
    const opportunityPage1 = [
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc1", title: "fifa1" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc2", title: "fifa2" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc3", title: "fifa3" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc4", title: "fifa4" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc5", title: "fifa5" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc6", title: "fifa6" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc7", title: "fifa7" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc8", title: "fifa8" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc9", title: "fifa9" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc10", title: "fifa10" }))
    ];
    const opportunityPage2 = [
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc11", title: "fifa11" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc12", title: "fifa12" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc13", title: "fifa13" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc14", title: "fifa14" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc15", title: "fifa15" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc16", title: "fifa16" })),
      new OpportunityWithActivationWindow(anOpportunityWithActivationWindow({ id: "abc17", title: "fifa17" }))
    ];
    const opportunitiesResponse1 = {
      count: 10,
      total: 17,
      opportunities: opportunityPage1
    };
    const opportunitiesResponse2 = {
      count: 10,
      total: 17,
      opportunities: opportunityPage2
    };
    const opportunityService = {
      searchOpportunities: jest.fn().mockImplementation((criteria) => {
        if (JSON.stringify(criteria) !== JSON.stringify({ page: 1, size: 10 })) {
          return Promise.resolve({
            data: opportunitiesResponse1
          });
        } else {
          return Promise.resolve({
            data: opportunitiesResponse2
          });
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (
          JSON.stringify(opportunitiesId) ===
          JSON.stringify(["abc1", "abc2", "abc3", "abc4", "abc5", "abc6", "abc7", "abc8", "abc9", "abc10"])
        ) {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: "abc1", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc2", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc3", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc4", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc5", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc6", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc7", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc8", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc9", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc10", status: "INVITED", participationId: null })
            ]
          });
        } else if (
          JSON.stringify(opportunitiesId) ===
          JSON.stringify(["abc11", "abc12", "abc13", "abc14", "abc15", "abc16", "abc17"])
        ) {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: "abc11", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc12", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc13", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc14", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc15", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc16", status: "INVITED", participationId: null }),
              aParticipationStatusWithSubmissionStatus({ id: "abc17", status: "INVITED", participationId: null })
            ]
          });
        } else {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);
    const secondPage = await screen.findByTestId("second-page-number");

    await userEvent.click(secondPage);

    await waitFor(() => {
      expect(opportunityService.searchOpportunities).toHaveBeenCalledTimes(2);
      expect(opportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(1);
      expect(secondPage).toHaveClass("pagination-text-selected");
      expect(screen.getAllByRole("button", { name: "fifa11" })).toHaveLength(2);
    });
  });

  it("shows all perks for an opportunity when user clicks more button", async () => {
    const opportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa123",
          perks: [
            aPerk({ value: "COLLAB", label: "Collab" }),
            aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
            aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
            aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
            aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
            aPerk({ value: "FOOD", label: "Food" }),
            aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
            aPerk({ value: "HOTEL", label: "Hotel" }),
            aPerk({ value: "PAID", label: "Paid" }),
            aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
            aPerk({ value: "SWAG", label: "Swag" }),
            aPerk({ value: "TRAVEL", label: "Travel" })
          ]
        })
      )
    ];
    const opportunitiesResponse = {
      count: 1,
      total: 1,
      opportunities: opportunity
    };
    const opportunityParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: opportunitiesResponse
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: opportunityParticipationStatus
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);

    renderPage(<Opportunities {...opportunityProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });

    await userEvent.click(moreButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      const { getByRole, getAllByRole } = within(modalDialog);
      expect(getByRole("heading", { name: /allPerks/ })).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
      expect(getAllByRole("listitem").length).toBe(opportunity[0].perks.length);
    });
  });

  it("closes the more perks modal when user clicks close button", async () => {
    const opportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa123",
          perks: [
            aPerk({ value: "COLLAB", label: "Collab" }),
            aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
            aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
            aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
            aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
            aPerk({ value: "FOOD", label: "Food" }),
            aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
            aPerk({ value: "HOTEL", label: "Hotel" }),
            aPerk({ value: "PAID", label: "Paid" }),
            aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
            aPerk({ value: "SWAG", label: "Swag" }),
            aPerk({ value: "TRAVEL", label: "Travel" })
          ]
        })
      )
    ];
    const opportunitiesResponse = {
      count: 1,
      total: 1,
      opportunities: opportunity
    };
    const opportunityParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "INVITED", participationId: null })
    ];

    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: opportunitiesResponse
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: opportunityParticipationStatus
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);

    renderPage(<Opportunities {...opportunityProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });
    await userEvent.click(moreButton);
    const closeButton = (await screen.findAllByRole("button", { name: /close$/ }))[0];

    await userEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /allPerks/ })).not.toBeInTheDocument();
      expect(screen.queryByRole("button", { name: /close$/ })).not.toBeInTheDocument();
    });
  });

  it("shows opportunity card v2 for 'Declined' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/opportunities:declined/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /Simple opp declined/i })).toHaveLength(2);
      expect(screen.getByText(/opportunities:designCouncil/i)).toBeInTheDocument();
      expect(screen.getByText("Simple declined description")).toBeInTheDocument();
      expect(screen.queryByText("Registration Window")).not.toBeInTheDocument();
    });
  });

  it("shows all perks for a 'Declined' opportunity when user clicks more button", async () => {
    const opportunity = [
      new OpportunityWithActivationWindow(
        anOpportunityWithActivationWindow({
          id: "123",
          title: "fifa123",
          perks: [
            aPerk({ value: "COLLAB", label: "Collab" }),
            aPerk({ value: "DAILY_ALLOWANCE", label: "Daily Allowance" }),
            aPerk({ value: "DESIGN_COUNCIL", label: "Design Council" }),
            aPerk({ value: "EARLY_ACCESS", label: "Early Access" }),
            aPerk({ value: "EXCLUSIVE_PREVIEW", label: "Exclusive Preview" }),
            aPerk({ value: "FOOD", label: "Food" }),
            aPerk({ value: "FREE_GAME_CODE", label: "Free Game Code" }),
            aPerk({ value: "HOTEL", label: "Hotel" }),
            aPerk({ value: "PAID", label: "Paid" }),
            aPerk({ value: "PRIVATE_DISCORD_CHANNEL", label: "Private Discord" }),
            aPerk({ value: "SWAG", label: "Swag" }),
            aPerk({ value: "TRAVEL", label: "Travel" })
          ]
        })
      )
    ];
    const opportunitiesResponse = {
      count: 1,
      total: 1,
      opportunities: opportunity
    };
    const opportunityParticipationStatus = [
      aParticipationStatusWithSubmissionStatus({ id: "123", status: "DECLINED", participationId: null })
    ];
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: opportunitiesResponse
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: opportunityParticipationStatus
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);

    renderPage(<Opportunities {...opportunityProps} />);
    const moreButton = await screen.findByRole("button", { name: /^opportunities:more/ });

    await userEvent.click(moreButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      const { getByRole, getAllByRole } = within(modalDialog);
      expect(getByRole("heading", { name: /allPerks/ })).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
      expect(getAllByRole("listitem").length).toBe(opportunity[0].perks.length);
    });
  });

  it("shows opportunity card v2 for 'Joined' status", async () => {
    renderPage(<Opportunities {...opportunityProps} />);

    await waitFor(() => {
      expect(screen.getByText(/opportunities:joined/i)).toBeInTheDocument();
      expect(screen.getAllByRole("button", { name: /Simple Joined opp #983/i })).toHaveLength(2);
      expect(screen.getByRole("button", { name: /opportunities:contentSubmission/i })).toBeInTheDocument();
      expect(screen.queryByText("Collab")).not.toBeInTheDocument();
      expect(screen.queryByText("VIP Event")).not.toBeInTheDocument();
    });
  });

  it("navigates to 'Deliverables' tab, for which deliverables is enabled when clicked on a 'Content Submission' button", async () => {
    renderPage(<Opportunities {...opportunityProps} />);
    const contentSubmissionButton = await screen.findByRole("button", { name: /opportunities:contentSubmission/i });

    await userEvent.click(contentSubmissionButton);

    await waitFor(() => expect(router.push).toHaveBeenCalledWith("/opportunities/983?tab=content-deliverables"));
  });

  it("shows creator code details modal for a joined opportunity when user clicks 'Creator Code' button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = "SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] },
        platformDetails: []
      }
    });
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined SAC Opportunity",
                hasDeliverables: false,
                hasGameCodes: false,
                hasEvent: false,
                perks,
                id: supportACreatorOpportunityId,
                creatorCodeActivationWindow,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({
                id: "222",
                participationId: "a0YK0000004yUXdgfdfM",
                status: "JOINED"
              })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      expect(modalDialog).toBeInTheDocument();
      const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
      expect(getByRole("heading", { name: /opportunities:creatorCode.title/i })).toBeInTheDocument();
      expect(getByTestId("creator-code-window-details")).toBeInTheDocument();
      expect(getByText(/Apex Legends/i)).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    });
  });

  it("gets the creator code details for a joined opportunity when user clicks 'Creator Code' button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = " SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: { creatorCodeDetails: null, platformDetails: [] }
    });
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined SAC Opportunity",
                hasDeliverables: false,
                hasGameCodes: false,
                hasEvent: false,
                perks,
                id: supportACreatorOpportunityId,
                creatorCodeActivationWindow,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({
                id: "222",
                participationId: "a0YK0000004yUXdgfdfM",
                status: "JOINED"
              })
            ]
          });
        }
      }),
      getParticipationDetails: jest.fn().mockRejectedValue({
        data: participationDetailsResponse
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });

    await userEvent.click(creatorCodeButton);

    await waitFor(() => {
      expect(opportunityService.getParticipationDetails).toHaveBeenCalledTimes(1);
      expect(opportunityService.getParticipationStatusWithSubmissionInformation).toHaveBeenCalledTimes(1);
      expect(opportunityService.searchOpportunities).toHaveBeenCalledTimes(1);
      expect(opportunityService.getParticipationDetails).toHaveBeenCalledWith(supportACreatorOpportunityId);
    });
  });

  it("hides the creator code details modal for a joined opportunity when user clicks close button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const supportACreatorOpportunityId = " SAC456";
    const perks = [{ name: "Creator Code", code: "CREATOR_CODE" }];
    const participationDetailsResponse = [
      aParticipationDetails({
        creatorCode: aCreatorCode({
          code: "TESTCREATORCODE340",
          activationWindow: creatorCodeActivationWindow,
          status: "JOINED"
        })
      })
    ];
    useAppContext.mockReturnValue({
      dispatch: jest.fn(),
      state: {
        creatorCodeDetails: { [supportACreatorOpportunityId]: participationDetailsResponse[0] },
        platformDetails: []
      }
    });
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined SAC Opportunity",
                hasDeliverables: false,
                hasGameCodes: false,
                hasEvent: false,
                perks,
                id: supportACreatorOpportunityId,
                creatorCodeActivationWindow,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({
                id: "222",
                participationId: "a0YK0000004yUXdgfdfM",
                status: "JOINED"
              })
            ]
          });
        }
      }),
      getParticipationDetails: jest.fn().mockResolvedValue({
        data: participationDetailsResponse
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);
    const creatorCodeButton = await screen.findByRole("button", { name: /opportunities:creatorCodePerk/ });
    await userEvent.click(creatorCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:creatorCode.title/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("creator-code-window-details")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows game code details modal for a joined opportunity when user clicks 'Game Code' button", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined Game code Opportunity",
                hasDeliverables: false,
                hasGameCodes: true,
                hasEvent: false,
                id: gameCodeOpportunityId,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    const operationsService = {
      viewAssignedGameCodes: jest.fn().mockResolvedValue({
        data: [
          {
            participationId,
            platformId,
            gameCode: {
              id: "testgameid",
              code: "GAME-CODE-FREE",
              status: "CLAIMED"
            }
          }
        ]
      })
    };
    OperationsService.mockReturnValue(operationsService);

    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });

    await userEvent.click(gameCodeButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      expect(modalDialog).toBeInTheDocument();
      const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
      expect(getByRole("heading", { name: /opportunities:getGameCode/i })).toBeInTheDocument();
      expect(getByTestId("content-submission-game-code-wrapper")).toBeInTheDocument();
      expect(getByText(/Apex Legends/i)).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    });
  });

  it("hides game code details modal for a joined opportunity when user clicks close button", async () => {
    const creatorCodeActivationWindow = {
      startDate: LocalizedDate.epochMinusDays(10),
      endDate: LocalizedDate.epochPlusDays(5),
      timeZone: "GMT"
    };
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined Game code Opportunity",
                hasDeliverables: false,
                hasGameCodes: true,
                hasEvent: false,
                id: gameCodeOpportunityId,
                creatorCodeActivationWindow,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    const operationsService = {
      viewAssignedGameCodes: jest.fn().mockResolvedValue({
        data: [
          {
            participationId,
            platformId,
            gameCode: {
              id: "testgameid",
              code: "GAME-CODE-FREE",
              status: "ASSIGNED"
            }
          }
        ]
      }),
      claimGameCode: jest.fn().mockImplementation(() => Promise.resolve())
    };
    OperationsService.mockReturnValue(operationsService);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:getGameCode/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("content-submission-game-code-wrapper")).not.toBeInTheDocument();
      expect(screen.queryByText(/Apex Legends/i)).not.toBeInTheDocument();
    });
  });

  it("shows active status in 'Game Code' button for an opportunity with game codes assigned", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined Game code Opportunity",
                hasDeliverables: false,
                hasGameCodes: true,
                hasEvent: false,
                id: gameCodeOpportunityId,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    const operationsService = {
      viewAssignedGameCodes: jest.fn().mockResolvedValue({
        data: [
          {
            participationId,
            platformId,
            gameCode: {
              id: "testgameid",
              code: "GAME-CODE-FREE",
              status: "ASSIGNED"
            }
          }
        ]
      })
    };
    OperationsService.mockReturnValue(operationsService);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    expect(await screen.findByTestId("quick-navigation-active-button-game_code")).toBeInTheDocument();
  });

  it("hides active status in 'Game Code' button for an opportunity if the creator claims the game code", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);

    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined Game code Opportunity",
                hasDeliverables: false,
                hasGameCodes: true,
                hasEvent: false,
                id: gameCodeOpportunityId,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);

    const operationsService = {
      viewAssignedGameCodes: jest.fn().mockResolvedValue({
        data: [
          {
            participationId,
            platformId,
            gameCode: {
              id: "testgameid",
              code: "GAME-CODE-FREE",
              status: "ASSIGNED"
            }
          }
        ]
      }),
      claimGameCode: jest.fn().mockImplementation(() => Promise.resolve())
    };
    OperationsService.mockReturnValue(operationsService);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:gameCode/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(gameCodeButton).not.toHaveProperty("data-testid", "quick-navigation-active-button-game_code");
      expect(screen.getByRole("button", { name: /opportunities:gameCode/ })).toBeInTheDocument();
      expect(operationsService.claimGameCode).toHaveBeenCalledTimes(1);
    });
  });
  it("shows active status in 'Content Submission' button for an opportunity, where community manager has requested changes", async () => {
    const perks = [
      aPerk({ name: "Paid", code: "PAID" }),
      aPerk({ name: "VIP Event", code: "VIP_EVENT" }),
      aPerk({ name: "Collab", code: "COLLAB" }),
      aPerk({ name: "Exclusive Preview", code: "EXCLUSIVE_PREVIEW" })
    ];

    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                id: "222",
                perks,
                title: "Simple Joined opp with content submission",
                hasDeliverables: true,
                hasDeliverables: true,
                hasGameCodes: false
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({
                id: "222",
                participationId: "a0YK0000004yUXdgfdfM",
                status: "JOINED",
                hasChangesRequested: true
              })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);

    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeContentSubmission: { ["222"]: true } }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows active status in 'Content Submission' button for an opportunity, where creator hasn't submitted content & window hasn't passed", async () => {
    const perks = [
      aPerk({ name: "Paid", code: "PAID" }),
      aPerk({ name: "VIP Event", code: "VIP_EVENT" }),
      aPerk({ name: "Collab", code: "COLLAB" }),
      aPerk({ name: "Exclusive Preview", code: "EXCLUSIVE_PREVIEW" })
    ];
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                id: "222",
                perks,
                title: "Simple Joined opp with content submission",
                hasDeliverables: true,
                contentSubmission: aContentSubmission({
                  submissionWindow: {
                    end: LocalizedDate.epochMinusDays(4)
                  }
                }),
                hasDeliverables: true,
                hasGameCodes: false
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({
                id: "222",
                participationId: "a0YK0000004yUXdgfdfM",
                status: "JOINED",
                hasChangesRequested: true
              })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeContentSubmission: { ["222"]: true } }
    });

    renderPage(<Opportunities {...opportunityProps} />);

    expect(await screen.findByTestId("quick-navigation-active-button-content_submission")).toBeInTheDocument();
  });

  it("shows error page when claiming a game code throws an error", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const gameCodeOpportunityId = "GAME_FURIOUS456";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Joined Game code Opportunity",
                hasDeliverables: false,
                hasGameCodes: true,
                hasEvent: false,
                id: gameCodeOpportunityId,
                gameTitle: "Apex Legends"
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: gameCodeOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    const operationsService = {
      viewAssignedGameCodes: jest.fn().mockResolvedValue({
        data: [
          {
            participationId,
            platformId,
            gameCode: {
              id: "testgameid",
              code: "GAME-CODE-FREE",
              status: "ASSIGNED"
            }
          }
        ]
      }),
      claimGameCode: jest.fn().mockRejectedValue(new Error("Failed to claim game code"))
    };
    OperationsService.mockReturnValue(operationsService);
    const dispatch = jest.fn();
    useAppContext.mockReturnValue({
      dispatch,
      state: { platformDetails: [], activeGameCode: { [gameCodeOpportunityId]: true } }
    });
    renderPage(<Opportunities {...opportunityProps} />);
    const activeGameCodeButton = await screen.findByTestId("quick-navigation-active-button-game_code");

    await userEvent.click(activeGameCodeButton);

    await waitFor(() => {
      expect(operationsService.claimGameCode).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledTimes(1);
      expect(errorHandler).toHaveBeenCalledWith(dispatch, expect.any(Error));
    });
  });

  it("shows event details modal for a joined opportunity when user clicks 'Event' button", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const eventOpportunityId = "Event_Opportunity";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Event Opportunity",
                hasDeliverables: false,
                hasGameCodes: false,
                hasEvent: true,
                id: eventOpportunityId,
                event: anOpportunityEvent({
                  eventPeriod: {
                    startDate: LocalizedDate.epochMinusDays(10),
                    endDate: LocalizedDate.epochPlusDays(10),
                    timeZone: "GMT"
                  },
                  meetingPassword: "Password",
                  type: "Remote Event"
                })
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: eventOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);
    const eventButton = await screen.findByRole("button", { name: /opportunities:event/ });

    await userEvent.click(eventButton);

    await waitFor(() => {
      const modalDialog = screen.getByRole("dialog");
      expect(modalDialog).toBeInTheDocument();
      const { getByRole, getAllByRole, getByTestId, getByText } = within(modalDialog);
      expect(getByRole("heading", { name: /opportunities:remote/i })).toBeInTheDocument();
      expect(getByTestId("event-detail-modal-window-details")).toBeInTheDocument();
      expect(getByRole("heading", { name: /opportunities:eventDetails:password/i })).toBeInTheDocument();
      expect(getByText("Password")).toBeInTheDocument();
      expect(getAllByRole("button", { name: /close$/ })).toHaveLength(2);
    });
  });

  it("hides event details modal for a joined opportunity when user clicks 'Event' button", async () => {
    const participationId = "participation123";
    const platformId = "platform123";
    const eventOpportunityId = "Event_Opportunity";
    metadataService.getPlatformsMatching = jest
      .fn()
      .mockResolvedValue([aPlatform({ label: "XBOX", value: platformId }), aPlatform({ label: "PC" })]);
    const opportunityService = {
      searchOpportunities: jest.fn().mockResolvedValue({
        data: {
          opportunities: [
            new OpportunityWithActivationWindow(
              anOpportunityWithActivationWindow({
                title: "Test Event Opportunity",
                hasDeliverables: false,
                hasGameCodes: false,
                hasEvent: true,
                id: eventOpportunityId,
                event: anOpportunityEvent({
                  eventPeriod: {
                    startDate: LocalizedDate.epochMinusDays(10),
                    endDate: LocalizedDate.epochPlusDays(10),
                    timeZone: "GMT"
                  },
                  meetingPassword: "Password",
                  type: "Remote Event"
                })
              })
            )
          ],
          total: 1
        }
      }),
      getParticipationStatusWithSubmissionInformation: jest.fn().mockImplementation((opportunitiesId) => {
        if (JSON.stringify(opportunitiesId) === JSON.stringify(["123", "983", "939", "940"])) {
          return Promise.resolve({
            data: initialOpportunitiesParticipationStatus
          });
        } else {
          return Promise.resolve({
            data: [
              aParticipationStatusWithSubmissionStatus({ id: eventOpportunityId, participationId, status: "JOINED" })
            ]
          });
        }
      })
    };
    OpportunityService.mockReturnValue(opportunityService);
    renderPage(<Opportunities {...opportunityProps} />);
    const gameCodeButton = await screen.findByRole("button", { name: /opportunities:event/ });
    await userEvent.click(gameCodeButton);
    const { getAllByRole } = within(await screen.findByRole("dialog"));

    await userEvent.click(getAllByRole("button", { name: /close$/ })[0]);

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
      expect(screen.queryByRole("heading", { name: /opportunities:remote/i })).not.toBeInTheDocument();
      expect(screen.queryByTestId("event-detail-modal-window-details")).not.toBeInTheDocument();
    });
  });
});
