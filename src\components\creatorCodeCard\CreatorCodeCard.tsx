import React, { memo } from "react";
import { useWindowSize } from "@src/utils";

type CreatorCodeCardProps = {
  username: string;
  label?: string;
};

const CreatorDisplayName = memo(function CreatorDisplayName({ username, label }: CreatorCodeCardProps) {
  return (
    <section className="creator-display-name-container">
      <div className={!label ? "creator-display-name-division" : ""}>
        {label && <span className="creator-display-name-welcome">{label}</span>}
        <h3 className="creator-display-name-username">{username}</h3>
      </div>
    </section>
  );
});

const CreatorCodeCard = memo(function CreatorCodeCard({ username, label }: CreatorCodeCardProps) {
  const { width } = useWindowSize();
  const isMobile = width !== undefined && width < 768;
  const logoSrc = isMobile ? "/img/dashboard/sims-logo-horizontal.svg" : "/img/dashboard/sims-logo-vertical.svg";

  return (
    <div className="creator-code-card">
      <div className="creator-code-content">
        <div className="creator-code-text">
          <CreatorDisplayName username={username} label={label} />
        </div>
        <div className="creator-code-logo">
          <img src={logoSrc} alt="" data-testid="creator-code-logo" />
        </div>
        <div className="creator-code-image">
          <img src="/img/dashboard/sims-group-of-characters.png" alt="" />
        </div>
      </div>
    </div>
  );
});

export default CreatorCodeCard;
