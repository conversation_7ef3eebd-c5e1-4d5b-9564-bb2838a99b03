import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import PaymentInformationPagePropsController from "./controllers/PaymentInformationPagePropsController";
import config from "config";

const paymentInformationProps = (locale: string) =>
  serverPropsControllerFactory(
    new PaymentInformationPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default paymentInformationProps;
