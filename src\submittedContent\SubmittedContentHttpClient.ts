import OpportunitySubmittedContent from "./OpportunitySubmittedContent";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type WebsiteContent = {
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  contentType: string;
};

export type Contents = {
  id: string;
  name: string;
  type: string;
  thumbnail: string;
  status: string;
  submittedOn: number;
  contentUri: string;
  contentType: string;
  submittedDate: number;
  opportunityName: string;
  opportunityId: string;
  sourceType: string;
  deliverableId: string;
  reviewFinalRemark: {
    content: string;
    author: string;
    date: number;
  };
};

export type UpdateWebsiteContent = {
  id: string;
  contentDetails: WebsiteContent;
};

export type SocialMediaContent = {
  creatorId: string;
  contentUrl: string;
};

export type SocialMediaTikTokContent = SocialMediaContent & {
  accountId: string;
};

export type SocialMediaContentDeliverable = SocialMediaTikTokContent & {
  deliverableId: string;
};

export type UploadedFileContent = {
  fileName: string;
  title: string;
  versionId: string;
  thumbnail: string;
  id: string;
  contentType: string;
};

export type WebsiteContentDeliverable = WebsiteContent & {
  deliverableId: string;
};

export type UploadedFileContentDeliverable = UploadedFileContent & {
  deliverableId: string;
};

export type SubmittedContentCriteria = {
  creatorId: string;
  participationId: string;
  program: string;
  page: number;
  size: number;
  deliverableId: string;
};

export type SubmittedContentWithProgramCode = {
  count: number;
  total: number;
  contents: Contents[];
};

@Service()
class SubmittedContentHttpClient {
  constructor(@Inject("contentSubmissionClient") private readonly client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/submitUploadedContent Submit website content}
   */
  async saveWebsiteContent(
    participationId: string,
    websiteContent: WebsiteContent
  ): Promise<Array<OpportunitySubmittedContent>> {
    const response = await this.client.post(`/v1/participations/${participationId}/website-contents`, {
      body: websiteContent
    });
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/updateWebsiteContent Update website content}
   */
  async updateWebsiteContent(
    participationId: string,
    websiteContent: UpdateWebsiteContent
  ): Promise<OpportunitySubmittedContent> {
    const response = await this.client.put(`/v1/participations/${participationId}/website-contents`, {
      body: websiteContent
    });
    return Promise.resolve(response.data);
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/saveSocialMediaContentsWithInstagramReelsSupport}
   */
  async saveSocialMediaContents(
    participationId: string,
    socialMediaContentDeliverable: SocialMediaContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v4/participations/${participationId}/social-media-contents`, {
      body: socialMediaContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Submitted-Content/operation/saveSocialMediaContentsWithInstagramMultipleMediaSupport}
   */
  async saveSocialMediaContentWithInstagramMedia(
    participationId: string,
    socialMediaContentDeliverable: SocialMediaContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v5/participations/${participationId}/social-media-contents`, {
      body: socialMediaContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/submitWebsiteContentWithDeliverableId Submit website content deliverable}
   */
  async saveWebsiteContentDeliverable(
    participationId: string,
    websiteContentDeliverable: WebsiteContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v2/participations/${participationId}/website-contents`, {
      body: websiteContentDeliverable
    });
  }

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/submitUploadedContentWithDeliverableId Submit uploaded content}
   */
  async submitUploadedContentDeliverable(
    participationId: string,
    uploadedFileContentDeliverable: UploadedFileContentDeliverable
  ): Promise<void> {
    await this.client.post(`/v2/participations/${participationId}/file-contents`, {
      body: uploadedFileContentDeliverable
    });
  }

  /**
   * @see {@link https://dev-services.cn.ea.com/cn-content-submission-api/swagger-ui/index.html?configUrl=/cn-content-submission-api/v3/api-docs/swagger-config#/view-submitted-content-controller/viewSubmittedContentWithProgramCode}
   **/
  async getSubmittedContentsWithProgramCode(
    criteria: SubmittedContentCriteria
  ): Promise<SubmittedContentWithProgramCode> {
    const submittedContent = await this.client.get(`/v6/submitted-content`, { query: criteria });
    return Promise.resolve(submittedContent.data);
  }
}

export default SubmittedContentHttpClient;
