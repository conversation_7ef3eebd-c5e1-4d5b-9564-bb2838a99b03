.content-submission-form-elements label:nth-child(2) .input-box-label,
.content-submission-form-elements .select-box {
  @apply pt-meas12;
}
.content-submission-form-elements .select-header-title,
.content-submission-form-elements .select-list {
  @apply w-full;
}
.content-submission-form-elements .form-textarea-label,
.content-submission-form-elements .select-label {
  @apply text-[#262626];
}
.content-submission-form-elements .select-error-message {
  @apply text-error-50;
}
.content-submission-footer {
  @apply float-right pt-meas12;
}
.content-submission-file-upload {
  @apply border-[1px] border-gray-20 bg-gray-10 p-meas10;
}
.content-submission-file-upload-content {
  @apply block md:grid;
  grid-template-columns: 1fr 3fr;
  grid-gap: 20px;
}
.content-submission-choose-file {
  @apply mx-auto mb-meas10 w-[142px] border-[1px] border-gray-90 px-meas8 py-[10px] text-center font-bold text-gray-90 xs:text-mobile-body-large md:my-[10px] md:text-tablet-body-large lg:text-desktop-body-large;
  border-radius: 2px;
}
.content-submission-no-choosen {
  @apply font-bold;
}
.content-submission-no-choosen,
.content-submission-file-formats {
  @apply text-navy-80 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.content-submission-file-formats {
  @apply pt-[2px];
}
.content-submission-file-upload.inactive .content-submission-choose-file {
  @apply border-gray-40 text-gray-40;
}
.content-submission-file-upload.inactive .content-submission-no-choosen,
.content-submission-file-upload.inactive .content-submission-file-formats {
  @apply border-gray-40 text-gray-40;
}
.content-submission-file-selection {
  @apply border-[1px] border-gray-20 bg-gray-10 p-meas10;
}
.content-submission-file-selection-content {
  @apply grid;
  grid-template-columns: 1fr 0fr;
  grid-gap: 20px;
}
.content-submission-upload-delete {
  @apply flex w-meas12 items-center;
}
.content-submission-delete-image {
  @apply cursor-pointer;
}
.content-submission-file-name {
  @apply break-all font-bold text-navy-80 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.content-submission-file {
  @apply grid pt-meas4;
  grid-template-columns: 1fr 28fr;
  grid-gap: 4px;
}
.content-submission-upload-label {
  @apply mt-meas12 pb-meas2 font-bold text-gray-90 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.content-submission-file-selected {
  @apply text-gray-90 xs:text-mobile-caption2 md:text-tablet-caption2 lg:text-desktop-caption2;
}
.content-submission-file-image {
  @apply h-meas7 w-meas6;
}
.content-submission-file-progress {
  @apply mt-meas4 w-full;
}
.content-submission-file-progress::-webkit-progress-value {
  @apply bg-teal-60;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}
.content-submission-file-progress::-webkit-progress-bar {
  @apply bg-white;
  border-radius: 50px;
  height: 11px;
}
.content-submission-file-progress-complete::-webkit-progress-value {
  border-radius: 20px;
}
.content-submission-file-progress::-moz-progress-value {
  @apply bg-teal-60;
  border-radius: 20px;
}
.content-submission-file-progress::-moz-progress-bar {
  @apply bg-white;
  border-radius: 50px;
  height: 11px;
}
.content-submission-file-upload.active .content-submission-choose-file:hover {
  @apply cursor-pointer bg-gray-20;
}
.content-submission-delete-image-disabled {
  @apply cursor-default opacity-50;
}
.content-submission-form-elements .form-textarea-field {
  @apply min-h-[124px];
}
