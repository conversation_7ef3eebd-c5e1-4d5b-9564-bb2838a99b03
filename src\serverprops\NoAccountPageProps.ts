import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import NoAccountPagePropsController from "./controllers/NoAccountPagePropsController";

const noAccountPageProps = (locale: string) =>
  serverPropsControllerFactory(
    new NoAccountPagePropsController(ApiContainer.get("options"), ApiContainer.get(ContentManagementService), locale)
  );

export default noAccountPageProps;
