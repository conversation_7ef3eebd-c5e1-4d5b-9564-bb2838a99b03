import { act, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { aCreatorWithPayableStatus } from "../../../factories/creators/CreatorWithPayableStatus";
import PaymentInformationPage from "@components/pages/payment-information/PaymentInformationPage";
import { renderPage } from "../../../helpers/page";
import { mockMatchMedia } from "../../../helpers/window";
import { useRouter } from "next/router";
import {
  buttonLabel,
  paymentBannerTranslations,
  paymentDetailsTranslations,
  paymentFilterTranslations,
  paymentHistoryGridTranslations,
  paymentInformationTranslations
} from "../../../translations/payment-information";
import { axe } from "jest-axe";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import "next/config";
import PaymentInformationService from "@src/services/paymentInformation/PaymentInformationService";
import CreatorsService from "@src/services/CreatorsService";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../../../src/context/DependencyContext";
import { aPaymentHistoryDetail } from "__tests__/factories/paymentsHistory/aPaymentHistoryDetail";
import { DollarAmount } from "@src/services/paymentInformation/DollarAmount";
import Random from "__tests__/factories/Random";

jest.mock("next/config", () => () => ({ publicRuntimeConfig: { SUPPORTED_LOCALES: '["en-us"]' } }));
jest.mock("../../../../src/services/paymentInformation/PaymentInformationService");
jest.mock("../../../../src/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../../../src/services/CreatorsService"),
    getCreatorWithPrograms: jest.fn()
  };
});
jest.mock("../../../../src/context/DependencyContext");
jest.mock("../../../../analytics/browser/src/ampli", () => {
  return {
    ampli: {
      identify: jest.fn(),
      clickedFooterLink: jest.fn(),
      receivedErrorMessage: jest.fn()
    }
  };
});

xdescribe("PaymentInformationPage", () => {
  mockMatchMedia();
  const paymentInformationProps = {
    PAYMENTS_DEFAULT_START_DATE: "2021-09-01",
    unhandledError: "Oops! Something went wrong",
    labels: {
      paymentInfoLabels: paymentInformationTranslations,
      paymentBannerLabels: paymentBannerTranslations,
      paymentDetailsLabels: paymentDetailsTranslations,
      paymentHistoryGridLabels: paymentHistoryGridTranslations,
      paymentFilterLabels: paymentFilterTranslations,
      buttonLabel: buttonLabel
    },
    analytics: ({} as unknown) as BrowserAnalytics
  };
  const errorHandler = jest.fn();
  const paymentWithNoData = aPaymentHistoryDetail({
    count: 0,
    total: 0,
    totalPaidAmount: new DollarAmount("0.00"),
    totalPendingAmount: new DollarAmount("0.00"),
    paymentsHistory: []
  });

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      errorHandler,
      configuration: { PROGRAM_CODE: "sims_creator_program" }
    });
  });

  it("selects Payment Settings tab based on router query", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({
      query: { tab: "payment-settings" },
      locale: "en-us"
    }));
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} />);

    expect(await screen.findByRole("button", { name: /payment settings/i })).toHaveClass("active cn-tab");
    expect(await screen.findByRole("button", { name: /your payments/i })).toHaveClass("cn-tab");
  });

  it("shows unpayable badge", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} />);

    expect(await screen.findByText(/payment details incomplete/i)).toBeInTheDocument();
  });

  it("shows payable badge", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const payableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: true, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} />);

    expect(await screen.findByText(/payable/i)).toBeInTheDocument();
  });

  it("displays tabs 'Payment Settings' as active and 'Your Payments' as inactive", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const payableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: true, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} />);

    await screen.findByRole("button", { name: /payment settings/i });
    expect(await screen.findByRole("button", { name: /your payments/i })).toHaveClass("active cn-tab");
  });

  it("makes 'Your Payments' tab active on click of payable badge button", async () => {
    const analytics = ({ clickedPaymentSettingsTab: jest.fn() } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const payableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: true, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      }),
      getPaymentsIFrameUrl: jest.fn().mockResolvedValue({
        data: {
          embeddableUrl: "about:blank",
          id: Random.uuid()
        }
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
    });
    const paymentsSettingsTabButton = await screen.findByRole("button", { name: /payment settings/i });
    expect(paymentsSettingsTabButton).toHaveClass("inactive");
    await userEvent.click(paymentsSettingsTabButton);
    await waitFor(() => {
      expect(paymentInformationService.getPaymentsIFrameUrl).toHaveBeenCalledTimes(1);
      expect(paymentsSettingsTabButton).toHaveClass("active");
    });
    await userEvent.click(screen.getByText(/payable/i));
    expect(await screen.findByText(/your payments/i)).toHaveClass("active cn-tab");
    expect(await screen.findByText(/No payments/i)).toBeInTheDocument();
  });

  it("makes 'Payment Settings' tab active on click of 'click here' in tooltip", async () => {
    const analytics = ({
      clickedPaymentSettingsTab: jest.fn(),
      clickedPaymentDetailsIncompleteTooltip: jest.fn()
    } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await userEvent.hover(await screen.findByText(/Payment Details Incomplete/i));
    await userEvent.click(screen.getByRole("button", { name: "Click here" }));
    await waitFor(() => expect(screen.getByRole("button", { name: /payment settings/i })).toHaveClass("active"));
  });

  it("logs 'Clicked Payment Details Incomplete Tooltip' event when clicking on 'click here' in unpayable tooltip", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const analytics = ({
      clickedPaymentSettingsTab: jest.fn(),
      clickedPaymentDetailsIncompleteTooltip: jest.fn()
    } as unknown) as BrowserAnalytics;
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await userEvent.hover(await screen.findByText(/Payment Details Incomplete/i));
    await userEvent.click(screen.getByRole("button", { name: "Click here" }));
    await waitFor(() => {
      expect(analytics.clickedPaymentDetailsIncompleteTooltip).toHaveBeenCalledTimes(1);
      expect(analytics.clickedPaymentDetailsIncompleteTooltip).toHaveBeenCalledWith({ locale: "en-us" });
    });
  });

  it("sets 'Payment Settings' tab active on click of 'click here' link in payment information banner", async () => {
    const analytics = ({
      clickedPaymentSettingsTab: jest.fn(),
      clickedPaymentDetailsIncompleteHelperBanner: jest.fn()
    } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const unPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: unPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
      expect(screen.queryByText(/no payments/i)).toBeInTheDocument();
    });
    const paymentsSettingsTabButton = await screen.findByRole("button", { name: /payment settings/i });
    expect(paymentsSettingsTabButton).toHaveClass("inactive");
    await userEvent.click(screen.getByRole("link", { name: /click here/i }));
    await waitFor(() => expect(paymentsSettingsTabButton).toHaveClass("active"));
  });

  it("logs 'Clicked Payment Details Incomplete Helper Banner' event on click of 'click here' link in payment information banner", async () => {
    const analytics = ({
      clickedPaymentSettingsTab: jest.fn(),
      clickedPaymentDetailsIncompleteHelperBanner: jest.fn()
    } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const unPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: unPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
      expect(screen.queryByText(/no payments/i)).toBeInTheDocument();
    });
    const paymentsSettingsTabButton = await screen.findByRole("button", { name: /payment settings/i });
    expect(paymentsSettingsTabButton).toHaveClass("inactive");
    await userEvent.click(screen.getByRole("link", { name: /click here/i }));
    await waitFor(() => {
      expect(analytics.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledTimes(1);
      expect(analytics.clickedPaymentDetailsIncompleteHelperBanner).toHaveBeenCalledWith({ locale: "en-us" });
    });
  });

  it("logs 'Clicked Payment Settings Tab' event on click of 'Payment Settings' tab", async () => {
    const analytics = ({ clickedPaymentSettingsTab: jest.fn() } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const unPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: unPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
      expect(screen.queryByText(/no payments/i)).toBeInTheDocument();
    });
    const paymentsSettingsTabButton = await screen.findByRole("button", { name: /payment settings/i });
    expect(paymentsSettingsTabButton).toHaveClass("inactive");
    await userEvent.click(paymentsSettingsTabButton);
    await waitFor(() => {
      expect(analytics.clickedPaymentSettingsTab).toHaveBeenCalledTimes(1);
      expect(analytics.clickedPaymentSettingsTab).toHaveBeenCalledWith({ locale: "en-us" });
    });
  });

  it("is accessible", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const payableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: true, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    const { container } = renderPage(<PaymentInformationPage {...paymentInformationProps} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
    });
    let results;
    await act(async () => {
      results = await axe(container);
    });
    expect(results).toHaveNoViolations();
  });

  it("updates 'Your Payments' tab with selected filters on filter selection", async () => {
    const analytics = ({
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn(),
      appliedAllPaymentFilters: jest.fn()
    } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const payableCreator = aCreatorWithPayableStatus({
      accountInformation: { payable: true, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
      expect(screen.queryByText(/no payments/i)).toBeInTheDocument();
    });
    const filterButton = screen.getByRole("button", { name: /Filter/i });
    await userEvent.click(filterButton);
    expect(await screen.findByText(/^Date Range$/i)).toBeInTheDocument();
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangeThisMonthElement = await screen.findByText(/This Month/i);
    await userEvent.click(dateRangeThisMonthElement);
    await userEvent.click(screen.getByRole("button", { name: /Apply/i }));
    expect(await screen.findAllByText(/^This Month$/i)).toHaveLength(1);
  });

  it("retains the filters selected on switching tabs", async () => {
    const analytics = ({
      clickedPaymentSettingsTab: jest.fn(),
      openedPaymentsFiltersForm: jest.fn(),
      appliedDateRangeFilter: jest.fn(),
      appliedAllPaymentFilters: jest.fn()
    } as unknown) as BrowserAnalytics;
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    const payableCreator = aCreatorWithPayableStatus({
      accountInformation: {
        payable: true,
        dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1))
      }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: payableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} analytics={analytics} />);

    await waitFor(() => {
      expect(CreatorsService.getCreatorWithPrograms).toHaveBeenCalledTimes(1);
      expect(paymentInformationService.getPaymentsHistoryWithProgramCode).toHaveBeenCalledTimes(1);
      expect(screen.queryByText(/no payments/i)).toBeInTheDocument();
    });
    const filterButton = screen.getByRole("button", { name: /Filter/i });
    await userEvent.click(filterButton);
    expect(await screen.findByText(/^Date Range$/i)).toBeInTheDocument();
    await userEvent.click(await screen.findByText(/^All Time$/));
    const dateRangeThisMonthElement = await screen.findByText(/This Month/i);
    await userEvent.click(dateRangeThisMonthElement);
    await userEvent.click(await screen.findByRole("button", { name: /Apply/i }));
    await waitFor(async () => expect(await screen.findAllByText(/^This Month$/i)).toHaveLength(1), { timeout: 1_250 });
    const paymentsSettingsTabButton = await screen.findByRole("button", { name: /payment settings/i });
    expect(paymentsSettingsTabButton).toHaveClass("inactive");
    await userEvent.click(paymentsSettingsTabButton);
    await waitFor(() => expect(paymentsSettingsTabButton).toHaveClass("active"));
    await userEvent.click(await screen.findByRole("button", { name: /your payments/i }));
    await waitFor(() => {
      expect(screen.getByText(/your payments/i)).toHaveClass("active cn-tab");
      expect(screen.queryByText(/No payments/i)).toBeInTheDocument();
      expect(screen.queryByText(/^This Month$/i)).toBeInTheDocument();
    });
  });

  it("hides loader when API fails on Payment Settings tab", async () => {
    (useRouter as jest.Mock).mockImplementation(() => ({
      query: { tab: "payment-settings" },
      locale: "en-us"
    }));
    const nonPayableCreator = aCreatorWithPayableStatus({
      accountInformation: { isPayable: false, dateOfBirth: new LocalizedDate(LocalizedDate.epochMinusDays(1)) }
    });
    (CreatorsService.getCreatorWithPrograms as jest.Mock).mockResolvedValue({
      data: nonPayableCreator
    });
    const paymentInformationService = ({
      getPaymentsHistoryWithProgramCode: jest.fn().mockResolvedValue({
        status: 200,
        data: paymentWithNoData
      }),
      getPaymentsIFrameUrl: jest.fn().mockResolvedValue({
        data: {
          title: "Unprocessable Entity",
          status: 409,
          code: "generate-payee-url-invalid-input"
        }
      })
    } as unknown) as PaymentInformationService;
    (PaymentInformationService as jest.Mock).mockReturnValue(paymentInformationService);

    renderPage(<PaymentInformationPage {...paymentInformationProps} />);

    await waitFor(() => {
      expect(screen.queryByAltText("Loading...")).not.toBeInTheDocument();
    });
  });
});
