import { useRouter } from "next/router";
import { useEffect } from "react";
import { setCookie } from "../utils";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { useDependency } from "@src/context/DependencyContext";

// This page is used for inviting members from the SF site.
// Ex: /en-US/sign-up?code=3286-70d3-fc88-b2b4&opportunityId=a0M7c00000GbYEDEA3
export default function SignUp() {
  const {
    configuration: { SUPPORTED_LOCALES }
  } = useDependency();
  const router = useRouter();

  useEffect(() => {
    if (!router.isReady) return;

    if (!router.query.code && !router.query.isExistingCreator) {
      router.push(`/404`);
      return;
    }

    SUPPORTED_LOCALES.includes(router.locale) && setCookie(router.locale);

    let url = "/api/registrations?";

    if (router.query.code) {
      url += `code=${router.query.code}`;
    }

    router.push(url, url, { locale: router.locale });
  }, [router.isReady]);
  return <></>;
}

export const getStaticProps = async () => ({
  props: {
    runtimeConfiguration: runtimeConfiguration()
  }
});
