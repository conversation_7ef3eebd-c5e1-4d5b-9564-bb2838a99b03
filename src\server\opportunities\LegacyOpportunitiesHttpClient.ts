import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import OpportunityWithDeliverables from "./OpportunityWithDeliverables";

@Service()
class LegacyOpportunitiesHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Opportunities/operation/viewOpportunityWithDeliverables}
   */
  async withOpportunityDeliverables(id: string, creatorId: string): Promise<OpportunityWithDeliverables> {
    const response = await this.client.get(`/v8/opportunities/${id}/creator/${creatorId}`);
    return OpportunityWithDeliverables.fromApi(response.data);
  }
}
export default LegacyOpportunitiesHttpClient;
