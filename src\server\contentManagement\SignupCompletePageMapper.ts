import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type SignupCompletePageLabels = {
  signupCompleteLabels: {
    title: string;
    subtitle: string;
    body: string;
    getStarted: string;
  };
};

export class SignupCompletePageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): SignupCompletePageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      signupCompleteLabels: {
        title: microCopy.get("signupComplete.title"),
        subtitle: microCopy.get("signupComplete.subtitle"),
        body: microCopy.get("signupComplete.body"),
        getStarted: microCopy.get("signupComplete.getStarted")
      }
    };
  }
}
