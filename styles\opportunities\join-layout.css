@import "criteria.css";
@import "./thanks-opportunity.css";
@import "game-code.css";
@import "./content-guidelines.css";

.join-opportunity-container {
  @apply absolute z-0 flex w-full flex-col overflow-y-hidden bg-secondary sm:px-meas8 md:py-meas8;
}
.join-opportunity-bg {
  @apply absolute left-[0rem] top-[-19rem] block h-[1400px] w-screen max-w-[1920px] bg-migration-shape bg-cover bg-center bg-no-repeat;
  z-index: -1;
}
.join-opportunity-page {
  @apply flex h-full min-h-screen w-full flex-col items-center text-gray-10;
}
.join-opportunity-title-container {
  @apply flex justify-center py-meas8 md:p-meas24;
}
.join-opportunity-title {
  @apply absolute font-display-regular font-bold text-white xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3;
}
.jn-opp-subTitle-container {
  @apply mt-meas33 flex items-center px-meas8;
}
.join-opportunity-image {
  @apply h-meas26 w-[85px];
}
.join-opportunity-subTitle {
  @apply ml-meas12 font-display-regular font-bold text-gray-10 xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
  word-break: break-word;
}
.join-opportunity-header-container {
  @apply mt-meas4 flex w-full flex-col pt-meas2;
}
.join-opportunity-header {
  @apply flex w-full flex-row items-center justify-between px-meas8 pb-meas20 text-gray-10;
}
.join-opportunity-header-back {
  @apply block flex-1 justify-start xl:hidden;
}

.exit-join-flow {
  @apply cursor-pointer;
}
/* join opportunity breadcrumb */

.join-opportunity-breadcrumb {
  @apply flex w-full flex-row items-center justify-center overflow-hidden fill-gray-10 text-white;
}
.join-opportunity-breadcrumb-item {
  @apply flex flex-row items-center justify-center;
}
.join-opportunity-breadcrumb-icon {
  @apply flex flex-row items-baseline justify-center pb-[8.75px];
}
.join-opportunity-breadcrumb-icon .icon {
  @apply h-[23px] w-[23px];
}
.join-opportunity-breadcrumb-item-label {
  @apply h-meas28 w-meas33 text-center font-text-regular xs:text-mobile-caption2 md:w-meas35 md:text-tablet-body-default lg:text-desktop-body-default xl:w-[7.5rem];
}
.join-opportunity-breadcrumb-item-border {
  @apply mb-meas24 w-meas18 border-b-[1px] border-dashed md:w-meas30 xl:mx-meas4 xl:w-meas30;
  border-color: gray;
}
.join-opportunity-last-item {
  @apply hidden;
}
.join-opportunity-breadcrumb .inactive {
  @apply text-gray-50;
}
.join-opportunity-breadcrumb .active {
  @apply text-gray-10;
}
.join-opportunity-breadcrumb .current {
  @apply text-white;
}
.join-opportunity-breadcrumb-nav {
  @apply text-gray-10 md:mb-meas34 xl:mt-meas8;
}

.join-opportunity-breadcrumb-back {
  @apply md:block xl:ml-[216px] xl:block;
}

.join-opportunity-breadcrumb-back-with-icon {
  @apply flex text-white;
}

.join-opportunity-breadcrumb-back > button {
  @apply flex items-center justify-start;
}

.join-opportunity-breadcrumb-back > button > span:last-child {
  @apply ml-meas4 cursor-pointer font-display-regular text-gray-10;
}

#join-opportunity-breadcrumb-content {
  @apply h-[90px] w-[260px] justify-center md:w-[548px] xl:w-[548px];
}
/* Join Opportunity footer */
.join-opportunity-footer-container {
  @apply mt-meas16 flex w-full  flex-col border-t border-white border-opacity-[0.33] py-meas16;
}
.join-opportunity-footer {
  @apply flex flex-row justify-between;
}
.criteria-opportunity > form .join-opportunity-footer {
  @apply flex flex-row self-end;
}
.join-opportunity-footer > .btn-sizes-md {
  @apply my-meas0 h-meas24 px-meas6;
}
.join-opportunity-footer > .btn-tertiary > span:last-child {
  @apply ml-meas4;
}
.join-opportunity-footer > .btn-primary > span:first-child {
  @apply mr-meas4;
}
.join-opportunity-footer button {
  @apply flex;
}
.join-opportunity-submit-next .icon {
  @apply order-2 ml-meas4 mr-meas4;
}
.join-opportunity-submit-next {
  @apply order-2 ml-meas4 mr-meas4 mt-meas2;
}
.join-opportunity-back {
  @apply mt-meas2;
}
.join-opportunity-submit-next .icon-label {
  @apply order-1 font-display-bold font-bold xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}

.opportunity-back-bt {
  @apply hidden pl-meas0 md:hidden xl:flex;
  border: none;
}

.join-opportunity-container .stepper-item-border {
  @apply mb-meas12;
}

.join-opportunity-stepper-container {
  @apply flex w-full flex-wrap items-center justify-between gap-meas4 px-meas8 py-meas4;
}

.join-opportunity-stepper-header {
  @apply flex w-full flex-row items-center justify-between px-meas8;
}
