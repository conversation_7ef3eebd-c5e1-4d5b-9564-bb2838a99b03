import React, { memo, ReactNode } from "react";
import Head from "next/head";
import ProgramSideNavigation from "./ProgramSideNavigation";
import Favicon from "./icons/Favicon/Favicon";

export type FooterLabels = {
  how: string;
  reward: string;
  perks: string;
  faq: string;
  policies: string;
  legal: string;
  disclaimer: string;
  updates: string;
  terms: string;
  privacy: string;
  rights: string;
  report: string;
  copyright: string;
  faqs: string;
  disclosure: string;
  policy: string;
};

export type HeaderLabels = {
  creatorNetwork: string;
  home: string;
  how: string;
  works: string;
  perks: string;
  rewards: string;
  signIn: string;
  logIn: string;
  apply: string;
  applyNow: string;
  requestToJoin: string;
  dashboard: string;
  opportunities: string;
  myContent: string;
  about: string;
  notifications: string;
  myProfile: string;
  signout: string;
  faq: string;
  faqs: string;
  disclosure: string;
  policy: string;
  calendar: string;
  documentation: string;
};

export type LayoutHeaderProps = {
  pageTitle: string;
  children?: ReactNode;
  tabTitle?: string;
};

export const LayoutHeader = memo(function LayoutHeader({ pageTitle, children, tabTitle }: LayoutHeaderProps) {
  return (
    <>
      <Head>
        <title suppressHydrationWarning>{tabTitle || pageTitle || "The Sims"}</title>
      </Head>
      <Favicon basePath={process.env.SIMS_BASE_URL || ""} />
      {children}
    </>
  );
});

export const LayoutFooter = memo(function LayoutFooter({ children }: { children: ReactNode }) {
  return <div className="layout-footer-container">{children}</div>;
});

export const LayoutBody = memo(function LayoutBody({
  children,
  showSideNavigation,
  className = ""
}: {
  children: ReactNode;
  className?: string;
  showSideNavigation?: boolean;
}) {
  return showSideNavigation ? (
    <div className="layout-body" data-testid="layout-body">
      <ProgramSideNavigation />
      <div className="content-area">
        <div className={className}>{children}</div>
      </div>
    </div>
  ) : (
    <div className={className}>{children}</div>
  );
});

export default memo(function Layout({ children }: { children: ReactNode }) {
  return <div>{children}</div>;
});
