import FrontendTracer from "@src/telemetry/FrontendTracer";
import { WebTracerProvider } from "@opentelemetry/sdk-trace-web";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-web";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { registerInstrumentations } from "@opentelemetry/instrumentation";
import { NoOpSpanExporter } from "@eait-playerexp-cn/telemetry";

jest.mock("@opentelemetry/sdk-trace-web");
jest.mock("@opentelemetry/exporter-trace-otlp-http");
jest.mock("@opentelemetry/instrumentation");

describe("FrontendTracer", () => {
  const tracerOptions = { serviceName: "cn_sims_ugx", enabled: true, version: "1.0.0", environment: "local" };

  beforeEach(() => jest.clearAllMocks());

  it("should initialize the tracer provider with correct settings", async () => {
    await FrontendTracer(tracerOptions);

    expect(WebTracerProvider).toHaveBeenCalledWith({
      resource: {
        _attributes: {
          "deployment.environment": tracerOptions.environment,
          "service.name": tracerOptions.serviceName,
          "service.version": tracerOptions.version,
          "telemetry.sdk.language": expect.any(String),
          "telemetry.sdk.name": expect.any(String),
          "telemetry.sdk.version": expect.any(String)
        },
        asyncAttributesPending: false,
        _syncAttributes: {
          "deployment.environment": tracerOptions.environment,
          "service.name": tracerOptions.serviceName,
          "service.version": tracerOptions.version,
          "telemetry.sdk.language": expect.any(String),
          "telemetry.sdk.name": expect.any(String),
          "telemetry.sdk.version": expect.any(String)
        },
        _asyncAttributesPromise: undefined
      }
    });
    // Check if the context manager and propagators were set up
    expect(registerInstrumentations).toHaveBeenCalledWith({
      tracerProvider: expect.objectContaining({
        addSpanProcessor: expect.any(Function),
        register: expect.any(Function)
      })
    });
    expect(BatchSpanProcessor).toHaveBeenCalledWith(expect.any(OTLPTraceExporter));
  });

  it("should call `NoOpSpanExporter` when tracing is disabled ", async () => {
    await FrontendTracer({ ...tracerOptions, enabled: false });

    expect(BatchSpanProcessor).toHaveBeenCalledWith(expect.any(NoOpSpanExporter));
  });
});
