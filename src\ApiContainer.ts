import {
  AuthorizationError<PERSON><PERSON><PERSON>,
  TermsAndConditionsStatusesHttpClient,
  VerifyAccessToProgramServerPropsMiddleware
} from "@eait-playerexp-cn/identity";
import {
  ContinueOnboardingPlugin,
  RegistrationCodesHttpClient,
  SaveRegistrationCodeToSessionController,
  StartOnboardingPlugin,
  VerifyIncompleteRegistrationPropsMiddleware
} from "@eait-playerexp-cn/onboarding-authentication-plugins";
import { Container } from "typedi";
import config from "../config";
import { AttributesConverter, AttributesSerializer, CompositeCollector } from "@eait-playerexp-cn/telemetry";
import { CompositeSerializer, ExceptionSerializer, ObjectMapper } from "@eait-playerexp-cn/object-mapper";
import ApplicationHeadersProvider from "@src/shared/headers/ApplicationHeadersProvider";
import {
  anAuthorizationHeaderProvider,
  anHttpClient,
  anOAuthTokenProvider,
  aTracer,
  CompositeH<PERSON><PERSON><PERSON>rovider,
  <PERSON>ttp<PERSON>lient<PERSON><PERSON>r,
  LoggingCollector,
  OAuthBearerHeaderProvider,
  OAuthTokenProvider
} from "@eait-playerexp-cn/http-client";
import { ActivityFeed } from "@eait-playerexp-cn/activity-feed";
import { ActivityLogger, ContextSerializer, Logger } from "@eait-playerexp-cn/activity-logger";
import SentryRecorder from "@src/logging/SentryRecorder";
import {
  ApiRouteErrorHandler,
  ErrorLoggerServerPropsMiddleware,
  JsonErrorHandler,
  MicroServiceErrorHandler,
  RedisCache,
  RedisClientFactory,
  RequestFactory,
  SessionOptionsFactory
} from "@eait-playerexp-cn/server-kernel";
import CachedAccessTokenProvider from "@src/shared/tokens/CachedAccessTokenProvider";
import CreatorsWithProgramsHttpClient from "./server/creators/CreatorsWithProgramsHttpClient";
import LegacyCreatorsHttpClient from "./server/creators/CreatorsHttpClient";
import SearchCreatorsWithFlaggedStatusHttpClient from "./server/creators/SearchCreatorsWithFlaggedStatusHttpClient";
import LegacyTermsAndConditionsHttpClient from "./server/pactSafe/TermsAndConditionsHttpClient";
import CachedTermsAndConditions from "./server/pactSafe/CachedTermsAndConditions";
import EmailsHttpClient from "./server/opportunities/EmailsHttpClient";
import SearchCreatorsWithProgamCodeHttpClient from "./server/creators/SearchCreatorsWithProgramHttpClient";
import DiscordAccountHttpClient from "./server/channels/discord/DiscordAccountHttpClient";
import ConnectedAccountsHttpClient from "./server/channels/ConnectedAccountsHttpClient";
import ConnectYouTubeAccountController from "./server/channels/youtube/ConnectYouTubeAccountController";
import ConnectTikTokAccountController from "./server/channels/tiktok/ConnectTikTokAccountController";
import ConnectTwitchAccountController from "./server/channels/twitch/ConnectTwitchAccountController";
import ConnectInstagramAccountController from "./server/channels/instagram/ConnectInstagramAccountController";
import SaveFacebookPagesController from "./server/channels/facebook/SaveFacebookPagesController";
import ConnectDiscordAccountController from "./server/channels/discord/ConnectDiscordAccountController";
import { DashboardPageMapper } from "./server/contentManagement/DashboardPageMapper";
import MicroCopiesHttpClient from "./server/contentManagement/MicroCopiesHttpClient";
import ContentManagementService from "./services/ContentManagementService";
import { NotificationsPageMapper } from "./server/contentManagement/NotificationsPageMapper";
import { CommonPageMapper } from "./server/contentManagement/CommonPageMapper";
import { NotificationsBellMapper } from "./server/contentManagement/NotificationsBellMapper";
import { ErrorPageMapper } from "./server/contentManagement/ErrorPageMapper";
import { NoAccountPageMapper } from "./server/contentManagement/NoAccountPageMapper";
import { AgeRestrictionPageMapper } from "./server/contentManagement/AgeRestrictionPageMapper";
import { CommunicationPreferencesPageMapper } from "./server/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageMapper } from "./server/contentManagement/ConnectAccountsPageMapper";
import { BreadcrumbPageMapper } from "./server/contentManagement/BreadcrumbPageMapper";
import { InformationPageMapper } from "./server/contentManagement/InformationPageMapper";
import { TermsAndCondtionsPageMapper } from "./server/contentManagement/TermsAndConditionsPageMapper";
import ValidRegistrationCodesHttpClient from "./validRegistrationCodes/ValidRegistrationCodesHttpClient";
import { TimeRangeFilterPageMapper } from "./server/contentManagement/TimeRangeFilterPageMapper";
import { PaymentDetailsPageMapper } from "./server/contentManagement/PaymentDetailsPageMapper";
import PaymentInformationHttpClient from "./server/paymentInformation/PaymentInformationHttpClient";
import { PaymentInformationPageMapper } from "./server/contentManagement/PaymentInformationPageMapper";
import LegacyOpportunitiesHttpClient from "./server/opportunities/LegacyOpportunitiesHttpClient";
import OpportunityRegistrationsHttpClient from "./server/opportunities/OpportunityRegistrationsHttpClient";
import ParticipationsHttpClient from "./server/opportunities/ParticipationsHttpClient";
import { SignupCompletePageMapper } from "./server/contentManagement/SignupCompletePageMapper";
import {
  AuthenticateController,
  AuthenticationPlugin,
  Authenticator,
  AuthenticatorPlugin,
  CloseSessionController,
  CompositeAuthenticator,
  CreatorsHttpClient,
  DisabledAccountPlugin,
  InactiveAccountPlugin,
  PlayerProfilesHttpClient,
  RedirectToLoginController,
  RedirectToLogoutController,
  ShowInitialMessagePlugin,
  SuccessfulAuthenticationPlugin,
  UpToDateTermsAndConditionsPlugin
} from "@eait-playerexp-cn/authentication";
import TelemetryRecorder from "./shared/logging/TelemetryRecorder";
import { UnderageRequestToJoinPlugin } from "@eait-playerexp-cn/interested-creators-authentication-plugins";

const ApiContainer = Container.of("api");

ApiContainer.set(ExceptionSerializer, new ExceptionSerializer());
ApiContainer.set(CompositeSerializer, new CompositeSerializer([ApiContainer.get(ExceptionSerializer)]));
ApiContainer.set(ObjectMapper, new ObjectMapper(ApiContainer.get(CompositeSerializer)));
ApiContainer.set(ContextSerializer, new ContextSerializer(ApiContainer.get(ObjectMapper)));
ApiContainer.set(AttributesSerializer, new AttributesSerializer(ApiContainer.get(ObjectMapper)));
ApiContainer.set(AttributesConverter, new AttributesConverter(ApiContainer.get(AttributesSerializer)));
ApiContainer.set(
  ActivityFeed,
  new ActivityFeed([
    new TelemetryRecorder(),
    new ActivityLogger(new Logger(config.LOG_LEVEL), ApiContainer.get(ContextSerializer)),
    new SentryRecorder()
  ])
);
ApiContainer.set(CompositeCollector, new CompositeCollector([new LoggingCollector(ApiContainer.get(ActivityFeed))]));
ApiContainer.set(
  HttpClientTracer,
  aTracer()
    .withName(config.SERVICE_NAME)
    .withSerializer(ApiContainer.get(CompositeSerializer))
    .withLoggingCollector(ApiContainer.get(ActivityFeed))
    .build()
);
ApiContainer.set("supportedLocales", config.SUPPORTED_LOCALES);
ApiContainer.set("options", {
  supportedLocales: config.SUPPORTED_LOCALES,
  program: config.PROGRAM_CODE,
  feed: ApiContainer.get(ActivityFeed)
});
ApiContainer.set(
  "sessionOptions",
  new SessionOptionsFactory(config.sessionOptions).create(RedisClientFactory.create(config.redisClient))
);
ApiContainer.set(RedisCache, new RedisCache(config.cacheOptions, ApiContainer.get(ActivityFeed)));
ApiContainer.set(
  OAuthTokenProvider,
  anOAuthTokenProvider()
    .withBaseUrl(config.ACCESS_TOKEN_BASE_URL)
    .withRequestTimeout(+config.HTTP_REQUEST_TIMEOUT)
    .withClientCredentials(config.API_CLIENT_ID, config.API_CLIENT_SECRET)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .build()
);
ApiContainer.set(
  CachedAccessTokenProvider,
  new CachedAccessTokenProvider(ApiContainer.get(OAuthTokenProvider), ApiContainer.get(RedisCache))
);
ApiContainer.set(
  OAuthBearerHeaderProvider,
  anAuthorizationHeaderProvider().withOAuthTokenProvider(ApiContainer.get(CachedAccessTokenProvider)).build()
);
ApiContainer.set(ApplicationHeadersProvider, new ApplicationHeadersProvider());
ApiContainer.set(RequestFactory, new RequestFactory());
ApiContainer.set(
  JsonErrorHandler,
  new JsonErrorHandler([
    new MicroServiceErrorHandler(),
    new AuthorizationErrorHandler(ApiContainer.get("options"), ApiContainer.get(RequestFactory)),
    new ApiRouteErrorHandler(
      ApiContainer.get("options"),
      config.APP_DEBUG,
      ApiContainer.get(ExceptionSerializer),
      ApiContainer.get(RequestFactory)
    )
  ])
);
ApiContainer.set(
  ErrorLoggerServerPropsMiddleware,
  new ErrorLoggerServerPropsMiddleware(ApiContainer.get("options"), ApiContainer.get(RequestFactory))
);
ApiContainer.set(
  CompositeHeadersProvider,
  new CompositeHeadersProvider([
    ApiContainer.get(OAuthBearerHeaderProvider),
    ApiContainer.get(ApplicationHeadersProvider)
  ])
);
ApiContainer.set(
  "operationsClient",
  anHttpClient()
    .withBaseUrl(config.OPERATIONS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "opportunityClient",
  anHttpClient()
    .withBaseUrl(config.OPPORTUNITIES_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(
      new CompositeHeadersProvider([
        ApiContainer.get(OAuthBearerHeaderProvider),
        ApiContainer.get(ApplicationHeadersProvider)
      ])
    )
    .build()
);
ApiContainer.set(
  "contentSubmissionClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_SUBMISSION_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(
      new CompositeHeadersProvider([ApiContainer.get(OAuthBearerHeaderProvider), new ApplicationHeadersProvider()])
    )
    .build()
);
ApiContainer.set(
  "contentScanningClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_SCANNING_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "metadataClient",
  anHttpClient()
    .withBaseUrl(config.METADATA_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "communicationsClient",
  anHttpClient()
    .withBaseUrl(config.COMMUNICATIONS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "contentManagementClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_MANAGEMENT_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set(
  "legalClient",
  anHttpClient()
    .withBaseUrl(config.LEGAL_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);

ApiContainer.set("discordAccount", ApiContainer.get(DiscordAccountHttpClient));
ApiContainer.set("connectedAccounts", ApiContainer.get(ConnectedAccountsHttpClient));
ApiContainer.set(
  ConnectDiscordAccountController,
  new ConnectDiscordAccountController(ApiContainer.get("discordAccount"), config.DISCORD_CLIENT_REDIRECT_URI)
);
ApiContainer.set(
  RedirectToLoginController,
  new RedirectToLoginController(ApiContainer.get("options"), config.LOGIN_URL)
);
ApiContainer.set(CloseSessionController, new CloseSessionController(ApiContainer.get("options")));
ApiContainer.set(CreatorsHttpClient, new CreatorsHttpClient(ApiContainer.get("operationsClient")));
ApiContainer.set(
  AuthenticatorPlugin,
  new AuthenticatorPlugin(
    new Authenticator(
      ApiContainer.get("options"),
      new PlayerProfilesHttpClient(ApiContainer.get("operationsClient"), config.LOGIN_REDIRECT_URI),
      ApiContainer.get(CreatorsHttpClient)
    )
  )
);
ApiContainer.set(
  SuccessfulAuthenticationPlugin,
  new SuccessfulAuthenticationPlugin(ApiContainer.get("options"), ApiContainer.get(CreatorsHttpClient))
);
ApiContainer.set(DisabledAccountPlugin, new DisabledAccountPlugin(ApiContainer.get("options")));
ApiContainer.set(InactiveAccountPlugin, new InactiveAccountPlugin(ApiContainer.get("options")));
ApiContainer.set("statuses", new TermsAndConditionsStatusesHttpClient(ApiContainer.get("legalClient")));
ApiContainer.set(
  UpToDateTermsAndConditionsPlugin,
  new UpToDateTermsAndConditionsPlugin(ApiContainer.get("options"), ApiContainer.get("statuses"))
);
const plugins: AuthenticationPlugin[] = [ApiContainer.get(AuthenticatorPlugin)];
config.FLAG_INITIAL_MESSAGE && plugins.push(new ShowInitialMessagePlugin(ApiContainer.get("options")));
ApiContainer.set(UnderageRequestToJoinPlugin, new UnderageRequestToJoinPlugin(ApiContainer.get("options")));
plugins.push(ApiContainer.get(UnderageRequestToJoinPlugin));
plugins.push(ApiContainer.get(UpToDateTermsAndConditionsPlugin));
plugins.push(ApiContainer.get(SuccessfulAuthenticationPlugin));
plugins.push(ApiContainer.get(DisabledAccountPlugin));
plugins.push(ApiContainer.get(InactiveAccountPlugin));
ApiContainer.set(ContinueOnboardingPlugin, new ContinueOnboardingPlugin(ApiContainer.get("options")));
ApiContainer.set(
  StartOnboardingPlugin,
  new StartOnboardingPlugin(
    ApiContainer.get("options"),
    new RegistrationCodesHttpClient(ApiContainer.get("operationsClient"))
  )
);
plugins.push(ApiContainer.get(ContinueOnboardingPlugin));
plugins.push(ApiContainer.get(StartOnboardingPlugin));
ApiContainer.set(CompositeAuthenticator, new CompositeAuthenticator(plugins, ApiContainer.get(ActivityFeed)));
ApiContainer.set(
  AuthenticateController,
  new AuthenticateController(ApiContainer.get("options"), ApiContainer.get(CompositeAuthenticator))
);
ApiContainer.set(RedirectToLogoutController, new RedirectToLogoutController(config.LOGOUT_URL));
ApiContainer.set(
  SaveRegistrationCodeToSessionController,
  new SaveRegistrationCodeToSessionController(ApiContainer.get("options"))
);
ApiContainer.set("creators", ApiContainer.get(LegacyCreatorsHttpClient));
ApiContainer.set("creatorsWithCreatorPrograms", ApiContainer.get(CreatorsWithProgramsHttpClient));
ApiContainer.set("creatorsWithFlaggedStatus", ApiContainer.get(SearchCreatorsWithFlaggedStatusHttpClient));
ApiContainer.set("creatorsWithProgramCode", ApiContainer.get(SearchCreatorsWithProgamCodeHttpClient));
ApiContainer.set(
  ConnectYouTubeAccountController,
  new ConnectYouTubeAccountController(ApiContainer.get("connectedAccounts"), config.YOUTUBE_CLIENT_REDIRECT_URL)
);
ApiContainer.set(
  ConnectTikTokAccountController,
  new ConnectTikTokAccountController(ApiContainer.get("connectedAccounts"), config.TIKTOK_CLIENT_REDIRECT_URL)
);
ApiContainer.set(
  ConnectTwitchAccountController,
  new ConnectTwitchAccountController(ApiContainer.get("connectedAccounts"), config.TWITCH_CLIENT_REDIRECT_URL)
);
ApiContainer.set(
  ConnectInstagramAccountController,
  new ConnectInstagramAccountController(ApiContainer.get("connectedAccounts"), config.INSTAGRAM_CLIENT_REDIRECT_URL)
);
ApiContainer.set(
  SaveFacebookPagesController,
  new SaveFacebookPagesController(ApiContainer.get("connectedAccounts"), config.FACEBOOK_CLIENT_REDIRECT_URL)
);
ApiContainer.set("registrations", ApiContainer.get(ValidRegistrationCodesHttpClient));
ApiContainer.set("contentManagement", ApiContainer.get(MicroCopiesHttpClient));
ApiContainer.set("communications", ApiContainer.get(EmailsHttpClient));
ApiContainer.set("termsAndConditions", ApiContainer.get(LegacyTermsAndConditionsHttpClient));
ApiContainer.set("cachedTermsAndConditions", ApiContainer.get(CachedTermsAndConditions));
ApiContainer.set("operations", ApiContainer.get(LegacyOpportunitiesHttpClient));
ApiContainer.set("participations", ApiContainer.get(ParticipationsHttpClient));
ApiContainer.set("opportunityRegistrations", ApiContainer.get(OpportunityRegistrationsHttpClient));
ApiContainer.set("pageMappers", {
  dashboard: [new DashboardPageMapper()],
  termsAndConditions: [
    new TermsAndCondtionsPageMapper(),
    new InformationPageMapper(),
    new BreadcrumbPageMapper(),
    new CommonPageMapper()
  ],
  notifications: [new NotificationsPageMapper(), new CommonPageMapper(), new NotificationsBellMapper()],
  error: [new ErrorPageMapper(), new CommonPageMapper()],
  noAccount: [new NoAccountPageMapper(), new CommonPageMapper()],
  ageRestriction: [new AgeRestrictionPageMapper(), new CommonPageMapper()],
  paymentsPage: [
    new CommonPageMapper(),
    new NotificationsBellMapper(),
    new TimeRangeFilterPageMapper(),
    new PaymentDetailsPageMapper()
  ],
  paymentInformation: [new PaymentInformationPageMapper(), new BreadcrumbPageMapper(), new CommonPageMapper()],
  communicationPreferences: [
    new CommunicationPreferencesPageMapper(),
    new CommonPageMapper(),
    new ConnectAccountsPageMapper(),
    new BreadcrumbPageMapper()
  ],
  information: [
    new InformationPageMapper(),
    new CommonPageMapper(),
    new BreadcrumbPageMapper(),
    new CommunicationPreferencesPageMapper(),
    new ConnectAccountsPageMapper()
  ],
  signupComplete: [new SignupCompletePageMapper(), new CommonPageMapper()],
  documentation: [new CommonPageMapper()]
});
ApiContainer.set(
  ContentManagementService,
  new ContentManagementService(
    ApiContainer.get(MicroCopiesHttpClient),
    ApiContainer.get("pageMappers"),
    config.pagesMicroCopy,
    config.PROGRAM_CODE
  )
);

ApiContainer.set(
  "paymentClient",
  anHttpClient()
    .withBaseUrl(config.PAYMENTS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(ApiContainer.get(CompositeHeadersProvider))
    .build()
);
ApiContainer.set("paymentInformation", ApiContainer.get(PaymentInformationHttpClient));
ApiContainer.set(
  VerifyAccessToProgramServerPropsMiddleware,
  new VerifyAccessToProgramServerPropsMiddleware(ApiContainer.get("options"))
);
ApiContainer.set(
  VerifyIncompleteRegistrationPropsMiddleware,
  new VerifyIncompleteRegistrationPropsMiddleware(ApiContainer.get("options"))
);

export default ApiContainer;
