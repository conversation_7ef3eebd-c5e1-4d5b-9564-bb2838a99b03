{"title": "Charger un fichier depuis votre appareil", "fileUpload": "Chargement de fichiers", "chooseFile": "<PERSON><PERSON> le <PERSON>", "noFileChoosen": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi", "acceptedFormats": "Formats de fichiers acceptés", "maxFileSize": "<PERSON><PERSON> de <PERSON>er maximale", "maxLimitMessage": "Veuillez sélectionner le fichier dans les limites de taille autorisées.", "invalidFileTypeMessage": "Veuillez sélectionner le fichier avec les extensions autorisées.", "fileSelected": "<PERSON><PERSON><PERSON>", "fileUploading": "Chargement du fichier...", "success": {"title": "Your file has been uploaded", "content": "Your file has been uploaded and is being processed. You will receive a notification when it is finished. You can leave this page or add further content while your file is processing."}, "error": {"title": "File Upload Failed", "content": "There was an error uploading your file. Please try again."}, "uploadFileProgress": "Upload file progress", "removeSelectedFile": "Remove selected file", "contentDescriptionRequired": "Content Description is required", "contentDescriptionLongMessage": "Content Description is too long", "contentDescription": "Description", "contentDescriptionPlaceholder": "Add a Description for your content", "maxcharacterLimit": "800 Characters"}