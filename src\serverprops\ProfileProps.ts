import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import ContentManagementService from "@src/services/ContentManagementService";
import ProfilePagePropsController from "./controllers/ProfilePagePropsController";
import { OAuthError } from "@src/server/channels/tiktok/ConnectTikTokAccountController";
import config from "config";

export type ProfilePageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  error: OAuthError | null;
  pages: { id: string; name: string; accessToken: string }[];
  invalidTikTokScope: boolean;
  FLAG_COUNTRIES_BY_TYPE: boolean;
};

const profileProps = (locale: string) =>
  serverPropsControllerFactory(
    new ProfilePagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );
export default profileProps;
