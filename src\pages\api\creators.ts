import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "../../ApiContainer";
import { createRouter } from "next-connect";
import User from "@src/authentication/User";
import SaveCreatorProfileAction from "../../actions/Creators/SaveCreatorProfile/SaveCreatorProfileAction";
import SaveCreatorProfileInput from "../../actions/Creators/SaveCreatorProfile/SaveCreatorProfileInput";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(SaveCreatorProfileAction);
    const identity = req.session.identity;
    const code = req.session.registrationCode as string;

    const input = await action.execute(new SaveCreatorProfileInput(identity, req.body, code));

    if (identity.id === null && code && input?.id) {
      const creator = User.from(identity);
      creator.updateId(input.id);
      delete req.session.registrationCode;
      req.session.user = creator;
      req.session.save();
    }

    res.status(201).end();
  })
  .put(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const action = ApiContainer.get(SaveCreatorProfileAction);
    const identity = req.session.identity;

    const response = await action.execute(new SaveCreatorProfileInput(identity, req.body));
    if (response?.accountInformation?.status && identity.status !== response?.accountInformation?.status) {
      identity.status = response?.accountInformation?.status;
      req.session.user = identity;
      req.session.save();
    }

    res.status(201).json(response);
    res.end();
  });

export default router.handler({ onError });
