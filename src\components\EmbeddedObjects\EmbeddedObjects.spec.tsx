import { render, screen } from "@testing-library/react";
import EmbeddedObjects from "./EmbeddedObjects";
import Random from "__tests__/factories/Random";
import { axe } from "jest-axe";
import { ImageType } from "@components/RichText/RichText";

describe("EmbeddedObjects", () => {
  const media = {
    url: Random.imageUrl(),
    title: Random.string(),
    width: Random.number({ min: 500, max: 700 }),
    height: Random.number({ min: 500, max: 700 })
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows image when entry type is Image", () => {
    const EmbeddedObjectsProps = {
      entryType: "Image" as ImageType,
      media
    };

    render(<EmbeddedObjects {...EmbeddedObjectsProps} />);

    expect(screen.getByTestId("richText-image")).toBeInTheDocument();
  });

  it("returns null for unknown entry type", () => {
    const EmbeddedObjectsProps = {
      entryType: "Video" as ImageType,
      media
    };

    render(<EmbeddedObjects {...EmbeddedObjectsProps} />);

    expect(screen.queryByTestId("richText-image")).not.toBeInTheDocument();
  });

  it("is accessible", async () => {
    const EmbeddedObjectsProps = {
      entryType: "Image" as ImageType,
      media
    };
    const { container } = render(<EmbeddedObjects {...EmbeddedObjectsProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
