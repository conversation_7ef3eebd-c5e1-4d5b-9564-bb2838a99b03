import { act, render, screen } from "@testing-library/react";
import SubmitWebsiteContentModal, {
  SubmitWebsiteContentModalProps
} from "@components/pages/content-submission/SubmitWebsiteContentModal";
import { axe } from "jest-axe";
import React from "react";
import { submitWebsiteContentTranslations } from "../../../translations";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "../../../../src/services/OpportunityService";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityWithPerks";
import {
  ContentsWithDeliverable,
  SubmittedContentWithDeliverableDetail
} from "../../../../src/services/SubmittedContentService";
import { aSubmittedContentWithDeliverable } from "../../../factories/opportunities/SubmittedContentWithDeliverable";
import userEvent from "@testing-library/user-event";
import Random from "../../../factories/Random";
import { useDependency } from "@src/context/DependencyContext";
import { ContentDetails } from "@components/pages/content-submission/ContentDeliverablesTab";

jest.mock("../../../../src/context/DependencyContext");

describe("SubmitWebsiteContentModal", () => {
  const websiteContentModalProps: SubmitWebsiteContentModalProps = {
    ...submitWebsiteContentTranslations,
    setUpdatedContent: jest.fn(),
    onClose: jest.fn(),
    contentTypes: [
      { name: "", label: "Please select", value: "" },
      { name: "VIDEO", label: "Video", value: "VIDEO" },
      { name: "IMAGE", label: "image", value: "IMAGE" }
    ],
    participationId: "a0YK0000004zHaoMAE",
    thumbnail: "https://yt3.ggpht.com/ytc/AAUvwnjhXC8zLwHT7QZbgfxeogf0HL7IXYQA5GmUBw=s88-c-k-c0x00ffffff-no-rj11",
    YOUTUBE_HOSTS: ["youtube.com", "m.youtube.com"],
    TWITCH_HOSTS: ["twitch.tv"],
    INSTAGRAM_HOSTS: ["instagram.com"],
    FACEBOOK_HOSTS: ["facebook.com"],
    TIKTOK_HOSTS: ["tiktok.com"],
    analytics: ({} as unknown) as BrowserAnalytics,
    opportunity: new OpportunityWithDeliverables(aOpportunityWithPerksAndContentSubmissionWithDeliverables()),
    locale: "en-us",
    deliverableId: "",
    enableUpdateWebsite: false,
    content: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ errorHandler: jest.fn() });
  });

  it("shows submit content modal for website", () => {
    render(<SubmitWebsiteContentModal {...websiteContentModalProps} />);

    expect(screen.getByText(websiteContentModalProps.title)).toBeInTheDocument();
    expect(screen.getByText(websiteContentModalProps.formLabels.contentTitle)).toBeInTheDocument();
    expect(screen.getByText(websiteContentModalProps.formLabels.contentUrl)).toBeInTheDocument();
    expect(screen.getByText(websiteContentModalProps.formLabels.contentType)).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText(websiteContentModalProps.formLabels.contentTitlePlaceholder)
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText(websiteContentModalProps.formLabels.contentUrlPlaceholder)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.close })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.submit })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.cancel })).toBeInTheDocument();
  });

  it("executes cancel handler when 'Close' button is clicked", async () => {
    render(<SubmitWebsiteContentModal {...websiteContentModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.close }));

    expect(websiteContentModalProps.onClose).toHaveBeenCalled();
  });

  it("shows submit website content modal for a deliverable", async () => {
    const deliverableId = Random.uuid();
    const contentName = Random.string();
    const contentId = Random.uuid();
    const content = {
      [deliverableId]: [
        new SubmittedContentWithDeliverableDetail(
          (aSubmittedContentWithDeliverable({
            name: contentName,
            status: "CHANGE_REQUESTED",
            id: contentId
          }) as unknown) as ContentsWithDeliverable
        )
      ]
    };

    await act(() =>
      render(
        <SubmitWebsiteContentModal
          {...websiteContentModalProps}
          content={(content as unknown) as ContentDetails}
          enableUpdateWebsite
          deliverableId={deliverableId}
          selectedContentId={contentId}
        />
      )
    );

    expect(screen.getByText(websiteContentModalProps.title)).toBeInTheDocument();
    expect(screen.getByText(websiteContentModalProps.formLabels.contentTitle)).toBeInTheDocument();
    expect(screen.getByLabelText(websiteContentModalProps.formLabels.contentTitle)).toHaveValue(contentName);
    expect(screen.getByText(websiteContentModalProps.formLabels.contentUrl)).toBeInTheDocument();
    expect(screen.getByText(websiteContentModalProps.formLabels.contentType)).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText(websiteContentModalProps.formLabels.contentTitlePlaceholder)
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText(websiteContentModalProps.formLabels.contentUrlPlaceholder)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.close })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.submit })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.cancel })).toBeInTheDocument();
  });

  it("executes cancel handler when 'Close' button is clicked", async () => {
    render(<SubmitWebsiteContentModal {...websiteContentModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: websiteContentModalProps.buttonsLabels.close }));

    expect(websiteContentModalProps.onClose).toHaveBeenCalled();
  });

  it("is accessible", async () => {
    const { container } = render(<SubmitWebsiteContentModal {...websiteContentModalProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
