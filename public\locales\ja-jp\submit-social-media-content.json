{"title": "以下からコンテンツを追加する：{{mediaType}}", "infoLabel": "追加するURLが{{mediaType}}に表示されているものであることを確認してください：", "contentUrl": "{{mediaType}}のURLを入力してください：", "contentUrlPlaceholder": "例: https://www.{{mediaType}}.com/mylink", "duplicateUrl": "このURLはすでに送信されています。", "instagramErrorUrl": "This content may be posted 30 days ago, moved out from main Gallery, may not belong to a professional account or deleted.", "videoNotFromChannel": "この動画は、CNアカウントにリンクされているソーシャルチャンネルからのものではありません。", "youtubeVideoError": "与えられたIDでYoutube動画が見つかりません。", "genericContentError": "有効なURLを入力してください", "unsupportedContentError": "送信しようとしているこのコンテンツが、選択したオプションと一致しません", "contentUrlRequired": "{{mediaType}} URLが必要です", "cancelButton": "キャンセル", "submitButton": "コンテンツを送信する", "unsafeUrlError": "このウェブサイトまたはドメインからコンテンツを送信することはできません。", "urlNotFromConnectedAccount": "入力したURLは、このリンクされた\"ソーシャルチャンネル\"アカウントのものではありません。", "urlNotFromConnectedChannel": "送信しようとしているURLは、{{accountName}} {{mediaType}}アカウントからのものではありません。", "unknownTikTokVideo": "投稿しようとしているURLは、TikTokのアカウント{{accountName}}のものではないか、公開されていないものです。", "invalidSocialSubmission": "ソーシャルメディアのコンテンツを保存できません：アカウントIDを空白にしないでください", "accountAuthorizationFailure": "ソーシャルメディアのコンテンツを保存できません：TikTokのアカウント情報を取得できません。トークンの有効期限が切れているか無効になっています", "cannotExpandUrl": "Expanding this short URL is taking longer than usual. Please try again later.", "cannotSubmitContentInvalidInput": "Submitted URL is invalid", "invalidFacebookPage": "Unable to submit. This content does not belong to the Facebook page selected.", "unSupportedContentType": "Only Instagram videos and reels are allowed for submission. The content type you're trying to upload is not supported.", "unSupportedContentTypeForMedia": "Only Instagram videos, reels, and photos are allowed for submission. The content type you're trying to upload is not supported.", "duplicateScannedContentUrl": "Your Community Manager has submitted this URL."}