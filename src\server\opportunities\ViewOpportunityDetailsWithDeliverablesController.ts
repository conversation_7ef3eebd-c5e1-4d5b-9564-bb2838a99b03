import { NextApiResponse } from "next";
import { Service } from "typedi";
import CreatorsWithFlaggedStatusHttpClient from "../creators/CreatorsWithFlaggedStatusHttpClient";
import CreatorWithFlaggedStatus from "../creators/CreatorWithFlaggedStatus";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import LegacyOpportunitiesHttpClient from "./LegacyOpportunitiesHttpClient";
import OpportunityWithDeliverables from "./OpportunityWithDeliverables";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { CreatorResponse } from "@eait-playerexp-cn/creator-types";

@Service()
class ViewOpportunityDetailsWithDeliverablesController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    private readonly opportunities: LegacyOpportunitiesHttpClient,
    private readonly creators: CreatorsWithFlaggedStatusHttpClient
  ) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const opportunityId = req.query.id;
    const user = this.identity(req);

    const [opportunities, creators]: [
      opportunities: OpportunityWithDeliverables,
      creators: CreatorWithFlaggedStatus | CreatorResponse
    ] = await Promise.all([
      this.opportunities.withOpportunityDeliverables(opportunityId as string, user.id),
      this.creators.withIdForProgramProfile(user.id)
    ]);

    const resp = {
      opportunity: opportunities,
      creator: creators
    };

    this.json(res, resp);
  }
}

export default ViewOpportunityDetailsWithDeliverablesController;
