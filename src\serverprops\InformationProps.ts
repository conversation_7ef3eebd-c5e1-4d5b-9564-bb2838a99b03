import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import InformationPagePropController from "./controllers/InformationPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";
import config from "config";

const informationProps = (locale: string) =>
  serverPropsControllerFactory(
    new InformationPagePropController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default informationProps;
