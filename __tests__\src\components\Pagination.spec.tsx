import Pagination, { DEFAULT_PAGE, Paginator } from "@components/pagination/Pagination";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { mockMatchMedia } from "__tests__/helpers/window";

describe("Pagination", () => {
  mockMatchMedia();

  beforeEach(() => jest.clearAllMocks());

  describe("Pagination Component", () => {
    it("renders the contents of the first page by default", async () => {
      const pages = [1, 2, 3];

      render(<Pagination pages={pages} currentPage={1} next={"Next"} prev={"Previous"} onPageChange={jest.fn()} />);

      expect(await screen.findByText(/1/i)).toHaveClass("pagination-text-selected");
      expect(screen.getByText(/2/i)).not.toHaveClass("pagination-text-selected");
    });

    it("renders the content of the page 3 on being clicked", async () => {
      const onPageChange = jest.fn();
      render(
        <Pagination pages={[1, 2, 3]} currentPage={2} onPageChange={onPageChange} next={"Next"} prev={"Previous"} />
      );

      await userEvent.click(await screen.findByText(/3/i));

      await waitFor(() => {
        expect(onPageChange).toHaveBeenCalledTimes(1);
        expect(onPageChange).toHaveBeenCalledWith(3);
      });
    });

    it("handles previous button click correctly", async () => {
      const onPageChange = jest.fn();
      render(
        <Pagination pages={[1, 2, 3]} currentPage={2} onPageChange={onPageChange} next={"Next"} prev={"Previous"} />
      );

      await userEvent.click(screen.getByText("Previous"));

      await waitFor(() => {
        expect(onPageChange).toHaveBeenCalledTimes(1);
        expect(onPageChange).toHaveBeenCalledWith(1);
      });
    });

    it("handles next button click correctly", async () => {
      const onPageChange = jest.fn();
      render(
        <Pagination pages={[1, 2, 3]} currentPage={2} onPageChange={onPageChange} next={"Next"} prev={"Previous"} />
      );

      await userEvent.click(screen.getByText("Next"));

      await waitFor(() => {
        expect(onPageChange).toHaveBeenCalledWith(3);
      });
    });

    it("disables the previous button on the first page", async () => {
      const onPageChange = jest.fn();
      render(
        <Pagination pages={[1, 2, 3]} currentPage={1} onPageChange={onPageChange} next={"Next"} prev={"Previous"} />
      );

      const prevButton = screen.getByText("Previous").closest("button");
      expect(prevButton).toBeDisabled();

      await userEvent.click(prevButton);

      expect(onPageChange).not.toHaveBeenCalled();
    });

    it("disables the next button on the last page", async () => {
      const onPageChange = jest.fn();
      render(
        <Pagination pages={[1, 2, 3]} currentPage={3} onPageChange={onPageChange} next={"Next"} prev={"Previous"} />
      );

      const nextButton = screen.getByText("Next").closest("button");
      expect(nextButton).toBeDisabled();

      await userEvent.click(nextButton);

      expect(onPageChange).not.toHaveBeenCalled();
    });

    it("does nothing when clicking the current page number", async () => {
      const onPageChange = jest.fn();
      render(
        <Pagination pages={[1, 2, 3]} currentPage={2} onPageChange={onPageChange} next={"Next"} prev={"Previous"} />
      );

      await userEvent.click(screen.getByText("2"));

      expect(onPageChange).not.toHaveBeenCalled();
    });

    it("handles navigating to the last page correctly", async () => {
      const onPageChange = jest.fn();
      const { rerender } = render(
        <Pagination
          pages={[1, 2, 3, 4, 5]}
          currentPage={4}
          onPageChange={onPageChange}
          next={"Next"}
          prev={"Previous"}
        />
      );

      await userEvent.click(screen.getByText("5"));
      await waitFor(() => {
        expect(onPageChange).toHaveBeenCalledWith(5);
      });
      onPageChange.mockClear();

      rerender(
        <Pagination
          pages={[1, 2, 3, 4, 5]}
          currentPage={5}
          onPageChange={onPageChange}
          next={"Next"}
          prev={"Previous"}
        />
      );

      expect(screen.getByText("4")).toBeInTheDocument();
      expect(screen.getByText("5")).toHaveClass("pagination-text-selected");
      const nextButton = screen.getByText("Next").closest("button");
      expect(nextButton).toBeDisabled();
    });

    it("handles proper page view when navigating from middle to first page", async () => {
      const onPageChange = jest.fn();
      const { rerender } = render(
        <Pagination
          pages={[1, 2, 3, 4, 5]}
          currentPage={3}
          onPageChange={onPageChange}
          next={"Next"}
          prev={"Previous"}
        />
      );

      await userEvent.click(screen.getByText("Previous"));
      await waitFor(() => {
        expect(onPageChange).toHaveBeenCalledWith(2);
      });
      onPageChange.mockClear();

      rerender(
        <Pagination
          pages={[1, 2, 3, 4, 5]}
          currentPage={2}
          onPageChange={onPageChange}
          next={"Next"}
          prev={"Previous"}
        />
      );

      await userEvent.click(screen.getByText("Previous"));
      await waitFor(() => {
        expect(onPageChange).toHaveBeenCalledWith(1);
      });
      onPageChange.mockClear();

      rerender(
        <Pagination
          pages={[1, 2, 3, 4, 5]}
          currentPage={1}
          onPageChange={onPageChange}
          next={"Next"}
          prev={"Previous"}
        />
      );

      expect(screen.getByText("1")).toHaveClass("pagination-text-selected");
      expect(screen.getByText("2")).toBeInTheDocument();
      const prevButton = screen.getByText("Previous").closest("button");
      expect(prevButton).toBeDisabled();
    });
  });

  describe("Paginator Class", () => {
    it("calculates pages correctly based on total records", () => {
      const paginator = new Paginator(25, 1, 10);

      expect(paginator.pages()).toEqual([1, 2, 3]);
    });

    it("returns correct current page", () => {
      const paginator = new Paginator(25, 2, 10);

      expect(paginator.getCurrentPage()).toBe(2);
    });

    it("determines if pagination is needed", () => {
      const paginatorNeeded = new Paginator(15, 1, 10);

      expect(paginatorNeeded.hasPages()).toBe(true);
    });

    it("uses default values when not provided", () => {
      const paginator = new Paginator(25);

      expect(paginator.getCurrentPage()).toBe(DEFAULT_PAGE);
      expect(paginator.pages()).toEqual([1, 2, 3]);
    });

    it("caches calculated pages", () => {
      const paginator = new Paginator(25, 1, 10);
      const pages1 = paginator.pages();
      const pages2 = paginator.pages();

      expect(pages1).toBe(pages2);
    });
  });
});
