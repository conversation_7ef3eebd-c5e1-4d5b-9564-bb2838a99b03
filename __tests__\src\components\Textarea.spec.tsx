import { act, fireEvent, render, screen } from "@testing-library/react";
import Textarea from "../../../src/components/Textarea";
import { axe } from "jest-axe";

describe("Textarea", () => {
  const textareaProps = {
    placeholder: "Enter text",
    label: "Test Label",
    errorMessage: "",
    maxcharacterLimit: "100 Char"
  };

  it("shows textarea component", () => {
    render(<Textarea {...textareaProps} />);

    expect(screen.getByText("Test Label")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Enter text")).toBeInTheDocument();
    expect(screen.getByText("100 Char")).toBeInTheDocument();
  });

  it("shows error message when provided", () => {
    const errorMessage = "input is required";
    render(<Textarea {...textareaProps} errorMessage={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it("shows focused and blure border", () => {
    render(<Textarea {...textareaProps} />);
    const textarea = screen.getByPlaceholderText("Enter text");

    fireEvent.focus(textarea);
    expect(textarea).toHaveClass("form-textarea-input-box-focused");

    fireEvent.blur(textarea);
    expect(textarea).not.toHaveClass("form-textarea-input-box-focused");
  });

  it("show textarea disabled", () => {
    render(<Textarea {...textareaProps} disabled />);

    expect(screen.getByPlaceholderText("Enter text")).toBeDisabled();
    expect(screen.getByPlaceholderText("Enter text")).toHaveClass("form-textarea-input-box-disabled");
  });

  it("correctly decodes HTML entities and Unicode sequences", () => {
    const encodedText = "&#128187; Tech &amp; Work";
    const expectedDecodedText = "💻 Tech & Work";
    render(<Textarea {...textareaProps} value={encodedText} />);

    expect(screen.getByPlaceholderText("Enter text")).toHaveValue(expectedDecodedText);
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<Textarea {...textareaProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
