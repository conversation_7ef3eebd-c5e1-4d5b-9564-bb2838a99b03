import { render, screen } from "@testing-library/react";
import UploadContentSubmissionModal, {
  UploadContentSubmissionModalProps
} from "@components/pages/content-submission/UploadContentSubmissionModal";
import { axe } from "jest-axe";
import React from "react";
import { submitWebsiteContentTranslations } from "../../../translations";
import { submitFileContentTranslations } from "../../../translations";
import BrowserAnalytics from "../../../../src/analytics/BrowserAnalytics";
import { OpportunityWithDeliverables } from "@src/services/OpportunityService";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../factories/opportunities/OpportunityWithPerks";
import userEvent from "@testing-library/user-event";
import { useDependency } from "@src/context/DependencyContext";
import Random from "__tests__/factories/Random";

jest.mock("../../../../src/context/DependencyContext");

describe("UploadContentSubmissionModal", () => {
  const uploadContentSubmissionModalProps: UploadContentSubmissionModalProps = {
    ...submitWebsiteContentTranslations,
    onClose: jest.fn(),
    title: submitFileContentTranslations.title,
    buttonsLabels: submitFileContentTranslations.buttonsLabels,
    formLabels: { ...submitWebsiteContentTranslations.formLabels, ...submitFileContentTranslations.formLabels },
    fileTypes: [
      {
        type: "Video",
        extensions: ["MOV"]
      }
    ],
    contentTypes: [],
    participationId: "",
    thumbnail: "",
    analytics: ({} as unknown) as BrowserAnalytics,
    locale: "en-us",
    opportunity: new OpportunityWithDeliverables(aOpportunityWithPerksAndContentSubmissionWithDeliverables()),
    setUpdatedContent: jest.fn(),
    creatorId: Random.uuid(),
    contentFormat: "FILE",
    nucleusId: 122
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({ errorHandler: jest.fn() });
  });

  it("shows modal for upload file content", () => {
    render(<UploadContentSubmissionModal {...uploadContentSubmissionModalProps} />);

    expect(screen.getByText(uploadContentSubmissionModalProps.title)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionModalProps.formLabels.contentTitle)).toBeInTheDocument();
    expect(screen.getByText(uploadContentSubmissionModalProps.formLabels.contentType)).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText(uploadContentSubmissionModalProps.formLabels.contentTitlePlaceholder)
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: uploadContentSubmissionModalProps.buttonsLabels.close })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: uploadContentSubmissionModalProps.buttonsLabels.upload })
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: uploadContentSubmissionModalProps.buttonsLabels.cancel })
    ).toBeInTheDocument();
  });

  it("executes cancel handler when 'Close' button is clicked", async () => {
    render(<UploadContentSubmissionModal {...uploadContentSubmissionModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: uploadContentSubmissionModalProps.buttonsLabels.close }));

    expect(uploadContentSubmissionModalProps.onClose).toHaveBeenCalled();
  });

  it("is accessible", async () => {
    const { container } = render(<UploadContentSubmissionModal {...uploadContentSubmissionModalProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
