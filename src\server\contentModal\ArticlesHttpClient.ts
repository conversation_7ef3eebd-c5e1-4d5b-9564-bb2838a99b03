import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Inject, Service } from "typedi";
import ArticleNotFound from "@src/utils/ArticleNotFoundException";

export type ArticlesCriteria = {
  locale: string;
};

type Articles = {
  data:
    | Record<string, string>
    | {
        error?: string;
        error_description?: string;
        code?: string;
        message?: string;
        status?: number;
        errors?: Record<string, string>;
      };
};

@Service()
class ArticlesHttpClient {
  constructor(@Inject("contentManagementClient") private client: TraceableHttpClient) {}

  async matching(slug: string, criteria: ArticlesCriteria): Promise<Articles> {
    try {
      const response = await this.client.get(`/v1/articles/${slug}`, {
        query: { ...criteria, published: true }
      });
      return response.data;
    } catch (err) {
      if (err.response?.status === 404) {
        throw new ArticleNotFound(err);
      }
      throw err;
    }
  }
}

export default ArticlesHttpClient;
