import { useDetectScreen } from "@src/utils";
import React, { ReactNode } from "react";

type TableProps = {
  children: ReactNode;
};

const Table = ({ children }: TableProps) => {
  const isMobile = useDetectScreen(767);

  if (!isMobile) {
    return (
      <div className="rich-text-table-wrapper">
        <table className="rich-text-table" data-testid="rich-text-table-desktop">
          <tbody>{children}</tbody>
        </table>
      </div>
    );
  }

  const headers: ReactNode[] = [];
  const rows: ReactNode[] = [];

  React.Children.forEach(children, (child) => {
    if (React.isValidElement(child) && child.type === "tr") {
      const cells: ReactNode[] = [];
      React.Children.forEach(child.props.children, (cell) => {
        if (cell?.type === "th") {
          headers.push(cell.props.children);
        } else if (cell?.type === "td") {
          cells.push(cell.props.children);
        }
      });
      if (cells.length) rows.push(cells);
    }
  });

  return (
    <div className="rich-text-table-wrapper rich-text-table" data-testid="rich-text-table-mobile">
      {headers.map((header, headerIndex) => (
        <div key={`header-${headerIndex}`} className="rich-text-table-section">
          <div className="rich-text-table-header" role="columnheader">
            {header}
          </div>
          {rows.map((row, rowIndex) => (
            <div key={`row-${rowIndex}`} className="rich-text-table-row">
              <div className="rich-text-table-cell" role="cell">
                {row[headerIndex]}
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default Table;
