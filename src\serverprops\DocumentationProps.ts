import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import DocumentationPagePropsController from "./controllers/DocumentationPagePropsController";
import IndexesHttpClient from "@src/server/contentModal/IndexesHttpClient";
import ContentManagementService from "@src/services/ContentManagementService";
import config from "config";

const documentationProps = (locale: string) =>
  serverPropsControllerFactory(
    new DocumentationPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(IndexesHttpClient),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default documentationProps;
