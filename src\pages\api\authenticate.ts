import "reflect-metadata";
import type { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import onError from "@src/shared/headers/AuthErrorHandler";
import logLocale from "@src/middleware/LocaleLogger";
import { AuthenticateController } from "@eait-playerexp-cn/authentication";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(logLocale)
  .use(addTelemetryInformation)
  .get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(AuthenticateController);
    await controller.handle(req, res);
  });

export default router.handler({ onError });
