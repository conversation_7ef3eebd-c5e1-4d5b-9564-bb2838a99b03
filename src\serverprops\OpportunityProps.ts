import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import OpportunityPagePropsController from "./controllers/OpportunityPagePropsController";
import { FacebookPage } from "@src/server/channels/ConnectedAccountsHttpClient";
import { OAuthError } from "@src/server/channels/tiktok/ConnectTikTokAccountController";
import config from "config";

export type OpportunityPageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
  pages: FacebookPage[];
  error: OAuthError;
  invalidTikTokScope: boolean;
  referer: string;
  WATERMARKS_URL: string;
  YOUTUBE_HOSTS: string[];
  TWITCH_HOSTS: string[];
  INSTAGRAM_HOSTS: string[];
  FACEBOOK_HOSTS: string[];
  TIKTOK_HOSTS: string[];
  accountConnected: boolean;
};

const opportunityProps = (locale: string) =>
  serverPropsControllerFactory(
    new OpportunityPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default opportunityProps;
