import { useRouter } from "next/router";
import React, { FC, memo, useCallback, useEffect, useState } from "react";
import { useAppContext } from "@src/context";
import { ERROR, onToastClose, toastContent, VALIDATION_ERROR } from "../../../utils";
import FormTitle from "../../formTitle/FormTitle";
import { Tabs } from "../../tabs";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import PaymentIFrameTab from "./PaymentIFrameTab";
import PayableStatus from "./PayableStatus";
import { PaymentTabs, usePayableStatus } from "./usePayableStatus";
import PaymentDetailsTab, { TransactionHistoryLabelProps } from "./PaymentDetailsTab/PaymentDetailsTab";
import { PaymentDetailsLabelsProps } from "./PaymentDetailsTab/PaymentDetailsTab";
import { PaymentBannersLabelsProps } from "./PaymentDetailsTab/PaymentBanner";
import { useDetectScreen } from "../../../utils";
import { DefaultPaymentDateRange, PaymentsCriteria } from "@src/services/paymentsStatistics/PaymentsService";
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE, Paginator } from "@components/pagination/Pagination";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";

type PaymentInfoLabelsProps = {
  paymentSettings: string;
  invoicesHistory: string;
  paymentsHistory: string;
  paymentSettingsDescription: string;
  paymentInvoicesDescription: string;
  paymentHistoryDescription: string;
  pageTitle: string;
  payableStatus: string;
  nonPayableStatus: string;
  nonPayableStatusHelp: string;
  nonPayableStatusStart: string;
  nonPayableStatusClickHere: string;
  yourPayments: string;
  buttons: {
    next: string;
    prev: string;
    close: string;
  };
};

export type PaymentFilterLabelsProps = {
  filters: string;
  dateRange: string;
  startDate: string;
  endDate: string;
  paymentStatus: string;
  programCode: string;
  applyFilters: string;
  startDateRequired: string;
  endDateRequired: string;
  startDateError: string;
  endDateError: string;
  sameDateError: string;
  range: {
    allTime: string;
    thisMonth: string;
    pastMonth: string;
    past90Days: string;
    past6Months: string;
    yearToDate: string;
    pastYear: string;
    customDateRange: string;
  };
  status: {
    all: string;
    paid: string;
    pending: string;
  };
  program: {
    all: string;
    creatorNetwork: string;
    affiliate: string;
    theSims: string;
  };
  buttons: {
    ok: string;
    cancel: string;
  };
  header: {
    calendar: string;
  };
};

type PaymentInformationPageProps = {
  labels: {
    paymentInfoLabels: PaymentInfoLabelsProps;
    paymentBannerLabels: PaymentBannersLabelsProps;
    paymentDetailsLabels: PaymentDetailsLabelsProps;
    paymentHistoryGridLabels: TransactionHistoryLabelProps;
    paymentFilterLabels: PaymentFilterLabelsProps;
    buttonLabel: string;
  };
  unhandledError: string;
  PAYMENTS_DEFAULT_START_DATE: string;
  analytics: BrowserAnalytics;
};

export type PaymentFilterProps = {
  label: string;
  value: string;
  code: string;
};

const MODE = "horizontal";

const PaymentInformationPage: FC<PaymentInformationPageProps> = ({
  labels,
  unhandledError,
  PAYMENTS_DEFAULT_START_DATE,
  analytics
}) => {
  const router = useRouter();
  const {
    dispatch,
    state: { isValidationError = false, validationErrors = [], isError = false, isLoading = false } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const defaultPaymentDateRange = new DefaultPaymentDateRange(PAYMENTS_DEFAULT_START_DATE);

  const [activeTabId, setActiveTabId] = useState<PaymentTabs>("PAYMENT_HISTORY");

  const [paginator, setPaginator] = useState<Paginator>(null);

  const isMobile = useDetectScreen(767);

  const [selectedFilters, setSelectedFilters] = useState<PaymentFilterProps[]>([]);

  const [selectedCriteria, setSelectedCriteria] = useState<PaymentsCriteria>({
    startDate: defaultPaymentDateRange.startDate,
    endDate: defaultPaymentDateRange.endDate,
    page: DEFAULT_PAGE,
    size: DEFAULT_PAGE_SIZE
  });

  const resetDateRangeFields = useCallback(() => {
    setSelectedCriteria({
      ...selectedCriteria,
      startDate: defaultPaymentDateRange.startDate,
      endDate: defaultPaymentDateRange.endDate
    });
  }, [selectedCriteria]);

  const initializePagination = (total: number) => {
    const paginatorInstance = new Paginator(total);
    setPaginator(paginatorInstance);
  };

  const {
    paymentsIframe = false,
    isPayable = null,
    paymentsHistory = null,
    setPaymentsHistory,
    isCreatorCodeAssigned = false
  } = usePayableStatus(activeTabId, router.locale, stableDispatch, selectedCriteria, initializePagination);

  const {
    paymentInfoLabels,
    paymentBannerLabels,
    paymentDetailsLabels,
    paymentHistoryGridLabels,
    paymentFilterLabels,
    buttonLabel
  } = labels;

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(validationErrors)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
      });
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  useEffect(() => {
    if (router?.query?.tab && router?.query?.tab === "payment-settings") setActiveTabId("PAYEE_ONBOARDING");
  }, []);

  const handlePaymentDetails = () => {
    analytics.clickedPaymentDetailsIncompleteTooltip({ locale: router.locale });
    setActiveTabId("PAYEE_ONBOARDING");
  };

  const onPayableClickHandler = () => {
    setActiveTabId("PAYMENT_HISTORY");
  };

  const onPaymentBannerClick = () => {
    analytics.clickedPaymentDetailsIncompleteHelperBanner({ locale: router.locale });
    setActiveTabId("PAYEE_ONBOARDING");
  };

  const onTabClick = (tabId) => {
    if (tabId === "PAYEE_ONBOARDING") analytics.clickedPaymentSettingsTab({ locale: router.locale });
    setActiveTabId(tabId);
  };

  const tabs = [
    {
      title: `${paymentInfoLabels.yourPayments}`,
      id: "PAYMENT_HISTORY",
      content: (
        <PaymentDetailsTab
          {...{
            isPayable,
            paymentsHistory,
            labels: {
              paymentBannerLabels,
              paymentDetailsLabels,
              paymentHistoryGridLabels,
              paymentFilterLabels,
              buttonLabel
            },
            isMobile,
            locale: router.locale,
            defaultPaymentDateRange,
            onPaymentBannerClick,
            isShowingPagination: paginator?.hasPages() || false,
            paginationProps: {
              next: paymentInfoLabels.buttons.next,
              prev: paymentInfoLabels.buttons.prev,
              pages: paginator?.pages() || [],
              currentPage: selectedCriteria.page,
              onPageChange: (pageNumber) => {
                setSelectedCriteria({
                  ...selectedCriteria,
                  page: pageNumber
                });
              }
            },
            setPaymentsHistory,
            selectedCriteria,
            setSelectedCriteria,
            selectedFilters,
            setSelectedFilters,
            resetDateRangeFields,
            initializePagination,
            PAYMENTS_DEFAULT_START_DATE,
            isCreatorCodeAssigned
          }}
          analytics={analytics}
        />
      )
    },
    {
      title: `${paymentInfoLabels.paymentSettings}`,
      id: "PAYEE_ONBOARDING",
      content: <PaymentIFrameTab {...{ ...paymentsIframe, ...paymentInfoLabels, activeTabId, isLoading }} />
    }
  ];

  return (
    <div className="payment-info creator-wallet">
      <div className="payment-info-header">
        <FormTitle className="payment-info-title" title={paymentInfoLabels.pageTitle} />
        {isPayable !== null && (
          <PayableStatus
            tooltip={
              isPayable ? null : (
                <div>
                  {paymentInfoLabels.nonPayableStatusHelp}
                  <button onClick={() => handlePaymentDetails()} className="payment-info-tooltip-content-click-here">
                    {paymentInfoLabels.nonPayableStatusClickHere}
                  </button>
                  {paymentInfoLabels.nonPayableStatusStart}
                </div>
              )
            }
            onClick={onPayableClickHandler}
          >
            {isPayable ? paymentInfoLabels.payableStatus : paymentInfoLabels.nonPayableStatus}
          </PayableStatus>
        )}
      </div>
      {activeTabId && <Tabs {...{ tabs, activeTabId, onTabClick, mode: MODE, analytics }} />}
    </div>
  );
};

export default memo(PaymentInformationPage);
