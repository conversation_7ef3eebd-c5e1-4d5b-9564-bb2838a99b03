import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewOpportunityParticipationStatusController from "@src/server/opportunities/ViewOpportunityParticipationStatusController";
import ParticipationsHttpClient from "@src/server/opportunities/ParticipationsHttpClient";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";

describe("ViewOpportunityParticipationStatusController", () => {
  let controller: ViewOpportunityParticipationStatusController;

  beforeEach(() => jest.clearAllMocks());

  it("get participation status for the list of opportunities successfully", async () => {
    const creatorId = "41b16e33";
    const opportunityIds = ["opp1234", "opp456"];
    const participationStatus = {
      participationStatuses: [
        {
          id: "opp1234",
          participationId: "000000000000Xyz",
          invitationId: "000000000000Xyz1",
          status: "JOINED",
          hasChangesRequested: true,
          hasNotSubmittedContent: true
        },
        {
          id: "opp456",
          participationId: "000000000000Abc",
          invitationId: "000000000000Xyz2",
          status: "JOINED",
          hasChangesRequested: true,
          hasNotSubmittedContent: true
        }
      ]
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: `/v3/opportunities/status/creator/${creatorId}`,
      body: opportunityIds,
      session: {
        identity: Identity.fromStored(({
          id: creatorId
        } as unknown) as StoredIdentity)
      }
    });
    const participations = { withSubmissionStatus: jest.fn().mockReturnValueOnce(participationStatus) };
    controller = new ViewOpportunityParticipationStatusController(
      (participations as unknown) as ParticipationsHttpClient
    );

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(participationStatus);
    expect(participations.withSubmissionStatus).toHaveBeenCalledTimes(1);
  });
});
