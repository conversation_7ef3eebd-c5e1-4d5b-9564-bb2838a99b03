import { AxiosResponse } from "axios";
import {
  attendInPersonEvent,
  attendOnlineEvent,
  gameCode,
  getCreatorCode,
  getPaid,
  joinOpportunity,
  makeContent,
  signContract,
  submitContent,
  SvgProps
} from "@eait-playerexp-cn/core-ui-kit";
import ParticipationDetails, { CreatorCode } from "@src/server/opportunities/OpportunityParticipantDetails";
import OpportunityContentSubmissionWithDeliverables from "@src/server/opportunities/OpportunityContentSubmissionWithDeliverables";
import { TimePeriod } from "@src/server/opportunities/OpportunityWithProgramCode";
import { Payment } from "@src/server/opportunities/OpportunityWithPaymentDetails";
import { OpportunityParticipationStatuses } from "@src/server/opportunities/ParticipationsHttpClient";
import { FC } from "react";
import { COMPLETED, INVITED, JOINED, OPEN, PAST } from "../utils";
import { LocalizedDate, LocalizedDateRange } from "@eait-playerexp-cn/client-kernel";
import Perk from "@src/server/perks/Perk";
import PointOfContact from "@src/server/creators/PointOfContact";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import OpportunityCriteria from "@src/actions/Dashboard/OpportunityCriteria";
import ParticipationStatus from "@src/server/opportunities/ParticipationStatus";

type Platform = {
  label: string;
  imageAsIcon: string;
};

type GameCodesAvailability = {
  platform: Platform;
};

type OpportunityGameCode = {
  availability: Array<GameCodesAvailability>;
};

type RemoteEventLabels = {
  description: string;
  eventTime: string;
};

type Country = {
  name: string;
  code: string;
};

type Address = {
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  country: Country;
  venue: string;
};

type Event = {
  address: Address;
  type: string;
  eventPeriod: LocalizedDateRange;
  meetingLink?: string;
  meetingPassword?: string;
};

type RemoteEvent = {
  type: string;
  eventPeriod: LocalizedDateRange;
};

type DateWindow = {
  startDate: string;
  endDate: string;
};

type KeyDate = {
  label: string;
  value: string;
};

type SettingActions = {
  opportunitiesLabels: OpportunityLabelsSettings;
  status: string;
  navigationOptionHandlers: NavigationOptionHandlers;
  loadingStatusHandlers: LoadingStatusHandlers;
  activeStatusHandlers: ActiveStatusHandlers;
  platform?: string;
  participationId?: string;
  hasGameCodesAssigned?: boolean;
};

export type NavigationOptionHandlers = {
  handleContentSubmission: (id: string, hasDeliverables: boolean) => void;
  handleCreatorCode: (id: string) => void;
  handleGameCode: (id: string, participationId: string, hasGameCodesAssigned: boolean) => void;
  handleEventDetails: (id: string) => void;
};

type LoadingStatusHandlers = {
  handleCreatorCode: (id: string) => boolean;
  handleGameCode: (id: string) => boolean;
};

type ActiveStatusHandlers = {
  handleGameCode: (id: string) => boolean;
  handleContentSubmission: (id: string) => boolean;
};

type OpportunityLabelsSettings = {
  contentSubmission: string;
  gameCode: string;
  event: string;
  creatorCodePerk: string;
};

type OpportunityKeyDatesLabels = {
  registrationCloses: string;
  onlineEventStartTime: string;
  inPersonStartTime: string;
  onlineEventEndTime: string;
  inPersonEndTime: string;
  contentSubmissionDeadline: string;
  codeActivationStartTime: string;
  codeActivationEndTime: string;
};

type OpportunitySettingDetailLabels = {
  contentSubmissionWindow: string;
  remoteEvent: {
    eventTime: string;
  };
  activationWindow: string;
};

export class Opportunity {
  static readonly IS_REMOTE = "Remote Event";
  static readonly IS_PHYSICAL = "Physical Presence";
  readonly id: string;
  readonly title: string;
  readonly gameTitle: string;
  readonly hasEvent: boolean;
  readonly hasGameCodes: boolean;
  readonly hasDiscordChannel: boolean;
  readonly gameCodes?: OpportunityGameCode;
  readonly hasDeliverables: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly contentSubmission: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  readonly event?: any;
  readonly registrationPeriod?: LocalizedDateRange;
  readonly hasPayments?: boolean;
  readonly hasAttachments?: boolean;
  readonly attachmentsUrl?: string;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(opportunity: any) {
    Object.assign(this, opportunity);
    this.registrationPeriod = new LocalizedDateRange(
      new LocalizedDate(opportunity.registrationPeriod.startDate),
      new LocalizedDate(opportunity.registrationPeriod.endDate),
      opportunity.registrationPeriod.timeZone
    );
    if (this.event?.eventPeriod) {
      this.event.eventPeriod = new LocalizedDateRange(
        new LocalizedDate(this.event.eventPeriod.startDate),
        new LocalizedDate(this.event.eventPeriod.endDate),
        this.event.eventPeriod.timeZone
      );
    }
    if (this.contentSubmission?.submissionWindow) {
      this.contentSubmission.submissionWindow = new LocalizedDateRange(
        new LocalizedDate(this.contentSubmission.submissionWindow.startDate),
        new LocalizedDate(this.contentSubmission.submissionWindow.endDate),
        this.contentSubmission.submissionWindow.timeZone
      );
    }
  }

  public isCompleted(): boolean {
    const endDates = [];
    endDates.push(!this.registrationPeriod.hasEnded());
    this.hasEvent && endDates.push(!this.event?.eventPeriod?.hasEnded());
    this.hasDeliverables && endDates.push(!this.contentSubmission?.submissionWindow?.hasEnded());
    return !endDates.find((date) => date);
  }

  public whatToDo(whatToDoLabels: Record<string, string>): Array<{ label: string; Icon: FC<SvgProps> }> {
    let whatToDo = [
      {
        label: whatToDoLabels.joinOpportunity,
        Icon: joinOpportunity
      }
    ];
    this.hasPayments &&
      whatToDo.push({
        label: whatToDoLabels.signContract,
        Icon: signContract
      });
    this.hasGameCodes &&
      whatToDo.push({
        label: whatToDoLabels.getGameCode,
        Icon: gameCode
      });
    this.hasEvent &&
      this?.event?.type === OpportunityWithPerks.IS_REMOTE &&
      whatToDo.push({
        label: whatToDoLabels.attendOnlineEvent,
        Icon: attendOnlineEvent
      });

    this.hasEvent &&
      this?.event?.type === OpportunityWithPerks.IS_PHYSICAL &&
      whatToDo.push({
        label: whatToDoLabels.attendInPersonEvent,
        Icon: attendInPersonEvent
      });
    if (this.hasDeliverables) {
      const contentSubmission = [
        {
          label: whatToDoLabels.makeContent,
          Icon: makeContent
        },
        {
          label: whatToDoLabels.submitContent,
          Icon: submitContent
        }
      ];
      whatToDo = [...whatToDo, ...contentSubmission];
    }
    this.hasPayments &&
      whatToDo.push({
        label: whatToDoLabels.getPaid,
        Icon: getPaid
      });

    return whatToDo;
  }

  public codesAvailability(): Array<{ label: string; image: string }> {
    return this.hasGameCodes
      ? this.gameCodes?.availability
          ?.sort((a, b) => (a.platform.label.toLowerCase() > b.platform.label.toLowerCase() ? 1 : -1))
          .map((availability) => {
            return { label: availability.platform.label, image: availability.platform.imageAsIcon };
          }) || []
      : [];
  }

  public isPastOpportunity(): boolean {
    return this.registrationPeriod.hasEnded();
  }

  public settings(opportunitiesLabels: Record<string, string>): string[] {
    const settings: string[] = [];
    this.hasEvent &&
      (this?.event?.type === OpportunityWithPerks.IS_REMOTE
        ? settings.push(opportunitiesLabels.remote)
        : settings.push(opportunitiesLabels.inPersonEvent));
    this.hasGameCodes && settings.push(opportunitiesLabels.gameCode);
    this.hasDeliverables && settings.push(opportunitiesLabels.contentSubmission);
    settings.sort();
    return settings.filter((setting) => setting !== "null");
  }

  public registrationPeriodEnd(locale: string) {
    return `${this.registrationPeriod.end.formatWithTime(
      this.registrationPeriod.timezone,
      locale
    )} (${LocalizedDate.timeZoneAbbreviation()})`;
  }

  public registrationPeriodEndWithDateAndTime(locale: string) {
    return this.registrationPeriod.end.formatWithDateAndTime(this.registrationPeriod.timezone, locale);
  }

  public registrationPeriodHasEnded(): boolean {
    return this.registrationPeriod?.hasEnded();
  }

  public contentSubmissionWindowHasStarted(): boolean {
    return this.contentSubmission?.submissionWindow?.hasStarted();
  }

  public contentSubmissionWindowHasEnded(): boolean {
    return this.contentSubmission?.submissionWindow?.hasEnded();
  }

  public contentSubmissionWindowStart(locale: string) {
    return `${this.contentSubmission.submissionWindow.start.formatWithTime(
      this.contentSubmission.submissionWindow.timezone,
      locale
    )} (${LocalizedDate.timeZoneAbbreviation()})`;
  }

  public contentSubmissionWindowEnd(locale: string) {
    return `${this.contentSubmission.submissionWindow.end.formatWithTime(
      this.contentSubmission.submissionWindow.timezone,
      locale
    )} (${LocalizedDate.timeZoneAbbreviation()})`;
  }

  public isSubmissionWindowClosed(): boolean {
    return this.contentSubmission?.submissionWindow
      ? this.contentSubmission.submissionWindow.hasEnded() || !this.contentSubmission.submissionWindow.hasStarted()
      : false;
  }

  public eventPeriodHasEnded(): boolean {
    return this.event?.eventPeriod?.hasEnded();
  }

  gameCodesPlatforms(): string[] {
    return (
      (this?.hasGameCodes && this?.gameCodes?.availability.map((availability) => availability.platform.label)) || []
    );
  }

  types(): string[] {
    const opportunityTypes = [];

    this.hasDeliverables && opportunityTypes.push("Content Submission");
    this.hasDiscordChannel && opportunityTypes.push("Discord Channel");
    this.hasEvent && this?.event?.type && opportunityTypes.push(this?.event?.type);
    this.hasGameCodes && opportunityTypes.push("Game Codes");
    this.hasPayments && opportunityTypes.push("Payments");

    return opportunityTypes;
  }
}

export class OpportunityWithPerks extends Opportunity {
  readonly perks?: Array<Perk>;
  readonly type?: string;
  readonly visibility?: string;
  readonly hasAttachments?: boolean;
  readonly attachmentsUrl?: string;
  /** Here the event object will have meeting url, meetingPassword & address fields */
  readonly event: RemoteEvent;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(opportunity: any) {
    super(opportunity);
    this.perks = opportunity.perks;
    this.type = opportunity.type;
    this.visibility = opportunity.visibility;
    this.hasAttachments = opportunity.hasAttachments;
    this.attachmentsUrl = opportunity.attachmentsUrl;
    this.event = opportunity.event;
  }

  public isSupportACreatorOpportunity(): boolean {
    return this.type === "support_a_creator";
  }

  public remoteEventDescription(remoteEventLabels: RemoteEventLabels, locale: string): string {
    return super.eventPeriodHasEnded()
      ? remoteEventLabels.description
      : `${remoteEventLabels.eventTime}: ${new LocalizedDate(
          this.event.eventPeriod?.start.millisecondsEpoch
        ).formatWithTime(this.event.eventPeriod?.timezone, locale)} (${LocalizedDate.timeZoneAbbreviation()})`;
  }

  public whatToDo(whatToDoLabels: Record<string, string>): Array<{ label: string; Icon: FC<SvgProps> }> {
    const whatToDo = super.whatToDo(whatToDoLabels);

    this.perks?.find((perk) => perk.code === "CREATOR_CODE") &&
      whatToDo.push({
        label: whatToDoLabels.getCreatorCode,
        Icon: getCreatorCode
      });
    return whatToDo;
  }

  perksCodes(): string {
    return this.perks?.map((perk) => perk.code).join(",") || "None";
  }

  public formattedPerks(perksLabels: Record<string, string>): Array<{ value: string; label: string }> {
    return (
      this.perks
        ?.sort((a, b) => (a.code > b.code ? 1 : -1))
        ?.map?.((perk) => ({
          value: perk.code,
          label: perksLabels[perk.code]
        })) || []
    );
  }

  public settings(opportunitiesLabels: Record<string, string>): string[] {
    const settings = super.settings(opportunitiesLabels);
    if (this.perks?.find((perk) => perk.code === "CREATOR_CODE")) {
      settings.push(opportunitiesLabels.supportACreator);
    }
    return settings.filter((setting) => setting !== "null");
  }
}

export class OpportunityWithDeliverables extends OpportunityWithPerks {
  readonly hasDeliverables: boolean;
  readonly contentSubmission: OpportunityContentSubmissionWithDeliverables;
  readonly pointOfContact: PointOfContact;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  constructor(opportunities: any) {
    super(opportunities);
    this.hasDeliverables = opportunities.hasDeliverables;
    this.contentSubmission = opportunities.contentSubmission;
    this.pointOfContact = opportunities.pointOfContact;
  }
}
export class OpportunityWithDetails extends OpportunityWithDeliverables {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  constructor(opportunities: any) {
    super(opportunities);
  }

  public settings(opportunitiesLabels: Record<string, string>): string[] {
    return super.settings(opportunitiesLabels);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public settingDetails(opportunitiesLabels: any, locale: string, status: string) {
    const details = [];
    if ([INVITED, OPEN].includes(status) && this.registrationPeriod) {
      details.push({
        label: opportunitiesLabels.registrationWindow,
        value: this.registrationPeriod.format(
          locale,
          this.event?.type === OpportunityWithPerks.IS_REMOTE ? null : this.registrationPeriod.timezone
        )
      });
    }
    PAST === status &&
      details.push({
        label: opportunitiesLabels.registrationWindow,
        value: opportunitiesLabels.closed
      });
    return details;
  }
}

export class OpportunityWithProgramCode extends OpportunityWithDetails {
  readonly creatorCodeActivationWindow?: TimePeriod | LocalizedDateRange;

  constructor(opportunities) {
    super(opportunities);
    this.creatorCodeActivationWindow = new LocalizedDateRange(
      new LocalizedDate(opportunities.creatorCodeActivationWindow?.startDate),
      new LocalizedDate(opportunities.creatorCodeActivationWindow?.endDate),
      opportunities.creatorCodeActivationWindow?.timeZone
    );
  }

  public settings(opportunitiesLabels: Record<string, string>): string[] {
    return super.settings(opportunitiesLabels);
  }

  public settingDetails(opportunitiesLabels: OpportunitySettingDetailLabels, locale: string, status: string) {
    let details = [];
    if ([JOINED, COMPLETED].includes(status)) {
      this.hasEvent &&
        details.push({
          label: opportunitiesLabels.remoteEvent.eventTime,
          value: this.event.eventPeriod?.format(
            locale,
            this.event?.type === OpportunityWithPerks.IS_REMOTE ? null : this.event.eventPeriod.timezone
          )
        });
      this.hasDeliverables &&
        details.push({
          label: opportunitiesLabels.contentSubmissionWindow,
          value: ((this.contentSubmission.submissionWindow as unknown) as LocalizedDateRange).format(locale, null)
        });
      this.perks?.find((perk) => perk.code === "CREATOR_CODE") &&
        details.push({
          label: opportunitiesLabels.activationWindow,
          value: (this.creatorCodeActivationWindow as LocalizedDateRange)?.format(
            locale,
            this.creatorCodeActivationWindow?.timezone
          )
        });
    }
    [INVITED, OPEN, PAST].includes(status) && (details = super.settingDetails(opportunitiesLabels, locale, status));
    return details;
  }

  public settingActions(settingActions: SettingActions) {
    const {
      opportunitiesLabels,
      status,
      navigationOptionHandlers,
      loadingStatusHandlers,
      activeStatusHandlers,
      platform,
      participationId,
      hasGameCodesAssigned
    } = settingActions;
    const actions = [];
    if ([JOINED, COMPLETED].includes(status)) {
      this.hasEvent &&
        actions.push({
          type: this.event.type === OpportunityWithPerks.IS_REMOTE ? "ONLINE_EVENT" : "IN_PERSON_EVENT",
          onClick: () => navigationOptionHandlers.handleEventDetails(this.id),
          label: opportunitiesLabels.event
        });
      this.hasGameCodes &&
        actions.push({
          type: "GAME_CODE",
          label: opportunitiesLabels.gameCode,
          onClick: () => navigationOptionHandlers.handleGameCode(this.id, participationId, hasGameCodesAssigned),
          platform,
          isActive: activeStatusHandlers?.handleGameCode(this.id),
          isLoading: loadingStatusHandlers?.handleGameCode(this.id)
        });
      this.hasDeliverables &&
        actions.push({
          type: "CONTENT_SUBMISSION",
          label: opportunitiesLabels.contentSubmission,
          onClick: () => navigationOptionHandlers.handleContentSubmission(this.id, this.hasDeliverables),
          isActive: activeStatusHandlers?.handleContentSubmission?.(this.id)
        });
      this.perks?.find((perk) => perk.code === "CREATOR_CODE") &&
        actions.push({
          type: "CREATOR_CODE",
          label: opportunitiesLabels.creatorCodePerk,
          onClick: () => navigationOptionHandlers?.handleCreatorCode(this.id),
          isLoading: loadingStatusHandlers?.handleCreatorCode(this.id)
        });
    }
    return actions;
  }

  public activationWindow(locale: string): DateWindow {
    return {
      startDate: (this.creatorCodeActivationWindow as LocalizedDateRange)?.formatStartTimeAndLabel(locale),
      endDate: (this.creatorCodeActivationWindow as LocalizedDateRange)?.formatEndTimeAndLabel(locale)
    };
  }

  public settingKeyDates(
    opportunitiesLabels: OpportunityKeyDatesLabels,
    locale: string,
    activationWindow: LocalizedDateRange
  ): Array<KeyDate> {
    const keyDates = [];
    this.registrationPeriod &&
      keyDates.push({
        label: opportunitiesLabels.registrationCloses,
        value: this.registrationPeriod.formatWithEndTimeAndFullMonth(locale, true)
      });
    this.hasEvent &&
      this.event?.eventPeriod &&
      keyDates.push(
        {
          label:
            this.event?.type === OpportunityWithPerks.IS_REMOTE
              ? opportunitiesLabels.onlineEventStartTime
              : opportunitiesLabels.inPersonStartTime,
          value: this.event?.eventPeriod.formatWithStartTimeAndFullMonth(
            locale,
            this.event?.type === OpportunityWithPerks.IS_REMOTE
          )
        },
        {
          label:
            this.event?.type === OpportunityWithPerks.IS_REMOTE
              ? opportunitiesLabels.onlineEventEndTime
              : opportunitiesLabels.inPersonEndTime,
          value: this.event?.eventPeriod.formatWithEndTimeAndFullMonth(
            locale,
            this.event?.type === OpportunityWithPerks.IS_REMOTE
          )
        }
      );
    this.hasDeliverables &&
      keyDates.push({
        label: opportunitiesLabels.contentSubmissionDeadline,
        value: ((this.contentSubmission
          .submissionWindow as unknown) as LocalizedDateRange).formatWithEndTimeAndFullMonth(locale, true)
      });
    this.isSupportACreatorOpportunity() &&
      keyDates.push(
        {
          label: opportunitiesLabels.codeActivationStartTime,
          value: activationWindow?.formatWithStartTimeAndFullMonth(locale, true)
        },
        {
          label: opportunitiesLabels.codeActivationEndTime,
          value: activationWindow?.formatWithEndTimeAndFullMonth(locale, true)
        }
      );
    return keyDates;
  }

  public isInPerson() {
    return this.hasEvent && this.event.type === OpportunityWithPerks.IS_PHYSICAL;
  }

  public eventVenue() {
    return (this?.event as Event)?.address?.venue;
  }

  public eventCountryName() {
    return (this?.event as Event)?.address?.country?.name;
  }

  public eventStreetAddress() {
    return [(this?.event as Event)?.address?.addressLine1, (this?.event as Event)?.address?.addressLine2]
      .filter((street) => street)
      .join(", ");
  }

  public eventAddress() {
    return [
      (this?.event as Event)?.address?.city,
      (this?.event as Event)?.address?.state,
      (this?.event as Event)?.address?.zipCode
    ]
      .filter((address) => address)
      .join(", ");
  }

  public eventWindow(locale: string): DateWindow {
    return {
      startDate: (this.event?.eventPeriod as LocalizedDateRange)?.formatStartTimeAndLabel(locale),
      endDate: (this.event?.eventPeriod as LocalizedDateRange)?.formatEndTimeAndLabel(locale)
    };
  }
}
export class OpportunityWithPaymentDetails extends OpportunityWithProgramCode {
  readonly payment: Payment;

  constructor(opportunities) {
    super(opportunities);
    this.payment = opportunities.payment;
  }

  public getPayment = (salesPercentage: string): string => {
    // Returns payment value with % of Sales for Support a creator opportunities
    // Returns payment value with USD for opportunities
    return this.isSupportACreatorOpportunity()
      ? `${this.payment.percentage}${salesPercentage}`
      : `${new Intl.NumberFormat("en-us", {
          style: "currency",
          currency: "USD"
        }).format(this.payment.amount)} USD`;
  };
}

export class OpportunityParticipantDetails {
  readonly opportunityId: string;
  readonly participationId: string;
  readonly invitationId: string;
  readonly status: string;
  readonly creatorCode: CreatorCode;

  public constructor(participationDetails: ParticipationDetails) {
    Object.assign(this, participationDetails);
    if (participationDetails?.creatorCode?.activationWindow) {
      participationDetails.creatorCode.activationWindow = new LocalizedDateRange(
        new LocalizedDate(participationDetails.creatorCode?.activationWindow.startDate),
        new LocalizedDate(participationDetails.creatorCode?.activationWindow.endDate),
        participationDetails.creatorCode.activationWindow.timeZone
      );
    }
  }

  public creatorCodeHasStarted(): boolean {
    return this.creatorCode.activationWindow?.hasStarted();
  }

  public creatorCodeHasEnded(): boolean {
    return this.creatorCode.activationWindow?.hasEnded();
  }

  public creatorCodeDetail(): string {
    return this.creatorCode.code;
  }

  public creatorCodeActivationWindowStart(locale: string): string {
    return this.creatorCode?.activationWindow?.start?.formatWithEpoch("MMM DD, YYYY", locale);
  }

  public creatorCodeActivationWindowEnd(locale: string): string {
    return this.creatorCode?.activationWindow?.end?.formatWithEpoch("MMM DD, YYYY", locale);
  }
}

export class OpportunityWithActivationWindow extends OpportunityWithDetails {
  readonly creatorCodeActivationWindow?: TimePeriod | LocalizedDateRange;

  constructor(opportunities) {
    super(opportunities);
    this.creatorCodeActivationWindow = new LocalizedDateRange(
      new LocalizedDate(opportunities.creatorCodeActivationWindow?.startDate),
      new LocalizedDate(opportunities.creatorCodeActivationWindow?.endDate),
      opportunities.creatorCodeActivationWindow?.timeZone
    );
  }

  public settings(opportunitiesLabels: Record<string, string>): string[] {
    return super.settings(opportunitiesLabels);
  }

  public settingDetails(opportunitiesLabels: OpportunitySettingDetailLabels, locale: string, status: string) {
    let details = [];
    if ([JOINED, COMPLETED].includes(status)) {
      this.hasEvent &&
        details.push({
          label: opportunitiesLabels.remoteEvent.eventTime,
          value: this.event.eventPeriod?.format(
            locale,
            this.event?.type === OpportunityWithPerks.IS_REMOTE ? null : this.event.eventPeriod.timezone
          )
        });
      this.hasDeliverables &&
        details.push({
          label: opportunitiesLabels.contentSubmissionWindow,
          value: ((this.contentSubmission.submissionWindow as unknown) as LocalizedDateRange).format(locale, null)
        });
      this.perks?.find((perk) => perk.code === "CREATOR_CODE") &&
        details.push({
          label: opportunitiesLabels.activationWindow,
          value: (this.creatorCodeActivationWindow as LocalizedDateRange)?.format(
            locale,
            this.creatorCodeActivationWindow?.timezone
          )
        });
    }
    [INVITED, OPEN, PAST].includes(status) && (details = super.settingDetails(opportunitiesLabels, locale, status));
    return details;
  }

  public settingActions(settingActions: SettingActions) {
    const {
      opportunitiesLabels,
      status,
      navigationOptionHandlers,
      loadingStatusHandlers,
      activeStatusHandlers,
      platform,
      participationId,
      hasGameCodesAssigned
    } = settingActions;
    const actions = [];
    if ([JOINED, COMPLETED].includes(status)) {
      this.hasEvent &&
        actions.push({
          type: this.event.type === OpportunityWithPerks.IS_REMOTE ? "ONLINE_EVENT" : "IN_PERSON_EVENT",
          onClick: () => navigationOptionHandlers.handleEventDetails(this.id),
          label: opportunitiesLabels.event
        });
      this.hasGameCodes &&
        actions.push({
          type: "GAME_CODE",
          label: opportunitiesLabels.gameCode,
          onClick: () => navigationOptionHandlers.handleGameCode(this.id, participationId, hasGameCodesAssigned),
          platform,
          isActive: activeStatusHandlers?.handleGameCode(this.id),
          isLoading: loadingStatusHandlers?.handleGameCode(this.id)
        });
      this.hasDeliverables &&
        actions.push({
          type: "CONTENT_SUBMISSION",
          label: opportunitiesLabels.contentSubmission,
          onClick: () => navigationOptionHandlers.handleContentSubmission(this.id, this.hasDeliverables),
          isActive: activeStatusHandlers?.handleContentSubmission?.(this.id)
        });
      this.perks?.find((perk) => perk.code === "CREATOR_CODE") &&
        actions.push({
          type: "CREATOR_CODE",
          label: opportunitiesLabels.creatorCodePerk,
          onClick: () => navigationOptionHandlers?.handleCreatorCode(this.id),
          isLoading: loadingStatusHandlers?.handleCreatorCode(this.id)
        });
    }
    return actions;
  }

  public activationWindow(locale: string): DateWindow {
    return {
      startDate: (this.creatorCodeActivationWindow as LocalizedDateRange)?.formatStartTimeAndLabel(locale),
      endDate: (this.creatorCodeActivationWindow as LocalizedDateRange)?.formatEndTimeAndLabel(locale)
    };
  }

  public settingKeyDates(
    opportunitiesLabels: OpportunityKeyDatesLabels,
    locale: string,
    activationWindow: LocalizedDateRange
  ): Array<KeyDate> {
    const keyDates = [];
    this.registrationPeriod &&
      keyDates.push({
        label: opportunitiesLabels.registrationCloses,
        value: this.registrationPeriod.formatWithEndTimeAndFullMonth(locale, true)
      });
    this.hasEvent &&
      this.event?.eventPeriod &&
      keyDates.push(
        {
          label:
            this.event?.type === OpportunityWithPerks.IS_REMOTE
              ? opportunitiesLabels.onlineEventStartTime
              : opportunitiesLabels.inPersonStartTime,
          value: this.event?.eventPeriod.formatWithStartTimeAndFullMonth(
            locale,
            this.event?.type === OpportunityWithPerks.IS_REMOTE
          )
        },
        {
          label:
            this.event?.type === OpportunityWithPerks.IS_REMOTE
              ? opportunitiesLabels.onlineEventEndTime
              : opportunitiesLabels.inPersonEndTime,
          value: this.event?.eventPeriod.formatWithEndTimeAndFullMonth(
            locale,
            this.event?.type === OpportunityWithPerks.IS_REMOTE
          )
        }
      );
    this.hasDeliverables &&
      keyDates.push({
        label: opportunitiesLabels.contentSubmissionDeadline,
        value: ((this.contentSubmission
          .submissionWindow as unknown) as LocalizedDateRange).formatWithEndTimeAndFullMonth(locale, true)
      });
    this.isSupportACreatorOpportunity() &&
      keyDates.push(
        {
          label: opportunitiesLabels.codeActivationStartTime,
          value: activationWindow?.formatWithStartTimeAndFullMonth(locale, true)
        },
        {
          label: opportunitiesLabels.codeActivationEndTime,
          value: activationWindow?.formatWithEndTimeAndFullMonth(locale, true)
        }
      );
    return keyDates;
  }

  public isInPerson() {
    return this.hasEvent && this.event.type === OpportunityWithPerks.IS_PHYSICAL;
  }

  public eventVenue() {
    return (this?.event as Event)?.address?.venue;
  }

  public eventCountryName() {
    return (this?.event as Event)?.address?.country?.name;
  }

  public eventStreetAddress() {
    return [(this?.event as Event)?.address?.addressLine1, (this?.event as Event)?.address?.addressLine2]
      .filter((street) => street)
      .join(", ");
  }

  public eventAddress() {
    return [
      (this?.event as Event)?.address?.city,
      (this?.event as Event)?.address?.state,
      (this?.event as Event)?.address?.zipCode
    ]
      .filter((address) => address)
      .join(", ");
  }

  public eventWindow(locale: string): DateWindow {
    return {
      startDate: (this.event?.eventPeriod as LocalizedDateRange)?.formatStartTimeAndLabel(locale),
      endDate: (this.event?.eventPeriod as LocalizedDateRange)?.formatEndTimeAndLabel(locale)
    };
  }
}

export default class OpportunityService {
  constructor(private readonly client: TraceableHttpClient) {}

  async declineInvitation(invitationId: string): Promise<AxiosResponse<void>> {
    return (await this.client.post(`/api/declined-invitations/${invitationId}`)) as AxiosResponse<void>;
  }

  async getParticipationDetails(opportunityId: string): Promise<AxiosResponse<Array<OpportunityParticipantDetails>>> {
    return this.client
      .post("/api/opportunities/participation-details", { body: { opportunityId } })
      .then((response: AxiosResponse) => {
        response.data[0] = new OpportunityParticipantDetails(response.data[0]);
        return response;
      });
  }

  async getOpportunityWithPaymentDetails(id: string): Promise<AxiosResponse<OpportunityWithPaymentDetails>> {
    return this.client.get(`/api/v10/opportunities/${id}`).then((response: AxiosResponse) => {
      response.data.opportunity = new OpportunityWithPaymentDetails(response.data.opportunity);
      return response;
    });
  }

  async matchingWithProgramDetails(criteria: OpportunityCriteria): Promise<AxiosResponse<OpportunityWithProgramCode>> {
    return this.client.get(`/api/v4/dashboard`, { query: criteria }).then((response: AxiosResponse) => {
      response.data.opportunities = response.data.opportunities.map(
        (opportunity: OpportunityWithProgramCode) => new OpportunityWithProgramCode(opportunity)
      );
      return response;
    });
  }

  async getParticipationStatusWithSubmissionInformation(
    opportunityIds: Array<string>
  ): Promise<AxiosResponse<OpportunityParticipationStatuses[]>> {
    const response = (await this.client.post("/api/opportunities/participation-status", {
      body: { opportunityIds }
    })) as AxiosResponse;
    response.data = response.data.participationStatuses;
    return response;
  }

  async searchOpportunities(criteria): Promise<AxiosResponse<OpportunityParticipationStatuses[]>> {
    const response = (await this.client.post(`/api/opportunities`, { body: criteria })) as AxiosResponse;
    response.data.opportunities = response.data.opportunities.map((opportunity: OpportunityWithActivationWindow) => {
      return new OpportunityWithActivationWindow(opportunity);
    });
    return response;
  }

  async getOpportunitiesParticipationStatus(criteria): Promise<AxiosResponse<ParticipationStatus>> {
    return (await this.client.post("/api/opportunities/participation-status", {
      body: criteria
    })) as AxiosResponse<ParticipationStatus>;
  }

  async getOpportunityWithDeliverables(id: string): Promise<AxiosResponse<OpportunityWithDeliverables>> {
    return this.client.get(`/api/v8/opportunities/${id}`).then((response: AxiosResponse) => {
      response.data.opportunity = new OpportunityWithDeliverables(response.data.opportunity);
      return response;
    });
  }
}
