import { createContext, ReactN<PERSON>, useContext, useEffect, useReducer } from "react";
import {
  ACTIVE_CONTENT_SUBMISSION,
  ACTIVE_GAME_CODE,
  CLICKED_DELIVERABLE_ID,
  COMPLETED_ONBOARDING_STEPS,
  COMPLETED_OPPORTUNITY_STEPS,
  CURRENT_DELIVERABLE_PAGE,
  DOMAIN_ERROR,
  ERROR,
  GET_PLATFORMS,
  HAS_EXCEPTION,
  JOIN_OPPORTUNITY_FORM,
  JOIN_OPPORTUNITY_STEPS,
  ONBOARDING_STEPS,
  OPPORTUNITY,
  OPPOR<PERSON>NITY_STATUS,
  PARTICIPATION,
  PARTICIPATION_STATUS,
  <PERSON><PERSON><PERSON>_OPENED,
  setC<PERSON><PERSON>,
  SHOW_WARNING_FOR_CHANGES_REQUESTED,
  TOAST_ERROR,
  USER_NAVIGATED,
  VALIDATION_ERROR
} from "@src/utils";
import { useRouter } from "next/router";
import { Action, Dispatch, ValidationError } from "@src/utils/types";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";

const SESSION_USER: string = "SESSION_USER";
const LOADING: string = "LOADING";
const GET_FB_PAGES: string = "GET_FB_PAGES";
const SHOW_FACEBOOK_PAGES: string = "SHOW_FACEBOOK_PAGES";
const RELOAD_INTERESTED_CREATOR_ACCOUNTS: string = "RELOAD_INTERESTED_CREATOR_ACCOUNTS";

//------------------------
// Global Constants
//------------------------

interface State {
  appName: string;
  onboardingSteps: [];
  userNavigated: boolean;
  exceptionCode?: number;
  sessionUser: AuthenticatedUser | null;
  isLoading: boolean;
  isValidationError: boolean;
  validationErrors: ValidationError[];
  isError: boolean;
  domainError: Error | null;
  isCompleted: boolean;
  popupOpened: boolean;
  getFbPages: boolean;
  showFacebookPages?: boolean;
  reloadInterestedCreatorAccounts: boolean;
  onToastCloseAction: (() => void) | null;
  isToastError: boolean;
  creatorCodeDetails: null;
  platformDetails: [];
  activeGameCode: null;
  activeContentSubmission: null;
  showWarningForChangesRequested: boolean;
  currentDeliverablePage: Record<string, unknown>;
  joinOpportunitySteps: [];
  opportunityStatus: null | Record<string, unknown>;
  participationStatus: null | Record<string, unknown>;
  opportunity: null | Record<string, unknown>;
  joinOpportunityFormData: Record<string, unknown>;
  participation: null | Record<string, unknown>;
  clickedDeliverableId: string;
}

interface AppContextProps {
  state: State;
  dispatch: Dispatch;
}

export const AppContext = createContext<AppContextProps>({ state: {} as State, dispatch: () => null });

const initialState: State = {
  appName: "Creator Applications",
  onboardingSteps: [],
  userNavigated: false,
  exceptionCode: null,
  sessionUser: null,
  isLoading: false,
  isValidationError: false,
  validationErrors: [],
  joinOpportunitySteps: [],
  opportunityStatus: null,
  participationStatus: null,
  participation: null,
  joinOpportunityFormData: {},
  opportunity: null,
  isError: false,
  domainError: null,
  isCompleted: false,
  popupOpened: false,
  getFbPages: false,
  showFacebookPages: false,
  reloadInterestedCreatorAccounts: false,
  onToastCloseAction: null,
  isToastError: false,
  creatorCodeDetails: null,
  platformDetails: [],
  activeGameCode: null,
  activeContentSubmission: null,
  showWarningForChangesRequested: false,
  currentDeliverablePage: null,
  clickedDeliverableId: ""
};

//------------------------
// Reducer
//------------------------
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case POPUP_OPENED:
      return {
        ...state,
        popupOpened: action.data as boolean
      };
    case ONBOARDING_STEPS:
      return {
        ...state,
        onboardingSteps: action.data as []
      };
    case COMPLETED_ONBOARDING_STEPS: {
      const { onboardingSteps } = state;
      const updatedOnboardingSteps = onboardingSteps.map((step: Record<string, string>) => {
        if (step.title === (action.data as { currentStep: Record<string, unknown> }).currentStep.title) {
          return {
            ...step,
            isCompleted: true
          };
        }
        return step;
      });
      return {
        ...state,
        onboardingSteps: updatedOnboardingSteps as []
      };
    }
    case USER_NAVIGATED:
      return {
        ...state,
        userNavigated: action.data as boolean
      };
    case CLICKED_DELIVERABLE_ID:
      return {
        ...state,
        clickedDeliverableId: action.data as string
      };
    case PARTICIPATION:
      return {
        ...state,
        participation: action.data as Record<string, unknown>
      };
    case JOIN_OPPORTUNITY_FORM:
      return {
        ...state,
        joinOpportunityFormData:
          action.data === null ? {} : { ...state.joinOpportunityFormData, ...(action.data as Record<string, unknown>) }
      };
    case OPPORTUNITY:
      return {
        ...state,
        opportunity: action.data as Record<string, unknown>
      };
    case PARTICIPATION_STATUS:
      return {
        ...state,
        participationStatus: action.data as Record<string, unknown>
      };
    case OPPORTUNITY_STATUS:
      return {
        ...state,
        opportunityStatus: action.data as Record<string, unknown>
      };
    case HAS_EXCEPTION:
      return {
        ...state,
        exceptionCode: action.data as number
      };
    case JOIN_OPPORTUNITY_STEPS:
      return {
        ...state,
        joinOpportunitySteps: action.data as []
      };
    case COMPLETED_OPPORTUNITY_STEPS:
      const { joinOpportunitySteps } = state;
      const updatedOpportunitySteps = joinOpportunitySteps.map((step: { title: string }) => {
        if (step.title === (action.data as { currentStep: Record<string, unknown> }).currentStep.title) {
          return {
            ...step,
            isCompleted: true
          };
        }
        return step;
      });
      return {
        ...state,
        joinOpportunitySteps: updatedOpportunitySteps as []
      };
    case SHOW_WARNING_FOR_CHANGES_REQUESTED:
      return {
        ...state,
        showWarningForChangesRequested: action.data as boolean
      };
    case TOAST_ERROR:
      return {
        ...state,
        onToastCloseAction: ((action as unknown) as { onToastCloseAction: () => void })?.onToastCloseAction,
        isToastError: true
      };
    case CURRENT_DELIVERABLE_PAGE:
      return {
        ...state,
        currentDeliverablePage: action.data as Record<string, unknown>
      };
    case SESSION_USER:
      return {
        ...state,
        sessionUser: action.data as AuthenticatedUser
      };
    case LOADING:
      return {
        ...state,
        isLoading: action.data as boolean
      };
    case VALIDATION_ERROR:
      const newState = { ...state };
      if (Array.isArray(action.data)) {
        console.log({ action });
        newState.validationErrors = action.data as ValidationError[];
        newState.isValidationError = true;
      } else {
        newState.isValidationError = action.data as boolean;
      }
      return newState;
    case GET_FB_PAGES:
      return {
        ...state,
        getFbPages: action.data as boolean
      };
    case DOMAIN_ERROR:
      return {
        ...state,
        domainError: action.data as Error | null
      };
    case SHOW_FACEBOOK_PAGES:
      return {
        ...state,
        showFacebookPages: action.data as boolean
      };
    case RELOAD_INTERESTED_CREATOR_ACCOUNTS:
      return {
        ...state,
        reloadInterestedCreatorAccounts: action.data as boolean
      };
    case ERROR:
      return {
        ...state,
        isError: action.data as boolean
      };
    case GET_PLATFORMS:
      return {
        ...state,
        platformDetails: action.data as []
      };
    case ACTIVE_GAME_CODE:
      return {
        ...state,
        activeGameCode: action.data as null
      };
    case ACTIVE_CONTENT_SUBMISSION:
      return {
        ...state,
        activeContentSubmission: action.data as null
      };
    default:
      return state;
  }
};

interface AppWrapperProps {
  children: ReactNode;
}

export function AppWrapper({ children }: AppWrapperProps): JSX.Element {
  const [state, dispatch] = useReducer(reducer, initialState);
  const router = useRouter();

  useEffect(() => {
    const { locale } = router;
    setCookie(locale);
  }, [router]);

  return <AppContext.Provider value={{ state, dispatch }}>{children}</AppContext.Provider>;
}

export function useAppContext() {
  return useContext(AppContext);
}
