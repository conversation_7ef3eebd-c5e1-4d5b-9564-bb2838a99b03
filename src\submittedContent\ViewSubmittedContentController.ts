import { NextApiResponse } from "next";
import { Service } from "typedi";
import SubmittedContentHttpClient, { SubmittedContentCriteria } from "../submittedContent/SubmittedContentHttpClient";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ViewSubmittedContentController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly contents: SubmittedContentHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const criteria = (req.query as unknown) as SubmittedContentCriteria;
    criteria.creatorId = this.identity(req).id;
    criteria.program = config.PROGRAM_CODE;
    const submittedContents = await this.contents.getSubmittedContentsWithProgramCode(criteria);
    this.json(res, submittedContents);
  }
}

export default ViewSubmittedContentController;
