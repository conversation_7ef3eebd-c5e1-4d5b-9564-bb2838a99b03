import "reflect-metadata";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { render, screen } from "@testing-library/react";
import { axe } from "jest-axe";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";
import { useAppContext } from "@src/context";
import { mockMatchMedia } from "__tests__/helpers/window";
import Article from "@src/pages/articles/[...slug]";
import { Random } from "@eait-playerexp-cn/onboarding-ui";
import { ArticleType } from "@components/ArticlePage/ArticlePage";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";

jest.mock("@src/context/DependencyContext");
jest.mock("@src/context", () => ({
  ...jest.requireActual("@src/context"),
  useAppContext: jest.fn()
}));

describe("Article Slug", () => {
  mockMatchMedia();
  const router = {
    push: jest.fn(),
    locale: "en-us",
    pathname: "/documentation"
  };
  const user = AuthenticatedUserFactory.fromIdentity(Identity.fromStored(aStoredIdentity()), "creator_network", "");
  const article = {
    sys: {
      id: "123",
      publishedAt: "2024-01-01",
      firstPublishedAt: "2024-01-01",
      publishedVersion: 1
    },
    title: "Test Article",
    slug: "test-article",
    body: {
      richText: '{"nodeType":"document","content":[]}',
      embeddedItems: []
    },
    entryType: "Article" as ArticleType
  };
  const articleProps = {
    article,
    slug: ["test-article"],
    user: null,
    locale: "en-US"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: { sessionUser: user }
    });
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  it("displays article content in Article page", () => {
    render(<Article {...articleProps} />);

    expect(screen.getByTestId("article-page")).toBeInTheDocument();
  });

  it("displays Nav Header in Article page", () => {
    render(<Article {...articleProps} />);

    expect(screen.getByTestId("header-container")).toBeInTheDocument();
  });

  it("displays footer in Article page", () => {
    render(<Article {...articleProps} />);

    expect(screen.getByTestId("footer-mobile")).toBeInTheDocument();
  });

  it("shows side navigation when user is authenticated", () => {
    const articlePropsWithUser = {
      ...articleProps,
      user: {
        analyticsId: Random.uuid(),
        username: Random.string()
      }
    };

    render(<Article {...articlePropsWithUser} />);

    expect(screen.getByTestId("layout-body")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<Article {...articleProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
