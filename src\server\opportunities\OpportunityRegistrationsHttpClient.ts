import { Inject, Service } from "typedi";
import OpportunityRegistrations from "./OpportunityRegistrations";
import Participation from "./Participation";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import JoinOpportunityInput from "@src/server/JoinOpportunity/JoinOpportunityInput";

@Service()
export default class OpportunityRegistrationsHttpClient implements OpportunityRegistrations {
  constructor(@Inject("opportunityClient") private client: TraceableHttpClient) {}

  async saveParticipation(input: JoinOpportunityInput): Promise<Participation> {
    const response = await this.client.post(`/v1/opportunities/${input.id}/participations`, {
      body: input.criteria()
    });
    return Participation.fromApi(response.data);
  }
}
