{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "This config was generated using 'stryker init'. Please see the guide for more information: https://stryker-mutator.io/docs/stryker-js/guides/react", "testRunner": "jest", "allowEmpty": true, "incremental": true, "reporters": ["progress", "clear-text", "html"], "coverageAnalysis": "perTest", "jest": {"projectType": "custom", "configFile": "jest.config.js", "enableFindRelatedTests": false}, "ignoreStatic": true, "mutate": ["src/pages/index.tsx", "src/components/**", "src/middleware/**", "src/server/**", "src/services/**", "src/utils/**"], "ignorePatterns": ["styles/**", "stories", "src/pages/api/**", "src/instrumentation*", "/src/**/*.css"], "thresholds": {"high": 90, "low": 70, "break": 28}, "mutator": {"plugins": [], "excludedMutations": ["ObjectLiteral"]}, "checkers": ["typescript"], "tsconfigFile": "tsconfig.json", "concurrency": 5, "timeoutMS": 120000, "tempDirName": "strykerTmp", "cleanTempDir": "always"}