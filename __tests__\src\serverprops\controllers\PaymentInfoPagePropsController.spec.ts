import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import PaymentInfoPagePropsController from "@src/serverprops/controllers/PaymentInfoPagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");

describe("PaymentInfoPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the payment info page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    req.session = { identity };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {
      header: { title: "Payment Information" },
      breadcrumbs: { dashboard: "Dashboard", paymentInfo: "Payment Information" }
    };
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new PaymentInfoPagePropsController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "paymentInformation");
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels: labels,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        }
      }
    });
  });
});
