import "reflect-metadata";
import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Registrations from "@src/pages/opportunities/[id]/registrations";
import OpportunityService from "@src/services/OpportunityService";
import OperationsService from "@src/services/OperationsService";
import { mockMatchMedia } from "../../../../helpers/window";
import { useRouter } from "next/router";
import { renderPage } from "../../../../helpers/page";
import { aCreator } from "../../../../factories/creators/Creator";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../../factories/opportunities/OpportunityWithPerks";
import OpportunityWithDeliverables from "@src/server/opportunities/OpportunityWithDeliverables";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../../../src/utils", () => ({
  ...jest.requireActual("../../../../../src/utils"),
  useDetectScreen: jest.fn().mockImplementation((width) => width === 1279)
}));
jest.mock("../../../../../src/services/OperationsService");
jest.mock("../../../../../src/services/OpportunityService");
jest.mock("../../../../../src/context/DependencyContext");

describe("Registrations", () => {
  mockMatchMedia();
  const opportunity = OpportunityWithDeliverables.fromApi(
    aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      hasGameCodes: true,
      id: "OPPO124"
    })
  );
  const router = { query: { id: "OPPO124" }, locale: "en-us" };
  const getParticipationStatusWithSubmissionInformation = jest.fn().mockResolvedValue({
    data: []
  });
  const opportunityService = {
    getParticipationStatusWithSubmissionInformation,
    getParticipationStatusWithSubmissionInformation,
    getOpportunityWithDeliverables: jest.fn(),
    getOpportunitiesParticipationWithInvitationStatus: jest.fn().mockResolvedValue({})
  };

  const operationsService = {
    saveParticipation: jest.fn(),
    viewAssignedGameCodes: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    OpportunityService.mockReturnValue(opportunityService);
    OperationsService.mockReturnValue(operationsService);
    useDependency.mockReturnValue({ errorHandler: jest.fn() });
  });

  it("displays title in Join Opportunity Criteria page", async () => {
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });

    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
      />
    );

    await waitFor(() => expect(screen.getAllByText(/criteria-opportunity:message/i)).toHaveLength(1));
  });

  it("logs 'Started Join Opportunity Flow' event after accepting the opportunity criteria", async () => {
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    operationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    const analytics = { startedJoinOpportunityFlow: jest.fn() };
    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
        analytics={analytics}
      />
    );
    await waitFor(() => expect(screen.getAllByText(/criteria-opportunity:message/i)).toHaveLength(1));
    expect(await screen.findByRole("button", { name: "next" }, { timeout: 1_100 })).toBeDisabled();
    const checkboxInput = screen.getByRole("checkbox");
    // Accept opportunity criteria
    await userEvent.click(checkboxInput);
    await waitFor(async () => {
      expect(checkboxInput).toBeChecked();
      expect(await screen.findByRole("button", { name: "next" })).toBeEnabled();
    });

    await userEvent.click(await screen.findByRole("button", { name: "next" }));

    await waitFor(() => {
      expect(analytics.startedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.startedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
    });
  });

  it("logs 'Completed Join Opportunity Flow' event after accepting the opportunity criteria", async () => {
    const opportunity = aOpportunityWithPerksAndContentSubmissionWithDeliverables({
      hasGameCodes: false,
      hasDeliverables: false
    });
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    operationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    const analytics = { completedJoinOpportunityFlow: jest.fn() };
    renderPage(
      <Registrations
        user={{ id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" }}
        WATERMARKS_URL="/"
        analytics={analytics}
      />
    );
    await waitFor(() => expect(screen.getAllByText(/criteria-opportunity:message/i)).toHaveLength(1));
    const joinButton = await screen.findByRole("button", { name: "join" });
    expect(joinButton).toBeDisabled();
    const checkboxInput = screen.getByRole("checkbox");
    // Accept opportunity criteria
    await userEvent.click(checkboxInput);
    await waitFor(() => {
      expect(checkboxInput).toBeChecked();
      expect(joinButton).toBeEnabled();
    });

    await userEvent.click(joinButton);

    await waitFor(() => {
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledWith({
        locale: "en-us",
        opportunity
      });
      expect(operationsService.saveParticipation).toHaveBeenCalledTimes(1);
    });
  });
});
