import "reflect-metadata";
import Layout, { LayoutBody, LayoutHeader } from "@components/Layout";
import { GetServerSidePropsContextWithSession } from "@src/middleware/types";
import { GetServerSideProps, GetServerSidePropsResult } from "next";
import React from "react";
import { NoAccountPageLabels } from "@src/server/contentManagement/NoAccountPageMapper";
import BrowserAnalytics, { AuthenticatedUser, InitialInterestedCreator } from "@src/analytics/BrowserAnalytics";
import Image from "next/image";
import TopNavBar from "@components/header/TopNavBar";
import { useRouter } from "next/router";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import noAccountPageProps from "@src/serverprops/NoAccountPageProps";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";

export type NoAccount = {
  runtimeConfiguration?: Record<string, unknown>;
  interestedCreator: InitialInterestedCreator;
  locale: string;
  pageLabels: NoAccountPageLabels;
  analytics?: BrowserAnalytics;
  user?: AuthenticatedUser;
  showInitialMessage?: boolean;
};
export default function NoAccount({ pageLabels, interestedCreator }: NoAccount): JSX.Element {
  const { noAccountLabels } = pageLabels;
  const { locale } = useRouter();

  return (
    <Layout>
      <LayoutHeader pageTitle={noAccountLabels.title} tabTitle={noAccountLabels.title}>
        <TopNavBar {...{ locale, labels: { topNavigation: "Top Navigation" } }} />
      </LayoutHeader>
      <LayoutBody className="interested-creator-layout">
        <div className="age-restriction-container">
          <div className="age-restriction-bg"></div>
          <div className="age-restriction-header">
            <div className="sims-creator-logo">
              <Image
                src="/img/sims-logo.svg"
                alt="The Sims Creator Program"
                width={175}
                height={42}
                className="scp__characters-image"
                priority
                data-testid="the-sims-creator-program"
              />
            </div>
          </div>
          <div className="age-restriction-main-content">
            <div className="age-restriction-character-section">
              <Image
                src="/img/frontify-separating-rows.svg"
                alt="Sims Characters"
                width={344}
                height={293}
                className="age-restriction-characters"
                data-testid="sims-characters"
              />
            </div>
            <div className="horizontal-line top-line"></div>
            <div className="age-restriction-content-container">
              <h1 className="age-restriction-title">{noAccountLabels.title}</h1>
              <div className="age-restriction-body">{`${noAccountLabels.subTitlePart1} ${
                interestedCreator?.originEmail || ""
              }. ${noAccountLabels.subTitlePart2}`}</div>
            </div>
            <div className="horizontal-line bottom-line"></div>
          </div>
        </div>
      </LayoutBody>
    </Layout>
  );
}

type NoAccountPage = {
  locale: string;
  interestedCreator: InitialInterestedCreator;
};

export const getServerSideProps: GetServerSideProps<NoAccountPage> = async ({
  req,
  res,
  locale
}: GetServerSidePropsContextWithSession) => {
  const router = createRouter();
  router.use(errorLogger).use(initializeSession).use(addIdentityTelemetryAttributes).get(noAccountPageProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<NoAccount>;
};
