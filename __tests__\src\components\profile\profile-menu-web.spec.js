import { render, screen } from "@testing-library/react";
import { aCreatorWithFlaggedStatus } from "../../../factories/creators/CreatorWithFlaggedStatus";
import ProfileMenuWeb from "../../../../src/components/profile/ProfileMenuWeb";
import { profileLabels } from "../../../translations";
import { useRouter } from "next/router";
import { anAccountInformationWithFlaggedStatus } from "../../../factories/creators/AccountInformationWithFlaggedStatus";

jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

xdescribe("Profile Menu Web", () => {
  const profileMenuWebProps = {
    pocLabels: { title: "Send Email" },
    profileLabels: profileLabels.default
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => ({
      query: { section: "information" },
      locale: "en-us",
      push: jest.fn()
    }));
  });

  it("displays point of contact section for a non-flagged creator", async () => {
    const creator = aCreatorWithFlaggedStatus({
      accountInformation: anAccountInformationWithFlaggedStatus({ isFlagged: false })
    });

    render(<ProfileMenuWeb {...profileMenuWebProps} creator={creator} />);

    expect(screen.getByText(profileMenuWebProps.profileLabels.pointOfContact)).toBeInTheDocument();
    expect(screen.getByText(creator.pocDiscordTag)).toBeInTheDocument();
  });

  it("does not show point of contact section for a flagged creator", async () => {
    const creator = aCreatorWithFlaggedStatus({
      accountInformation: anAccountInformationWithFlaggedStatus({ isFlagged: true })
    });

    render(<ProfileMenuWeb {...profileMenuWebProps} creator={creator} />);

    expect(screen.queryByText(profileMenuWebProps.profileLabels.pointOfContact)).not.toBeInTheDocument();
  });
});
