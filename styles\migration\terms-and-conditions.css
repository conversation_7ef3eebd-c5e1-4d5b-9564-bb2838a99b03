.terms-and-conditions {
  @apply m-meas8 flex max-w-full flex-col items-center;
}

.terms-and-conditions .input-box {
  @apply border-none;
}

.terms-and-conditions input[type="text"] {
  @apply w-full cardSmall:w-[316px] xl:w-[319px];
}
.terms-and-conditions .terms-condition-address > .address-cont > .select-box {
  @apply w-[90%] max-w-[356px] cardSmall:w-[316px] xl:w-[319px];
}

.terms-and-conditions .select-header-title,
.select-header-label {
  @apply w-full;
}
.terms-and-conditions .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}

.terms-and-conditions > main {
  @apply flex max-w-full flex-col items-center justify-center;
}

.terms-and-conditions > main > header {
  @apply flex flex-col items-center justify-center md:pb-meas24;
}

.terms-and-conditions > main > header > h3 {
  @apply pb-meas12 text-center font-display-regular text-mobile-h1 font-bold text-gray-10 md:text-desktop-h3;
}

.terms-and-conditions > main > header > p {
  @apply text-center font-text-regular text-desktop-body-default xs:max-w-[333px] md:max-w-[700px];
}

.terms-and-conditions .pactsafe {
  @apply flex max-w-full justify-center xs:min-w-full md:lg:mt-meas5;
}
.terms-and-conditions .pactsafe > form {
  @apply w-full max-w-full;
}

.terms-and-conditions .pactsafe > .pactSafe-iframe-cont {
  @apply flex flex-col items-center xs:min-w-full md:min-w-[auto] xl:items-end;
}

.terms-and-conditions .pactsafe > .pactSafe-iframe-cont > hr {
  @apply w-[100%];
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}
.terms-and-conditions .pactsafe > .pactSafe-iframe-cont > iframe {
  @apply my-meas26 min-h-[461px] min-w-full md:min-h-[436px] md:min-w-[641px] xl:min-h-[695px] xl:min-w-[850px];
}

.terms-and-conditions form .input-box-label {
  @apply mr-meas16 text-gray-10;
}

.terms-and-conditions form .input-box-label-disabled {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.terms-and-conditions form label.form-input-box {
  @apply flex w-[90%] items-center;
}

.input-help-text {
  @apply w-[90%];
}
.terms-and-conditions form .terms-condition-personal label.form-input-box {
  @apply mb-meas12 items-start md:mb-meas0;
}

.terms-and-conditions > main > section.pactsafe > form {
  @apply py-meas16;
}

.terms-and-conditions .pact-safe-entity-form input:disabled {
  @apply text-ellipsis pl-meas0 text-gray-10;
  background-color: transparent;
}

.terms-and-conditions .pact-safe-entity-form .input-container:hover {
  background-color: transparent;
}

.terms-and-conditions .pact-safe-entity-form .input-box:hover {
  background: none;
}
.terms-and-conditions .pact-safe-entity-form .terms-condition-entity {
  @apply py-meas16 pl-meas16 md:py-meas24;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.terms-and-conditions .pact-safe-entity-form .personal-info-address-container {
  @apply border-white border-opacity-[0.3] pl-meas16 xs:pl-meas0 md:border-t md:pl-meas16;
}
.terms-and-conditions .pact-safe-entity-form .terms-condition-entity .form-radio-field {
  @apply w-full flex-row;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-entity .form-radio-field .radio-container {
  @apply grid grid-cols-2 gap-meas26 md:w-full md:flex-row md:justify-between;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-entity fieldset {
  @apply mt-meas20;
}

.terms-condition-entity fieldset {
  @apply flex flex-col font-text-regular;
}

.terms-condition-entity fieldset > legend {
  @apply font-bold xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.terms-condition-entity aside {
  @apply mt-meas8 flex justify-between md:w-[96%];
}

.terms-condition-entity > p {
  @apply font-text-regular text-gray-10 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.terms-condition-personal {
  @apply md:pt-meas26;
}
.terms-condition-personal .personal-input-cont {
  @apply flex justify-start md:pb-meas12;
}

.terms-condition-personal .personal-input-cont > label {
  @apply mb-meas12 flex w-[90%] flex-col items-start md:w-[auto];
}

.terms-condition-personal .personal-input-cont > label:first-child {
  @apply md:mr-meas24;
}

.terms-condition-personal .input-box.input-box-disabled input {
  @apply p-meas0;
  background: none;
}

.terms-condition-entity .radio-container label {
  @apply flex flex-row;
}

.terms-and-conditions form .terms-condition-address .checkbox-label-container {
  @apply flex-row;
}

.terms-condition-address > .legal-entity-field:not(.asMailingAddress) ~ div > .input-box-label {
  @apply flex flex-col items-start pb-meas12;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > .input-box {
  @apply flex flex-col md:flex-row;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > .input-box-label {
  @apply w-[210px] font-bold xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > .form-input-box input {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.terms-condition-address > div.address-cont {
  @apply mt-meas10 flex flex-col md:mt-meas16 md:grid;
}
@media screen and (min-width: 768px) {
  .terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont:not(.business),
  .terms-condition-address > .legal-entity-field:not(.asMailingAddress) ~ div.address-cont {
    grid-template-columns: 50% 50%;
    display: grid;
  }
  .terms-condition-address > .legal-entity-field.asMailingAddress ~ div.address-cont {
    grid-template-columns: 100%;
  }
}
@media screen and (min-width: 1024px) {
  .terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont:not(.business),
  .terms-condition-address > .legal-entity-field:not(.asMailingAddress) ~ div.address-cont {
    display: grid;
    grid-template-columns: 45% 45%;
    gap: 2rem;
  }
}

.terms-condition-address > .legal-entity-field.asMailingAddress ~ div.address-cont {
  grid-template-columns: 100%;
}

.terms-and-conditions .pact-safe-entity-form .input-box:not(.input-box-disabled) {
  @apply w-full;
}

.terms-and-conditions .pact-safe-entity-form .input-box.input-box-disabled {
  border: none;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont {
  @apply flex flex-col md:flex-row;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont.business {
  @apply flex-col md:items-start;
}

.terms-and-conditions .pact-safe-entity-form .terms-condition-personal .personal-input-cont.business .business-caption {
  @apply mb-meas8 w-[90%] font-text-regular xs:text-mobile-caption1 md:w-[316px] md:text-tablet-caption1 lg:text-desktop-caption1 xl:w-[319px];
}

.terms-and-conditions .pact-safe-entity-form .input-box {
  @apply md:w-auto md:min-w-full;
}
.terms-and-conditions form .terms-condition-address label {
  @apply w-[90%] flex-col pb-meas8 md:w-auto;
}

.terms-condition-address > .address-cont > .select-box {
  @apply pb-meas16;
}

.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > label {
  @apply flex w-full flex-col items-start md:w-[65%] md:flex-row md:items-center md:justify-between;
}
.terms-condition-address > .legal-entity-field.asMailingAddress ~ div > label .input-box {
  @apply h-meas12 max-w-[319px] p-meas0 font-text-regular;
  min-width: auto;
}
.terms-condition-address {
  @apply md:mt-meas20;
}
.pact-safe-entity-form {
  @apply max-w-full md:mb-[70px] xl:mb-[120px];
}
.terms-and-condition-title {
  @apply font-display-regular font-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3;
}
.terms-and-conditions-description {
  @apply font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.terms-and-conditions-decline {
  @apply mt-meas4 font-display-regular text-gray-10 xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
  background: transparent;
}
