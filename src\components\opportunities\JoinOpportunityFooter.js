import React, { memo, useCallback } from "react";
import { Button, Icon, leftArrow, rightArrow } from "@eait-playerexp-cn/core-ui-kit";
import { useFormContext } from "react-hook-form";
import { useRouter } from "next/router";

export default memo(function JoinOpportunityFooter({ button, back, prev, layout, pending }) {
  const methods = useFormContext();
  const router = useRouter();
  const onClickPrevious = useCallback(() => router.push(prev), [prev, router]);

  return (
    <div className="join-opportunity-footer-container">
      <div className="join-opportunity-footer">
        {prev && (
          <Button dark variant="tertiary" size="md" onClick={onClickPrevious}>
            <Icon icon={leftArrow} className="join-opportunity-back" />
            <span>{back}</span>
          </Button>
        )}
        <Button
          variant="primary"
          size="md"
          type="submit"
          spinner={pending}
          disabled={
            pending ||
            (button === layout?.buttons.join
              ? false
              : Object.keys(methods.formState.errors).length !== 0 || methods.formState.isValid === false)
          }
        >
          <span>{button}</span>
          <Icon icon={rightArrow} className="join-opportunity-submit-next" />
        </Button>
      </div>
    </div>
  );
});
