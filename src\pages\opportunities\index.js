import "reflect-metadata";
import { useTranslation } from "next-i18next";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import labelsCommon from "../../config/translations/common";
import labelsOpportunities from "../../config/translations/opportunities";
import { useRouter } from "next/router";
import {
  ACTIVE_CONTENT_SUBMISSION,
  ACTIVE_GAME_CODE,
  CREATOR_CODE_DETAILS,
  ERROR,
  GET_PLATFORMS,
  onToastClose,
  SESSION_USER,
  toastContent,
  useDetectScreen,
  useUpdateEffect,
  VALIDATION_ERROR
} from "../../utils";
import Error from "../_error";
import Loading from "../../components/Loading";
import { LOADING } from "../../utils";
import { OpportunityCardV2, Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import labelsDashboard from "../../config/translations/dashboard";
import { claimGameCode, getActiveStatus, getPlatforms, mapGameCodeToOpportunity } from "../dashboard";
import { formatEventAddress, formatEventWindow, formatRemoteEvent } from "../dashboard";
import Layout, { LayoutBody, LayoutFooter, LayoutHeader } from "../../components/Layout";
import Footer from "../../components/footer/ProgramFooter";
import { COMPLETED, DECLINED, INVITED, JOINED, OPEN } from "../../utils/constants";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";
import Header from "@components/header/header";
import MorePerksModal from "@components/morePerksModal/MorePerksModal";
import SupportACreatorModal from "@components/supportACreatorModal/SupportACreatorModal";
import GameCodeModal from "@components/gameCodeModal/GameCodeModal";
import EventDetailsModal from "@components/eventDetailsModal/EventDetailsModal";
import { useAppContext } from "@src/context";
import OpportunityService from "@src/services/OpportunityService";
import Pagination from "@components/pagination/Pagination";
import { mapNotificationsBellLabels } from "@src/config/translations/mappers/notifications";
import OperationsService from "../../services/OperationsService";
import { createRouter } from "next-connect";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import verifyAccessToProgram from "@src/serverprops/middleware/VerifyAccessToProgram";
import opportunitiesProps from "@src/serverprops/OpportunitiesProps";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";

const PAGE_SIZE = 10;
const DEFAULT_PAGE_NUMBER = 1;

export default function Opportunities({ user, analytics = new BrowserAnalytics(user) }) {
  const { metadataClient, errorHandler, client } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);
  const isMobile = useDetectScreen(767);
  const router = useRouter();
  const {
    dispatch,
    state: {
      exceptionCode = null,
      sessionUser = null,
      isLoading,
      isError,
      isValidationError,
      creatorCodeDetails,
      platformDetails,
      activeGameCode,
      activeContentSubmission
    } = {}
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const [pages, setPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(null);
  const [currentPageOpportunities, setCurrentPageOpportunities] = useState(null);
  const [opportunitiesList, setOpportunitiesList] = useState({});
  const [showMorePerks, setShowMorePerks] = useState(null);
  const [showSupportACreatorModal, setShowSupportACreatorModal] = useState(null);
  const [showEventDetailsModal, setShowEventDetailsModal] = useState(null);
  const [creatorCodeLoading, setCreatorCodeLoading] = useState(null);
  const [showGameCodeModal, setShowGameCodeModal] = useState(null);
  const [gameCodeLoading, setGameCodeLoading] = useState(null);
  const { t } = useTranslation(["common", "dashboard", "notifications", "connect-accounts", "opportunities"]);
  const { layout, opportunitiesLabels, dashboardLabels, notificationsLabels } = useMemo(() => {
    const notificationBellLabels = mapNotificationsBellLabels(t);
    const labels = {
      opportunitiesLabels: labelsOpportunities(t),
      layout: { ...labelsCommon(t), footer: { locale: router.locale, labels: labelsCommon(t).footer } },
      dashboardLabels: labelsDashboard(t),
      notificationsLabels: notificationBellLabels
    };
    return labels;
  }, [t]);
  const statusLabel = {
    [INVITED]: dashboardLabels.invited,
    [OPEN]: dashboardLabels.open,
    [DECLINED]: opportunitiesLabels.declined,
    [JOINED]: opportunitiesLabels.joined
  };
  const {
    main: { unhandledError }
  } = layout;

  const {
    requestToJoin,
    logIn,
    signIn,
    home,
    faqs,
    dashboard,
    myProfile,
    signout,
    notifications,
    opportunities,
    myContent,
    documentation
  } = layout.header;
  const {
    how,
    reward,
    perks,
    faq,
    policies,
    legal,
    disclaimer,
    updates,
    terms,
    privacy,
    rights,
    report,
    disclosure,
    policy
  } = layout.footer.labels;

  const labels = {
    commonPageLabels: {
      requestToJoin,
      logIn,
      signIn,
      home,
      faqs,
      dashboard,
      myProfile,
      signout,
      how,
      reward,
      perks,
      faq,
      policies,
      legal,
      disclaimer,
      updates,
      terms,
      privacy,
      rights,
      report,
      disclosure,
      policy,
      notifications,
      opportunities,
      myContent,
      documentation
    },
    notificationsBellLabels: notificationsLabels
  };

  const footerLabels = {
    commonPageLabels: {
      dashboard: dashboard,
      opportunities: opportunities,
      myContent: myContent,
      documentation: documentation,
      faq: faq,
      legal: legal,
      disclaimer: disclaimer,
      updates: updates,
      terms: terms,
      privacy: privacy,
      rights: rights,
      report: report,
      faqs: faqs,
      disclosure: disclosure,
      policy: policy,
      policies: policies
    }
  };

  const opportunityService = useMemo(() => new OpportunityService(client), [client]);
  const operationsService = useMemo(() => new OperationsService(client), [client]);

  const searchOpportunities = useCallback(() => {
    let reqBody = {
      page: DEFAULT_PAGE_NUMBER,
      size: PAGE_SIZE
    };
    stableDispatch({ type: LOADING, data: true });
    opportunityService
      .searchOpportunities(reqBody)
      .then((res) => {
        if (res.data && res.data.total > 0) {
          let pages = [];
          for (let i = 0; i < Math.ceil(res.data.total / PAGE_SIZE); i++) {
            pages.push(i + 1);
          }
          getOpportunityStatus(pages, DEFAULT_PAGE_NUMBER, res.data.opportunities, platformDetails);
        } else {
          if (res.data && res.data.total === 0) {
            setOpportunitiesList({ [DEFAULT_PAGE_NUMBER]: res.data.opportunities });
            setCurrentPageOpportunities(null);
            setPages(null);
          }
          stableDispatch({ type: LOADING, data: false });
        }
      })
      .catch((e) => {
        stableDispatch({ type: LOADING, data: false });
        errorHandler(stableDispatch, e);
      });
  }, []);

  useUpdateEffect(() => {
    searchOpportunities();
  }, []);

  useEffect(() => {
    async function fetchOpportunities() {
      if (!router.isReady) return;
      let currentPage = DEFAULT_PAGE_NUMBER;
      if (router.query && router.query.page && parseInt(router.query.page) > 0) {
        currentPage = parseInt(router.query.page);
      }
      const platforms = platformDetails.length
        ? platformDetails
        : await getPlatforms(metadataService, stableDispatch, errorHandler);
      stableDispatch({ type: GET_PLATFORMS, data: platforms });

      let criteria = {
        page: currentPage,
        size: PAGE_SIZE
      };
      stableDispatch({ type: LOADING, data: true });
      opportunityService
        .searchOpportunities(criteria)
        .then((res) => {
          if (res.data && res.data.total > 0) {
            let pages = [];
            for (let i = 0; i < Math.ceil(res.data.total / PAGE_SIZE); i++) {
              pages.push(i + 1);
            }
            getOpportunityStatus(pages, currentPage, res.data.opportunities, platforms);
          } else {
            stableDispatch({ type: LOADING, data: false });
          }
        })
        .catch((e) => {
          stableDispatch({ type: LOADING, data: false });
          errorHandler(stableDispatch, e);
        });
    }
    fetchOpportunities();
  }, [router.isReady, stableDispatch]);

  useEffect(() => {
    if (currentPage > 0) {
      if (opportunitiesList[currentPage] && opportunitiesList[currentPage].length > 0) {
        setCurrentPageOpportunities(opportunitiesList[currentPage]);
      } else {
        let criteria = {
          page: currentPage,
          size: PAGE_SIZE
        };
        stableDispatch({ type: LOADING, data: true });
        opportunityService
          .searchOpportunities(criteria)
          .then((res) => {
            if (res.data && res.status === 200 && res.data.total > 0) {
              getOpportunityStatus(pages, currentPage, res.data.opportunities, platformDetails);
            }
          })
          .catch((e) => {
            stableDispatch({ type: LOADING, data: false });
            errorHandler(stableDispatch, e);
          });
      }
    }
  }, [currentPage, stableDispatch]);

  const getOpportunityStatus = useCallback(
    async (pages, page, opportunitiesTemp, platforms) => {
      let opportunitiesIds = [];
      opportunitiesTemp.forEach((element) => {
        opportunitiesIds.push(element.id);
      });
      if (opportunitiesIds.length > 0) {
        await opportunityService
          .getParticipationStatusWithSubmissionInformation(opportunitiesIds)
          .then(async (res) => {
            const participationIds = [];
            let contentSubmissionActiveStatus = activeContentSubmission; // storing the data from context to a variable before looping through to avoid data loss
            if (res.data && res.data.length > 0) {
              for (let i = 0; i < opportunitiesTemp.length; i++) {
                opportunitiesTemp[i].status = res.data[i].status;
                opportunitiesTemp[i].participationId = res.data[i].participationId; // added participationId to an opportunity to match with respective game codes eg: opportunity.participationId === gameCode.participationId
                res.data[i].participationId &&
                  opportunitiesTemp[i].hasGameCodes &&
                  participationIds.push(opportunitiesTemp[i].participationId); // form the list of participationIds ['id1', "id2"] and use it for getting game code details
              }
              const filterJoinedGameCodeOpportunities = opportunitiesTemp.filter(
                (opportunity) => opportunity.status === JOINED && opportunity.hasGameCodes
              );
              if (filterJoinedGameCodeOpportunities.length > 0 && participationIds.length > 0) {
                opportunitiesTemp = await mapGameCodeToOpportunity(
                  {
                    opportunities: opportunitiesTemp,
                    participationIds,
                    platforms,
                    dispatch: stableDispatch
                  },
                  errorHandler,
                  operationsService
                );
              }
              setOpportunitiesList({ ...opportunitiesList, [page]: opportunitiesTemp });
              opportunitiesTemp.map((opportunity, index) => {
                if (res.data[index].status === JOINED) {
                  opportunity["status"] = JOINED;
                  opportunity.isCompleted() && (opportunity["status"] = COMPLETED);
                  opportunity.hasChangesRequested = res.data[index].hasChangesRequested;
                  opportunity.hasNotSubmittedContent = res.data[index].hasNotSubmittedContent;
                  contentSubmissionActiveStatus = {
                    ...contentSubmissionActiveStatus,
                    [opportunity.id]: getActiveStatus(
                      opportunity,
                      opportunity.hasChangesRequested,
                      opportunity.hasNotSubmittedContent
                    )
                  };
                } else if (res.data[index].status === INVITED) {
                  opportunity["status"] = INVITED;
                } else if (res.data[index].status === "NA") {
                  opportunity["status"] = OPEN;
                }
              });
              stableDispatch({ type: ACTIVE_CONTENT_SUBMISSION, data: contentSubmissionActiveStatus });
              setCurrentPageOpportunities(opportunitiesTemp);
              setPages(pages);
              setCurrentPage(page);
            }
            stableDispatch({ type: LOADING, data: false });
          })
          .catch((e) => {
            stableDispatch({ type: LOADING, data: false });
            errorHandler(stableDispatch, e);
          });
      }
    },
    [stableDispatch]
  );

  useEffect(() => {
    if (user) stableDispatch({ type: SESSION_USER, data: user });
  }, [user, stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(isValidationError)}
          closeButtonAriaLabel={layout.buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, unhandledError, stableDispatch]);

  const modalLabels = {
    title: dashboardLabels.allPerks,
    close: layout.buttons.close
  };

  const handleContentSubmission = (opportunitiesId) => {
    router.push(`/opportunities/${opportunitiesId}?tab=content-deliverables`);
  };

  const handleCreatorCode = async (opportunityId) => {
    if (creatorCodeDetails?.[opportunityId]) {
      setShowSupportACreatorModal({ [opportunityId]: true });
      return;
    }
    setCreatorCodeLoading({ [opportunityId]: true });
    try {
      const response = await opportunityService.getParticipationDetails(opportunityId);
      const opportunityCreatorCodeDetails = response.data.find((opportunity) => opportunity); // Unpack first element from the array response
      dispatch({ type: CREATOR_CODE_DETAILS, data: { [opportunityId]: opportunityCreatorCodeDetails } });
      setCreatorCodeLoading({ [opportunityId]: false });
      setShowSupportACreatorModal({ [opportunityId]: true });
    } catch (e) {
      setCreatorCodeLoading({ [opportunityId]: false });
      errorHandler(dispatch, e);
    }
  };

  const handleGameCode = async (opportunityId, participationId, hasGameCodesAssigned) => {
    if (hasGameCodesAssigned) {
      setGameCodeLoading({ [opportunityId]: true });
      await claimGameCode(participationId, stableDispatch, errorHandler, operationsService);
      setGameCodeLoading({ [opportunityId]: false });
      dispatch({ type: ACTIVE_GAME_CODE, data: { ...activeGameCode, [opportunityId]: false } });
    }
    setShowGameCodeModal({ [opportunityId]: true });
  };

  const handleEventDetails = (opportunityId) => {
    currentPageOpportunities.find((opportunity) => opportunity.id === opportunityId);
    setShowEventDetailsModal({ [opportunityId]: true });
  };

  const navigationOptionHandlers = {
    handleContentSubmission,
    handleCreatorCode,
    handleGameCode,
    handleEventDetails
  };

  const activeStatusHandlers = {
    handleGameCode: (opportunityId) => activeGameCode?.[opportunityId] ?? false,
    handleContentSubmission: (opportunityId) => activeContentSubmission?.[opportunityId] ?? false
  };

  const loadingStatusHandlers = {
    handleCreatorCode: (opportunityId) => creatorCodeLoading?.[opportunityId] ?? false,
    handleGameCode: (opportunityId) => gameCodeLoading?.[opportunityId] ?? false
  };

  const formatCodeActivationWindow = (codeWindow) => {
    return [
      {
        label: opportunitiesLabels.creatorCode.codeActivationStartTime,
        value: codeWindow.startDate
      },
      {
        label: opportunitiesLabels.creatorCode.codeActivationEndTime,
        value: codeWindow.endDate
      }
    ];
  };

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  return (
    <Layout>
      <LayoutHeader
        pageTitle={layout.header.opportunities}
        tabTitle={`${layout.theSims} | ${layout.header.opportunities}`}
      >
        <Header labels={labels} user={user} />
      </LayoutHeader>
      <LayoutBody showSideNavigation={!!user} className="opportunities-container">
        <div className="opportunities-full-screen">
          <h3 className="opportunities-title">{layout.header.opportunitiesTitle}</h3>
          <div className="opportunities-sub-title">{opportunitiesLabels.description}</div>
          <div className={"opportunities-list-container"}>
            {(isLoading && <Loading />) ||
              (currentPageOpportunities ? (
                <div className="opportunities-list-with-perks opportunities-list" data-testid="opportunities-list">
                  {currentPageOpportunities.map((opportunity, index) => {
                    return (
                      [INVITED, OPEN, DECLINED, JOINED].includes(opportunity.status) && (
                        <Fragment key={`opportunity-card-${index}`}>
                          <OpportunityCardV2
                            title={opportunity.title}
                            description={opportunity.description}
                            opportunityId={opportunity.id}
                            heroImage={opportunity.heroImage}
                            perksTitle={opportunitiesLabels.perks}
                            perks={
                              opportunity.status === JOINED
                                ? []
                                : opportunity.formattedPerks(opportunitiesLabels.perksLabels)
                            }
                            settings={opportunity.settings(opportunitiesLabels)}
                            href={`/opportunities/${opportunity.id}`}
                            morePerksLabel={opportunitiesLabels.more}
                            pillStatus={{ status: opportunity.status, label: statusLabel[opportunity.status] }}
                            handleCardClick={() => router.push(`/opportunities/${opportunity.id}`)}
                            details={opportunity.settingDetails(opportunitiesLabels, router.locale, opportunity.status)}
                            handleMoreButtonClick={() => setShowMorePerks({ [index]: true })}
                            settingsAction={opportunity?.settingActions({
                              opportunitiesLabels,
                              status: opportunity.status,
                              navigationOptionHandlers,
                              loadingStatusHandlers,
                              activeStatusHandlers,
                              platform: opportunity?.gameCode?.platform,
                              participationId: opportunity?.participationId,
                              hasGameCodesAssigned: opportunity?.gameCode?.status === "ASSIGNED"
                            })}
                          />
                          {[INVITED, OPEN, DECLINED].includes(opportunity.status) && showMorePerks?.[index] && (
                            <MorePerksModal
                              labels={modalLabels}
                              onClose={() => setShowMorePerks({ [index]: false })}
                              perks={opportunity.formattedPerks(opportunitiesLabels.perksLabels)}
                            />
                          )}
                          {[JOINED].includes(opportunity.status) && showSupportACreatorModal?.[opportunity.id] && (
                            <SupportACreatorModal
                              labels={{
                                title: opportunitiesLabels.creatorCode.title,
                                close: modalLabels.close,
                                copied: opportunitiesLabels.copied,
                                copyText: opportunitiesLabels.creatorCode.copyText
                              }}
                              onClose={() => setShowSupportACreatorModal({ [opportunity.id]: false })}
                              codeWindow={formatCodeActivationWindow(opportunity.activationWindow(router.locale))}
                              gameTitle={opportunity.gameTitle}
                              code={creatorCodeDetails?.[opportunity.id]?.creatorCode?.code}
                            />
                          )}
                          {[JOINED].includes(opportunity.status) && showGameCodeModal?.[opportunity.id] && (
                            <GameCodeModal
                              labels={{
                                title: opportunitiesLabels.getGameCode,
                                close: modalLabels.close,
                                copied: opportunitiesLabels.copied,
                                copyText: opportunitiesLabels.getGameCode
                              }}
                              onClose={() => setShowGameCodeModal({ [opportunity.id]: false })}
                              platform={opportunity?.gameCode?.platform}
                              gameTitle={opportunity.gameTitle}
                              code={opportunity?.gameCode?.code || opportunitiesLabels.checkBackSoon}
                              hasCode={!!opportunity?.gameCode?.code}
                            />
                          )}

                          {[JOINED].includes(opportunity.status) && showEventDetailsModal?.[opportunity.id] && (
                            <EventDetailsModal
                              labels={{
                                title: opportunity.isInPerson()
                                  ? opportunitiesLabels.inPersonEvent
                                  : opportunitiesLabels.remote,
                                joinEvent: opportunitiesLabels.remoteEvent.joinEvent,
                                close: modalLabels.close,
                                copied: opportunitiesLabels.copied,
                                copy: opportunity.isInPerson()
                                  ? opportunitiesLabels.eventDetails.copyAddress
                                  : opportunitiesLabels.eventDetails.copyPassword
                              }}
                              onClose={() => setShowEventDetailsModal({ [opportunity.id]: false })}
                              eventWindow={formatEventWindow(
                                opportunity.eventWindow(router.locale),
                                opportunitiesLabels
                              )}
                              eventAddress={
                                opportunity.isInPerson() && formatEventAddress(opportunity, opportunitiesLabels)
                              }
                              isInPerson={opportunity.isInPerson()}
                              eventPeriodEnded={
                                opportunity?.eventPeriodHasEnded()
                                  ? {
                                      label: opportunitiesLabels.eventDetails.info,
                                      value: opportunitiesLabels.remoteEvent.description
                                    }
                                  : null
                              }
                              remoteEventDetails={
                                !opportunity.isInPerson() && formatRemoteEvent(opportunity, opportunitiesLabels)
                              }
                              meetingLink={!opportunity.isInPerson() && opportunity.event.meetingLink}
                              isMeetingLinkDisabled={
                                (!opportunity.event.meetingLink && !opportunity.event.meetingPassword) ||
                                opportunity?.eventPeriodHasEnded()
                              }
                            />
                          )}
                        </Fragment>
                      )
                    );
                  })}
                </div>
              ) : (
                <div className="opportunities-no-content">
                  <div className="opportunities-no-content-row1">
                    <img src="/img/dashboard/light-bulb.svg" alt="icon" className="no-opportunities-icon" />
                    <div>
                      <div className="opportunities-no-content-title">
                        {opportunitiesLabels.noAvailableOpportunities}
                      </div>
                      <div className="opportunities-no-content-desc">
                        {opportunitiesLabels.noAvailableOpportunitiesDesc}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </div>

          {pages && pages.length > 1 && (
            <div className="dashboard-pagination-container">
              <Pagination
                next={layout.buttons.next}
                prev={layout.buttons.prev}
                pages={pages}
                currentPage={currentPage}
                onPageChange={(page) => setCurrentPage(page)}
              />
            </div>
          )}
        </div>
      </LayoutBody>
      <LayoutFooter>
        <Footer locale={router.locale} labels={footerLabels} analytics={analytics} />
      </LayoutFooter>
    </Layout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();
  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(verifyAccessToProgram)
    .use(addLocaleCookie(locale))
    .get(opportunitiesProps(locale));

  return await router.run(req, res);
};
