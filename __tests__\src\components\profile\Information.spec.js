import { act, screen, waitFor, within } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Information from "../../../../src/components/profile/Information";
import { aUser } from "../../../factories/User/User";
import { aCreatorWithPayableStatus } from "../../../factories/creators/CreatorWithPayableStatus";
import { useRouter } from "next/router";
import { anAccountInformationWithPayableStatus } from "../../../factories/creators/AccountInformationWithPayableStatus";
import { mockMatchMedia } from "../../../helpers/window";
import { clearValueFor, enterValueFor, selectOption } from "../../../helpers/forms";
import { aMailingAddress } from "../../../factories/creators/MailingAddress";
import { aAdditionalInformation } from "../../../factories/creators/AdditionalInformation";
import { aLegalEntity } from "../../../factories/creators/LegalEntity";
import { useAppContext } from "../../../../src/context";
import { renderWithToast, triggerAnimationEnd } from "../../../helpers/toast";
import { commonTranslations as layout } from "../../../translations";
import { onToastClose } from "../../../../src/utils";
import CreatorsService, { CreatorWithPayableStatusProfile } from "../../../../src/services/CreatorsService";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { aCountry, aHardwarePartner } from "@eait-playerexp-cn/metadata-test-fixtures";
import { useDependency } from "../../../../src/context/DependencyContext";
import { axe } from "jest-axe";

jest.mock("../../../../src/context", () => ({
  ...jest.requireActual("../../../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));
jest.mock("../../../../src/utils", () => ({
  ...jest.requireActual("../../../../src/utils"),
  onToastClose: jest.fn()
}));
jest.mock("../../../../src/services/CreatorsService", () => {
  return {
    ...jest.requireActual("../../../../src/services/CreatorsService"),
    update: jest.fn(),
    getCreatorWithPayableStatus: jest.fn()
  };
});
jest.mock("../../../../src/context/DependencyContext");

xdescribe("Information", () => {
  mockMatchMedia();
  let creator;
  const analytics = { updatedBasicInformation: jest.fn() };
  const creatorCountryCode = "MX";
  const infoLabels = {
    title: "Start Building your Profile",
    infoTitle: "Your Information",
    primaryPlatform: "Primary Platform",
    platformPreferences: "Platform Preferences",
    secondaryPlatforms: "Secondary Platform",
    personalInformation: "Personal Information",
    mailingAddress: "Mailing Address",
    miscellaneous: "Miscellaneous",
    legalEntityType: "Legal Entity & Address",
    success: {
      updatedInformationHeader: "Information update successful",
      personalInformation: "You have successfully updated your Personal Information.",
      mailingAddress: "You have successfully updated your Mailing Address.",
      miscellaneous: "You have successfully updated your Miscellaneous Information.",
      legalEntityType: "You have successfully updated your Legal Entity Information.",
      platformPreferences: "You have successfully updated your Platform Preferences."
    },
    labels: {
      firstName: "First Name",
      street: "Street",
      city: "City",
      tShirtSize: "t-shirt size"
    },
    messages: {},
    profilePicture: {
      title: "Change My Avatar"
    },
    info: {
      businessName: "Registered Entity name"
    },
    header: {
      calendar: "Calendar"
    },
    profileLabels: {
      updateAvatar: "Update Avatar"
    }
  };
  const informationProps = {
    infoLabels,
    buttons: { edit: "Edit", save: "Save" },
    user: aUser(),
    updateCreator: jest.fn(),
    hardwarePartners: [aHardwarePartner({ name: "Nvidia" }), aHardwarePartner()],
    countries: [aCountry({ code: creatorCountryCode })],
    allCountries: [aCountry(), aCountry(), aCountry()],
    layout: {
      main: {}
    },
    analytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => ({ locale: "en-us" }));
    creator = new CreatorWithPayableStatusProfile(
      aCreatorWithPayableStatus({
        accountInformation: anAccountInformationWithPayableStatus({
          registrationDate: LocalizedDate.fromFormattedDate("2020-12-31").formatWithEpoch("MM/DD/YYYY"),
          dateOfBirth: LocalizedDate.fromFormattedDate("1969-12-31").formatWithEpoch("MM/DD/YYYY")
        }),
        mailingAddress: aMailingAddress({ country: aCountry({ code: creatorCountryCode }) }),
        additionalInformation: aAdditionalInformation({ hoodieSize: "L" }),
        legalInformation: aLegalEntity()
      })
    );
    informationProps.creator = creator;
    CreatorsService.getCreatorWithPayableStatus.mockResolvedValue({
      data: creator
    });
    useDependency.mockReturnValue({
      errorHandler: jest.fn(),
      configuration: {}
    });
  });

  it("logs 'Update Basic Information' event when creator updates their basic information", async () => {
    CreatorsService.update.mockImplementation(() => Promise.resolve());
    const { unmount } = renderWithToast(<Information {...informationProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[0]);
    const saveButton = await waitFor(expectSaveButton);
    await clearValueFor(/^First Name/i);
    await enterValueFor(/^First Name/i, "Jane");

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedBasicInformation).toHaveBeenCalledTimes(1);
      expect(analytics.updatedBasicInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("logs 'Update Basic Information' event when creator updates their mailing address", async () => {
    CreatorsService.update.mockResolvedValue({
      data: creator
    });
    const { unmount } = renderWithToast(<Information {...informationProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[1]);
    const saveButton = await waitFor(expectSaveButton);
    await clearValueFor(/^Street/i);
    await enterValueFor(/^Street/i, "Main St. 123");

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedBasicInformation).toHaveBeenCalledTimes(1);
      expect(analytics.updatedBasicInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("logs 'Update Basic Information' event when creator updates their legal entity", async () => {
    CreatorsService.update.mockResolvedValue({
      data: creator
    });
    const { unmount } = renderWithToast(<Information {...informationProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[2]);
    const saveButton = await waitFor(expectSaveButton);
    await clearValueFor(/^City/i);
    await enterValueFor(/^City/i, "San Antonio");

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedBasicInformation).toHaveBeenCalledTimes(1);
      expect(analytics.updatedBasicInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("logs 'Update Basic Information' event when creator updates their miscellaneous information", async () => {
    CreatorsService.update.mockResolvedValue({
      data: creator
    });
    const { unmount, container } = renderWithToast(<Information {...informationProps} />);
    const editButtons = await waitFor(expect4EditButtons);
    await userEvent.click(editButtons[3]);
    const saveButton = await waitFor(expectSaveButton);
    await selectOption({ option: "M", container, label: infoLabels.labels.tShirtSize });

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedBasicInformation).toHaveBeenCalledTimes(1);
      expect(analytics.updatedBasicInformation).toHaveBeenCalledWith({ locale: "en-us" });
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
    });
    unmount(); // Remove toast
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage }
    });
    CreatorsService.update.mockResolvedValue({
      data: creator
    });
    const { unmount } = renderWithToast(<Information {...informationProps} layout={layout.default} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong.");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(screen.getByText("Oops! Something has gone wrong."));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderWithToast(<Information {...informationProps} layout={layout.default} />);
    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  function expect4EditButtons() {
    const editButtons = screen.getAllByRole("button", { name: "Edit" });
    expect(editButtons).toHaveLength(4);
    return editButtons;
  }

  function expectSaveButton() {
    const saveButton = screen.getByRole("button", { name: "Save" });
    expect(saveButton).toBeEnabled();
    return saveButton;
  }
});
