import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser, AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { FacebookPage, FacebookPages } from "@src/server/channels/ConnectedAccountsHttpClient";
import { OAuthError } from "@src/server/channels/tiktok/ConnectTikTokAccountController";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export type ConnectAccountsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user?: AuthenticatedUser;
  pages: FacebookPage[];
  invalidTikTokScope: boolean;
  error: OAuthError | null;
};

export default class ConnectAccountsPagePropController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<ConnectAccountsProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<ConnectAccountsProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    const { pages = [] } = this.hasSession(req, "fbPages") ? (this.session(req, "fbPages") as FacebookPages) : {};
    const error = this.hasSession(req, "error") ? (this.session(req, "error") as OAuthError) : null;
    const invalidTikTokScope = this.hasSession(req, "INVALID_TIKTOK_SCOPE")
      ? (this.session(req, "INVALID_TIKTOK_SCOPE") as boolean)
      : false;

    await this.removeFromSession(req, "error");
    await this.removeFromSession(req, "INVALID_TIKTOK_SCOPE");
    await this.removeFromSession(req, `${this.program}.futureCreator`);

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        pages,
        error,
        invalidTikTokScope,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "breadcrumb",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
