import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { screen } from "@testing-library/react";
import { axe } from "jest-axe";
import FAQ from "@src/pages/faq";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import { useDependency } from "@src/context/DependencyContext";
import { mockMatchMedia } from "__tests__/helpers/window";
import { renderPage } from "__tests__/helpers/page";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";

jest.mock("@src/context", () => ({
  ...jest.requireActual("@src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("@src/context/DependencyContext");

describe("FAQ", () => {
  mockMatchMedia();
  const router = {
    push: jest.fn(),
    locale: "en-us"
  };
  const faqProps = {
    user: AuthenticatedUserFactory.fromIdentity(Identity.fromStored(aStoredIdentity()), "creator_network", ""),
    locale: "en-us",
    faqPage: {
      title: "FAQ",
      body: {
        richText: '{"nodeType":"document","content":[]}',
        embeddedItems: []
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  it("shows the FAQ page content", () => {
    renderPage(<FAQ {...faqProps} />);

    expect(screen.getByTestId("faq-page")).toBeInTheDocument();
  });

  it("dispatches the session user to the context when user is provided", () => {
    const mockDispatch = jest.fn();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: mockDispatch,
      state: {}
    });

    renderPage(<FAQ {...faqProps} />);

    expect(mockDispatch).toHaveBeenCalledWith({
      type: "SESSION_USER",
      data: faqProps.user
    });
  });

  it("shows error page when exception code exists in state", () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: jest.fn(),
      state: { exceptionCode: 404 }
    });

    renderPage(<FAQ {...faqProps} />);

    expect(screen.queryByText("FAQ")).not.toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = renderPage(<FAQ {...faqProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
