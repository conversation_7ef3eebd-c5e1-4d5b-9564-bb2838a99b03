import "reflect-metadata";
import { screen, waitFor, within } from "@testing-library/react";
import Registrations from "../../../../../src/pages/opportunities/[id]/registrations";
import userEvent from "@testing-library/user-event";
import { aOpportunityWithPerksAndContentSubmissionWithDeliverables } from "../../../../factories/opportunities/OpportunityWithPerks";
import OpportunityService from "@src/services/OpportunityService";
import OperationsService from "@src/services/OperationsService";
import { mockMatchMedia } from "../../../../helpers/window";
import { renderPage } from "../../../../helpers/page";
import { useRouter } from "next/router";
import { ampli } from "../../../../../analytics/browser/src/ampli";
import { useDetectScreen } from "../../../../../src/utils";
import "next/config";
import { aCreator } from "../../../../factories/creators/Creator";
import OpportunityWithDeliverables from "../../../../../src/server/opportunities/OpportunityWithDeliverables";
import { useDependency } from "@src/context/DependencyContext";
import { errorHandlerFactory } from "../../../../../src/shared/errorHandling/errorHandler";

jest.mock("../../../../../src/services/OpportunityService");
jest.mock("../../../../../src/services/OperationsService");
jest.mock("../../../../../src/utils", () => ({
  ...jest.requireActual("../../../../../src/utils"),
  useIsMounted: jest.fn().mockImplementation(() => jest.fn().mockReturnValue(true)),
  useDetectScreen: jest.fn().mockImplementation((width) => width === MD_SIZE)
}));
jest.mock("../../../../../analytics/browser/src/ampli", () => ({
  ampli: { identify: jest.fn(), receivedErrorMessage: jest.fn() }
}));
jest.mock("next/config");
jest.mock("../../../../../src/context/DependencyContext");

const MD_SIZE = 1279;
const LG_SIZE = 10000;
const withGameCodeGuidelineEnabled = [
  ["Criteria", "", "/opportunities/OPPO123"],
  ["Game Code", "game-code", "/opportunities/OPPO123/registrations"],
  ["Content Guidelines", "content-guidelines", "/opportunities/OPPO123/registrations?step=game-code"]
];
const withContentGuidelineEnabled = [
  ["Criteria", "", "/opportunities/OPPO123"],
  ["Content Guidelines", "content-guidelines", "/opportunities/OPPO123/registrations"]
];
const withGameCodeEnabled = [
  ["Criteria", "", "/opportunities/OPPO123"],
  ["Game Code", "game-code", "/opportunities/OPPO123/registrations"]
];

describe("Registrations", () => {
  mockMatchMedia();
  const analytics = {};
  const router = {
    query: { id: "OPPO123", step: "content-guidelines" },
    pathname: "/opportunities/[id]/registrations",
    locale: "en-us",
    push: jest.fn()
  };
  const opportunityService = {
    getOpportunityWithDeliverables: jest.fn(),
    getOpportunitiesParticipationStatus: jest.fn(),
    getParticipationStatusWithSubmissionInformation: jest.fn()
  };
  const operationsService = {
    saveParticipation: jest.fn(),
    viewAssignedGameCodes: jest.fn()
  };
  const registrationsProps = {
    user: { id: "a960d87e-26e5-49a2-b24b-8fd9a7d4746e", username: "jane", avatar: "jane.jpg" },
    WATERMARKS_URL: "/",
    analytics
  };
  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => router);
    OpportunityService.mockReturnValue(opportunityService);
    OperationsService.mockReturnValue(operationsService);
    useDependency.mockReturnValue({ errorHandler: errorHandlerFactory(false, ["en-us"]) });
  });

  it("shows a spinner while opportunity information is being retrieved", async () => {
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123"
      })
    );
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    expect(screen.getByAltText(/Loading/i)).toBeInTheDocument();
    await waitFor(() => expect(screen.queryByAltText(/Loading/i)).not.toBeInTheDocument());
  });

  it("shows content guidelines by default", async () => {
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        id: "OPPO123"
      })
    );

    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /opportunities:contentGuidelinesTitle/i })).toBeInTheDocument();
    });

    expect(screen.getAllByText(/opportunities:contentGuidelines/i)[0]).toBeInTheDocument();
  });

  it("logs 'Completed Join Opportunity Flow' event after content guidelines", async () => {
    const analytics = { completedJoinOpportunityFlow: jest.fn() };
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        hasDeliverables: true,
        id: "OPPO123"
      })
    );

    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });
    operationsService.saveParticipation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} analytics={analytics} />);

    await waitFor(() => expect(screen.queryAllByText(/opportunities:contentGuidelines/i).length).toBeGreaterThan(0));

    await userEvent.click(await screen.findByRole("button", { name: "join" }));

    await waitFor(() => {
      expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    });

    expect(analytics.completedJoinOpportunityFlow).toHaveBeenCalledWith({
      locale: "en-us",
      opportunity
    });
  });

  it("doesn't show content guidelines if opportunity has no content submission guidelines", async () => {
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        contentSubmission: { guidelines: null },
        hasDeliverables: true
      })
    );
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations {...registrationsProps} />);

    await waitFor(() => expect(screen.queryByText(/opportunities:contentGuidelines/i)).not.toBeInTheDocument());
  });

  it("doesn't show download attachments link, if there is no attachments", async () => {
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        contentSubmission: { guidelines: "<p>I am a content guideline</p>" },
        hasDeliverables: true,
        hasAttachments: false,
        attachmentsUrl: undefined
      })
    );
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations />);

    await waitFor(() =>
      expect(screen.queryByRole("link", { name: /content-submission:downloadAttachments$/i })).not.toBeInTheDocument()
    );
  });

  it("shows a link to download attachments", async () => {
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        contentSubmission: { guidelines: "<p>I am a content guideline</p>" },
        hasAttachments: true,
        attachmentsUrl: "https://downloadUrl.zip"
      })
    );
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations />);

    expect(await screen.findByRole("link", { name: /content-submission:downloadAttachments$/i })).toHaveAttribute(
      "href",
      "https://downloadUrl.zip"
    );
    expect(await screen.findByRole("link", { name: /content-submission:downloadAttachments$/i })).toHaveAttribute(
      "download"
    );
  });

  it("doesn't show attachments link if opportunity doesn't have content guidelines", async () => {
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        contentSubmission: { guidelines: null },
        hasAttachments: true,
        attachmentsUrl: "https://downloadUrl.zip"
      })
    );
    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations />);

    await waitFor(() =>
      expect(screen.queryByRole("link", { name: /content-submission:downloadAttachments$/i })).not.toBeInTheDocument()
    );
  });

  it.each(withGameCodeGuidelineEnabled)(
    "navigates to consent back for all steps enabled, when user taps on 'Back' button in '%s' step for desktop",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverables.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: true
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === LG_SIZE);
      renderPage(<Registrations analytics={analytics} />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("navigation"));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withContentGuidelineEnabled)(
    "navigates to consent back for only content guidelines enabled, when user taps on 'Back' button in '%s' step for desktop",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverables.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: true,
          hasGameCodes: false
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === LG_SIZE);
      renderPage(<Registrations analytics={analytics} />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("navigation"));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it.each(withGameCodeEnabled)(
    "navigates to consent back for only game codes enabled, when user taps on 'Back' button in '%s' step for desktop",
    async (_label, step, url) => {
      const opportunity = OpportunityWithDeliverables.fromApi(
        aOpportunityWithPerksAndContentSubmissionWithDeliverables({
          id: "OPPO123",
          hasDeliverables: false,
          hasGameCodes: true
        })
      );
      opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
        data: { opportunity, creator: aCreator() }
      });
      opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
        data: []
      });
      opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
        data: []
      });

      const router = {
        query: { id: opportunity.id, step: step },
        pathname: "/opportunities/[id]/registrations",
        locale: "en-us",
        push: jest.fn()
      };
      useRouter.mockImplementation(() => router);
      useDetectScreen.mockImplementation((width) => width === LG_SIZE);
      renderPage(<Registrations analytics={analytics} />);
      // There are two back buttons in this page used within to find the one in the header.
      const { findByRole } = within(await screen.findByRole("navigation"));

      await userEvent.click(await findByRole("button", { name: /Back/i }));

      expect(router.push).toHaveBeenCalledWith(url);
    }
  );

  it("redirects to the Join Opportunity page when clicking the close button", async () => {
    const analytics = { cancelledJoinOpportunityFlow: jest.fn() };
    const opportunity = OpportunityWithDeliverables.fromApi(
      aOpportunityWithPerksAndContentSubmissionWithDeliverables({
        id: "OPPO123",
        hasDeliverables: true,
        contentSubmission: { guidelines: null }
      })
    );

    opportunityService.getOpportunityWithDeliverables.mockResolvedValue({
      data: { opportunity, creator: aCreator() }
    });
    opportunityService.getOpportunitiesParticipationStatus.mockResolvedValue({
      data: []
    });
    opportunityService.getParticipationStatusWithSubmissionInformation.mockResolvedValue({
      data: []
    });

    renderPage(<Registrations analytics={analytics} />);

    await userEvent.click(screen.getByRole("button", { name: /Exit join opportunity flow/i }));

    await waitFor(() => {
      expect(analytics.cancelledJoinOpportunityFlow).toHaveBeenCalledTimes(1);
    });

    expect(router.push).toHaveBeenCalledWith(`/opportunities/${opportunity.id}`);
  });
});
