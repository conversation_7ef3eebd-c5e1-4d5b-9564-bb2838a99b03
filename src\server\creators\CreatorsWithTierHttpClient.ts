import CreatorWithTier from "./CreatorWithPayableStatus";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithTierHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithTier> {
    const response = await this.client.get(`/v5/creators/${id}`);
    return Promise.resolve(CreatorWithTier.fromApi(response.data));
  }
}

export default CreatorsWithTierHttpClient;
