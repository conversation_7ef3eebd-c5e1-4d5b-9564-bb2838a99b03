import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import ApiContainer from "@src/ApiContainer";
import OpportunitiesPagePropsController from "./controllers/OpportunitiesPagePropsController";
import config from "config";

export type OpportunitiesPageProps = {
  runtimeConfiguration?: Record<string, unknown>;
  user: AuthenticatedUser;
};

const opportunitiesProps = (locale: string) =>
  serverPropsControllerFactory(
    new OpportunitiesPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default opportunitiesProps;
