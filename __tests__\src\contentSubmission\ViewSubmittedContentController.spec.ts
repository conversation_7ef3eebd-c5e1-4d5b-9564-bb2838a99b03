import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import SubmittedContentHttpClient from "../../../src/submittedContent/SubmittedContentHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewSubmittedContentController from "@src/submittedContent/ViewSubmittedContentController";
import { aSubmittedContentCriteriaWithProgramCode } from "__tests__/factories/opportunities/SubmittedContentCriteriaWithProgramCode";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";

jest.mock("../../../config");

describe("ViewSubmittedContentController", () => {
  let controller: ViewSubmittedContentController;

  beforeEach(() => jest.clearAllMocks());

  it("finds contents submitted by a creator for an opportunity", async () => {
    const criteria = aSubmittedContentCriteriaWithProgramCode({ creatorId: null, programCode: "the_sims" });
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/submitted-content",
      query: criteria
    });
    const identity = Identity.fromStored(aStoredIdentity());
    req.session = { identity };
    const submittedContent = {
      count: 0,
      contents: [],
      total: 0
    };
    const contents = { getSubmittedContentsWithProgramCode: jest.fn().mockResolvedValue(submittedContent) };
    controller = new ViewSubmittedContentController((contents as unknown) as SubmittedContentHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(submittedContent);
    expect(contents.getSubmittedContentsWithProgramCode).toHaveBeenCalledTimes(1);
    expect(contents.getSubmittedContentsWithProgramCode).toHaveBeenCalledWith({ ...criteria, creatorId: identity.id });
  });
});
