import { useDependency } from "@src/context/DependencyContext";
import React from "react";
import { render } from "@testing-library/react";
import FrontendTracer from "@src/telemetry/FrontendTracer";
import TraceProvider from "@components/TraceProvider";

jest.mock("../../../src/telemetry/FrontendTracer");
jest.mock("../../../src/context/DependencyContext");

describe("TraceProvider", () => {
  it("configures frontend tracing", () => {
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SERVICE_NAME: "cn_ugx_sims",
        APP_ENV: "local",
        RELEASE_VERSION: "1.0.0",
        FLAG_OBSERVABILITY: true
      }
    });

    render(
      <TraceProvider>
        <div>Test Child</div>
      </TraceProvider>
    );

    expect(FrontendTracer).toHaveBeenCalledTimes(1);
    expect(FrontendTracer).toHaveBeenCalledWith({
      enabled: true,
      environment: "local",
      serviceName: "cn_ugx_sims",
      version: "1.0.0"
    });
  });
});
