import { act, render, screen } from "@testing-library/react";
import MyApp from "@src/pages/_app";
import { Router, useRouter } from "next/router";
import Random from "__tests__/factories/Random";

jest.mock("react-i18next", () => ({
  useTranslation: () => {
    return {
      t: jest.fn().mockImplementation === ((str) => str)
    };
  }
}));
jest.mock("../../../src/telemetry/FrontendTracer", () => jest.fn());
jest.mock("next/router", () => ({
  useRouter: jest.fn(),
  Router: {
    events: {
      on: jest.fn(),
      off: jest.fn()
    }
  }
}));

describe("MyApp", () => {
  const myAppProps = {
    Component: () => <h1> Component Title </h1>,
    pageProps: { showInitialMessage: false }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
  });

  it("displays build information in a non production environment", async () => {
    const version = Random.gitBranch();
    const runtimeConfiguration = { APP_ENV: Random.nonProductionEnvironment(), BUILD_VERSION: version };

    render(<MyApp {...myAppProps} pageProps={{ ...myAppProps.pageProps, runtimeConfiguration }} />);

    expect(await screen.findByText(/Build/i)).toBeInTheDocument();
    expect(await screen.findByText(new RegExp(version))).toBeInTheDocument();
  });

  it("shows the content of the component for logged in user", async () => {
    render(<MyApp {...myAppProps} />);

    expect(screen.getByRole("heading", { name: "Component Title" })).toBeInTheDocument();
  });

  it("shows loading state during route changes", async () => {
    render(<MyApp {...myAppProps} />);
    const routeChangeHandler = (Router.events.on as jest.Mock).mock.calls.find(
      (call) => call[0] === "routeChangeStart"
    )[1];

    act(() => {
      routeChangeHandler();
    });

    expect(screen.getByTestId("loading-icon")).toBeInTheDocument();
  });

  it("hides loading state after route change completes", async () => {
    render(<MyApp {...myAppProps} />);
    const startHandler = (Router.events.on as jest.Mock).mock.calls.find((call) => call[0] === "routeChangeStart")[1];
    const completeHandler = (Router.events.on as jest.Mock).mock.calls.find(
      (call) => call[0] === "routeChangeComplete"
    )[1];

    act(() => {
      startHandler();
      completeHandler();
    });

    expect(screen.queryByTestId("loading-indicator")).not.toBeInTheDocument();
  });
});
