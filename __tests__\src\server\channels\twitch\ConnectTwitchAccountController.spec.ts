import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectTwitchAccountController from "@src/server/channels/twitch/ConnectTwitchAccountController";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";

describe("ConnectTwitchAccountController", () => {
  let controller: ConnectTwitchAccountController;
  const redirectUrl = new URL("http://localhost:3040/api/twitch-connect");
  let connectedAccounts;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    connectedAccounts = ({ connectTwitchAccount: jest.fn() } as unknown) as ConnectedAccountsHttpClient;
    controller = new ConnectTwitchAccountController(connectedAccounts, redirectUrl);
  });

  it("connects a Twitch account for a creator", async () => {
    const code = "65e99cb6-b1c7-4bbd-b20d-5b1255f86ceb";
    const creatorId = "8bfc4cad-611c-4f54-8214-fac9d43f320f";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/twitch-connect?code=${code}`,
      session: {
        ...session,
        identity: Identity.fromStored({
          type: "CREATOR",
          id: creatorId
        } as StoredIdentity)
      }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
    expect(req.session.accountType).toEqual("Twitch");
    expect(req.session.save).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectTwitchAccount).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectTwitchAccount).toHaveBeenCalledWith({
      code,
      creatorId,
      redirectUri: redirectUrl.toString(),
      nucleusId: undefined
    });
  });
});
