import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { CommunicationPreferencesProps } from "@src/pages/communication-preferences";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { CommunicationPreferencesPageLabels } from "@src/server/contentManagement/CommunicationPreferencesPageMapper";
import { ConnectAccountsPageLabels } from "@src/server/contentManagement/ConnectAccountsPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class CommunicationPreferencesPropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<CommunicationPreferencesProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<CommunicationPreferencesProps>> {
    const pageLabels = (await this.contents.getPageLabels(
      this.currentLocale,
      "communicationPreferences"
    )) as CommunicationPreferencesPageLabels & CommonPageLabels & ConnectAccountsPageLabels & BreadcrumbPageLabels;
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );

    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels,
        user: authenticatedUser
      }
    };
  }
}
