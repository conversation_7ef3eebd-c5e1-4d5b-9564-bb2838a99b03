import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import RegistrationsPagePropsController from "@src/serverprops/controllers/RegistrationsPagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "config";

jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations");

describe("RegistrationsPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for registrations page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    config.WATERMARKS_URL = "https://example.com/watermarks";
    req.session = { identity };
    const configuration = {};
    const translations = {};

    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    (serverSideTranslations as jest.Mock).mockResolvedValue(translations);

    const controller = new RegistrationsPagePropsController(options, currentLocale, "");
    const props = await controller.handle(req, res);

    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        WATERMARKS_URL: config.WATERMARKS_URL,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        ...translations
      }
    });
  });
});
