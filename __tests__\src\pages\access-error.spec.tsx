// import { useDependency } from "@src/context/DependencyContext";
import { useDependency } from "@src/context/DependencyContext";
import AccessError from "@src/pages/access-error";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { ErrorPageLabels } from "@src/server/contentManagement/ErrorPageMapper";
import { render, screen } from "@testing-library/react";
import { axe } from "jest-axe";
import { useRouter } from "next/router";

jest.mock("../../../src/context/DependencyContext");

describe("AccessError", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    const errorHandler = jest.fn();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      metadataClient: {},
      errorHandler,
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  const accessErrorLabels = {
    errorLabels: {
      accessErrorTitle: "Access Error",
      accessErrorSubtitle: "You do not have access to this page"
    },
    commonPageLabels: {
      logout: "Log out"
    }
  } as ErrorPageLabels & CommonPageLabels;

  it("shows access error page", () => {
    render(<AccessError pageLabels={accessErrorLabels} />);

    expect(screen.getByText(accessErrorLabels.errorLabels.accessErrorTitle)).toBeInTheDocument();
    expect(screen.getByText(accessErrorLabels.errorLabels.accessErrorSubtitle)).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<AccessError pageLabels={accessErrorLabels} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
