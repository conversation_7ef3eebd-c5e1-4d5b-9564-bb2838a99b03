import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Inject, Service } from "typedi";

export type ContentCriteria = {
  locale: string;
};

type Documentation = {
  data:
    | Record<string, string>
    | {
        error?: string;
        error_description?: string;
        code?: string;
        message?: string;
        status?: number;
        errors?: Record<string, string>;
      };
};

@Service()
class IndexesHttpClient {
  constructor(@Inject("contentManagementClient") private client: TraceableHttpClient) {}

  async matching(slug: string, criteria: ContentCriteria): Promise<Documentation> {
    const response = await this.client.get(`/v1/indexes/${slug}`, {
      query: { ...criteria, published: true }
    });
    return response.data;
  }
}

export default IndexesHttpClient;
