import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { TermsAndConditionsProps } from "@src/pages/terms-and-conditions";
import TermsAndConditionsPagePropsController from "@src/serverprops/controllers/TermsAndConditionsPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";
import flags from "@src/utils/feature-flags";
import { NextApiResponse } from "next";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("../../../../src/utils/feature-flags");

describe("TermsAndConditionsPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the Terms and Conditions page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      headers: { referer: "/opportunities" }
    });
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    const opportunityId = Random.uuid();
    const initialPage = "/profile";
    req.session = {
      identity,
      opportunityId,
      [`${program}.initialPage`]: initialPage
    };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {};
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new TermsAndConditionsPagePropsController(options, contents, currentLocale, "");
    (flags.isCountriesByTypeEnabled as jest.Mock).mockReturnValue(true);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "termsAndConditions");
    expect(props).toEqual({
      props: {
        FLAG_COUNTRIES_BY_TYPE: true,
        initialPage,
        runtimeConfiguration: configuration,
        locale: currentLocale,
        opportunityId,
        urlLocale: "/",
        pageLabels: labels,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        }
      }
    });
  });

  it("assign default values to optional props", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      headers: { referer: "/opportunities" }
    });
    const identity = Identity.fromStored(aStoredIdentity());
    req.session = { identity };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {};
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new TermsAndConditionsPagePropsController(options, contents, currentLocale, "");
    (flags.isCountriesByTypeEnabled as jest.Mock).mockReturnValue(true);

    const props = await controller.handle(req, res);

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "termsAndConditions");
    expect((props as { props: TermsAndConditionsProps }).props.initialPage).toEqual(null);
    expect((props as { props: TermsAndConditionsProps }).props.opportunityId).toEqual(null);
  });
});
