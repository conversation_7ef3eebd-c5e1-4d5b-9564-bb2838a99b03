import { act, screen, waitFor, within } from "@testing-library/react";
import { aCreator } from "../../../factories/creators/Creator";
import { aPrimaryFranchise, aSecondaryFranchise } from "../../../factories/franchises/PreferredFranchise";
import userEvent from "@testing-library/user-event";
import PreferredPlatform from "../../../../src/server/platforms/PreferredPlatform";
import { useRouter } from "next/router";
import { useAppContext } from "../../../../src/context";
import { renderWithToast, triggerAnimationEnd } from "../../../helpers/toast";
import { commonTranslations as layout } from "../../../translations";
import { onToastClose } from "../../../../src/utils";
import { aPrimaryPlatform, aSecondaryPlatform } from "../../../factories/platforms/PreferredPlatform";
import { aFranchise } from "@eait-playerexp-cn/metadata-test-fixtures";
import PreferredFranchise from "../../../../src/server/franchises/PreferredFranchise";
import { useDependency } from "../../../../src/context/DependencyContext";
import GamePreferences from "../../../../src/components/profile/GamePreferences";
import { axe } from "jest-axe";

jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));
jest.mock("../../../../src/context", () => ({
  ...jest.requireActual("../../../../src/context"),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../../src/utils", () => ({
  ...jest.requireActual("../../../../src/utils"),
  onToastClose: jest.fn()
}));
jest.mock("../../../../src/context/DependencyContext");

xdescribe("GamePreferences", () => {
  const gamePreferencesProps = {
    layout: { main: {} },
    buttons: { edit: "Edit", cancel: "Cancel", save: "Save" },
    franchisesYouPlayLabels: {
      labels: { primaryFranchise: "Primary franchise" },
      messages: { success: { content: "Changes saved!" } },
      title: "Franchises you Play"
    },
    infoLabels: {
      title: "Start Building your Profile",
      primaryPlatform: "Primary Platform",
      platformPreferences: "Platform Preferences",
      platformPreferencesTitle: "Select the platform you play the most.",
      secondaryPlatforms: "Secondary Platform",
      secondaryPlatformsTitle: "Select other platforms that you own.",
      success: {
        platformPreferences: "You have successfully updated your Platform Preferences."
      },
      labels: {
        firstName: "First Name"
      }
    },
    updateCreator: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockImplementation(() => ({ locale: "en-us" }));
    useDependency.mockReturnValue({
      errorHandler: jest.fn(),
      configuration: {}
    });
  });

  it("updates preferred primary franchise and logs 'Updated Primary Franchise' event", async () => {
    const fifa = aPrimaryFranchise({ name: "FIFA" });
    const primaryFranchise = PreferredFranchise.fromApi(fifa);
    const newPrimaryFranchise = PreferredFranchise.fromApi(aPrimaryFranchise({ name: "Plants vs. Zombies" }));
    const creator = new CreatorWithFlaggedStatusProfile(aCreator({ preferredFranchises: [fifa] }));
    const selectedFranchise = aFranchise(newPrimaryFranchise);
    const franchises = [aFranchise(primaryFranchise), selectedFranchise];
    const updatedCreator = creator;
    updatedCreator.preferredFranchises = [newPrimaryFranchise];
    const newPreferredFranchises = {
      franchisesYouPlay: {
        primaryFranchise: selectedFranchise,
        secondaryFranchise: []
      }
    };
    CreatorsService.update.mockResolvedValue({
      data: updatedCreator
    });
    const updateCreator = jest.fn();
    const analytics = { updatedPrimaryFranchise: jest.fn(), updatedSecondaryFranchises: jest.fn() };
    renderWithToast(
      <GamePreferences
        {...gamePreferencesProps}
        creator={creator}
        franchises={franchises}
        updateCreator={updateCreator}
        analytics={analytics}
      />
    );
    await userEvent.click(await screen.findByRole("button", { name: /Edit/i }));
    // Wait for search box to be shown
    const primaryFranchiseSearchBox = await screen.findByDisplayValue(/FIFA/i);
    // Enter a new preferred primary franchise
    await userEvent.clear(primaryFranchiseSearchBox);
    await userEvent.type(primaryFranchiseSearchBox, "Plants");
    // Choose the new option
    await userEvent.click(await screen.findByText(/Plants vs. Zombies/i));
    // Check the new franchise was selected
    await screen.findByDisplayValue(/Plants vs. Zombies/i);
    const saveButton = await screen.findByRole("button", { name: /Save/i });

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedPrimaryFranchise).toHaveBeenCalledTimes(1);
      expect(analytics.updatedPrimaryFranchise).toHaveBeenCalledWith({ locale: "en-us", creator });
      expect(analytics.updatedSecondaryFranchises).not.toHaveBeenCalledTimes(1);
      expect(analytics.updatedSecondaryFranchises).not.toHaveBeenCalledWith({ locale: "en-us", creator });
      expect(screen.queryByRole("button", { name: /Edit/i })).toBeInTheDocument();
      expect(screen.getByText(/Plants vs. Zombies/i)).toBeInTheDocument();
      expect(
        screen.getByRole("img", { src: "https://test.ea.com/img/public/franchises/plants-vs-zombies.jpg" })
      ).toBeInTheDocument();
      expect(CreatorsService.update).toHaveBeenCalledTimes(1);
      expect(CreatorsService.update).toHaveBeenCalledWith(newPreferredFranchises);
      expect(updateCreator).toHaveBeenCalledTimes(1);
      expect(updateCreator).toHaveBeenCalledWith(updatedCreator);
    });
  });

  it("logs 'Updated Secondary Franchise' and 'Updated Primary Franchise' events ", async () => {
    const primaryFranchise = aPrimaryFranchise({ name: "APEX" });
    const secondaryFranchise = aSecondaryFranchise({ name: "FIFA" });
    const newSecondaryFranchise = aSecondaryFranchise({ name: "Sims" });
    const newPrimaryFranchise = aPrimaryFranchise({ name: "Iron Man" });
    const creator = new CreatorWithFlaggedStatusProfile(
      aCreator({ preferredFranchises: [primaryFranchise, secondaryFranchise] })
    );
    const franchises = [
      aFranchise(primaryFranchise),
      aFranchise(newPrimaryFranchise),
      aFranchise(secondaryFranchise),
      aFranchise(newSecondaryFranchise)
    ];
    const updatedCreator = creator;
    updatedCreator.preferredFranchises = [newPrimaryFranchise, newSecondaryFranchise];
    CreatorsService.update.mockResolvedValue({
      data: updatedCreator
    });
    const updateCreator = jest.fn();
    const analytics = { updatedPrimaryFranchise: jest.fn(), updatedSecondaryFranchises: jest.fn() };
    renderWithToast(
      <GamePreferences
        {...gamePreferencesProps}
        creator={creator}
        franchises={franchises}
        updateCreator={updateCreator}
        analytics={analytics}
      />
    );
    await userEvent.click(await screen.findByRole("button", { name: /Edit/i }));
    const saveButton = await screen.findByRole("button", { name: /Save/i });
    expect(saveButton).toBeEnabled();

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedPrimaryFranchise).toHaveBeenCalledTimes(1);
      expect(analytics.updatedPrimaryFranchise).toHaveBeenCalledWith({ locale: "en-us", creator });
      expect(analytics.updatedSecondaryFranchises).toHaveBeenCalledTimes(1);
      expect(analytics.updatedSecondaryFranchises).toHaveBeenCalledWith({ locale: "en-us", creator });
    });
  });

  it("logs 'Updated Primary Platform' and 'Updated Secondary Platform' events", async () => {
    const primaryPlatform = aPrimaryPlatform({ name: "Mac" });
    const newPrimaryPlatform = aPrimaryPlatform({ name: "Xbox" });
    const secondaryPlatform = aSecondaryPlatform({ name: "Android" });
    const removedSecondaryPlatform = aSecondaryPlatform({ name: "Xbox" });
    const addedSecondaryPlatform = aSecondaryPlatform({ name: "Mac" });
    const creator = new CreatorWithFlaggedStatusProfile(
      aCreator({ preferredPlatforms: [primaryPlatform, secondaryPlatform, removedSecondaryPlatform] })
    );
    const platforms = [
      primaryPlatform,
      newPrimaryPlatform,
      secondaryPlatform,
      removedSecondaryPlatform,
      addedSecondaryPlatform
    ];
    const updatedCreator = creator;
    updatedCreator.preferredPlatforms = [newPrimaryPlatform, secondaryPlatform, addedSecondaryPlatform];
    CreatorsService.update.mockResolvedValue({
      data: updatedCreator
    });
    const analytics = { updatedPrimaryPlatformInProfile: jest.fn(), updatedSecondaryPlatformsInProfile: jest.fn() };
    renderWithToast(
      <GamePreferences {...gamePreferencesProps} platforms={platforms} creator={creator} analytics={analytics} />
    );
    await userEvent.click(await screen.findByRole("button", { name: /Edit/i }));
    const saveButton = await screen.findByRole("button", { name: /Save/i });
    expect(saveButton).toBeEnabled();

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedPrimaryPlatformInProfile).toHaveBeenCalledWith({ locale: "en-us", creator });
      expect(analytics.updatedSecondaryPlatformsInProfile).toHaveBeenCalledWith({
        locale: "en-us",
        creator,
        selectedPlatforms: updatedCreator.preferredSecondaryPlatforms
      });
    });
  });

  it("logs 'Updated Primary Platform' event when primary platform updated", async () => {
    const primaryPlatform = aPrimaryPlatform({ name: "Mac" });
    const newPrimaryPlatform = aPrimaryPlatform({ name: "Xbox" });
    const secondaryPlatform = aSecondaryPlatform({ name: "Android" });
    const creator = new CreatorWithFlaggedStatusProfile(
      aCreator({ preferredPlatforms: [primaryPlatform, secondaryPlatform] })
    );
    const platforms = [
      PreferredPlatform.fromApi(primaryPlatform),
      PreferredPlatform.fromApi(newPrimaryPlatform),
      PreferredPlatform.fromApi(secondaryPlatform)
    ];
    const updatedCreator = creator;
    updatedCreator.preferredPlatforms = [newPrimaryPlatform, secondaryPlatform];
    CreatorsService.update.mockResolvedValue({
      data: updatedCreator
    });
    const analytics = { updatedPrimaryPlatformInProfile: jest.fn(), updatedSecondaryPlatformsInProfile: jest.fn() };
    renderWithToast(
      <GamePreferences {...gamePreferencesProps} platforms={platforms} creator={creator} analytics={analytics} />
    );
    await userEvent.click(await screen.findByRole("button", { name: /Edit/i }));
    const saveButton = await screen.findByRole("button", { name: /Save/i });
    expect(saveButton).toBeEnabled();

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedPrimaryPlatformInProfile).toHaveBeenCalledWith({ locale: "en-us", creator });
      expect(analytics.updatedSecondaryPlatformsInProfile).not.toHaveBeenCalledWith();
    });
  });

  it("logs 'Updated Secondary Platform' event when secondary platform updated", async () => {
    const primaryPlatform = aPrimaryPlatform({ name: "Mac" });
    const secondaryPlatform = aSecondaryPlatform({ name: "Android" });
    const addedSecondaryPlatform = aSecondaryPlatform({ name: "Mac" });
    const creator = new CreatorWithFlaggedStatusProfile(
      aCreator({ preferredPlatforms: [primaryPlatform, secondaryPlatform] })
    );
    const platforms = [primaryPlatform, secondaryPlatform, addedSecondaryPlatform];
    const updatedCreator = creator;
    updatedCreator.preferredPlatforms = [primaryPlatform, secondaryPlatform, addedSecondaryPlatform];
    CreatorsService.update.mockResolvedValue({
      data: updatedCreator
    });
    const analytics = { updatedPrimaryPlatformInProfile: jest.fn(), updatedSecondaryPlatformsInProfile: jest.fn() };
    renderWithToast(
      <GamePreferences {...gamePreferencesProps} platforms={platforms} creator={creator} analytics={analytics} />
    );
    await userEvent.click(await screen.findByRole("button", { name: /Edit/i }));
    const saveButton = await screen.findByRole("button", { name: /Save/i });
    expect(saveButton).toBeEnabled();

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(analytics.updatedPrimaryPlatformInProfile).not.toHaveBeenCalledWith();
      expect(analytics.updatedSecondaryPlatformsInProfile).toHaveBeenCalledWith({
        locale: "en-us",
        creator,
        selectedPlatforms: updatedCreator.preferredSecondaryPlatforms
      });
    });
  });

  it("closes error toast message on clicking the close button", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const dispatch = jest.fn();
    const errorMessage = "ERROR";
    useAppContext.mockReturnValue({
      dispatch,
      state: { isError: errorMessage }
    });
    const { unmount } = renderWithToast(<GamePreferences {...gamePreferencesProps} layout={layout.default} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent("Oops! Something has gone wrong.");
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(screen.getByText("Oops! Something has gone wrong."));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(errorMessage, dispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderWithToast(<GamePreferences {...gamePreferencesProps} layout={layout.default} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
