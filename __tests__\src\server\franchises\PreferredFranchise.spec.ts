import PreferredFranchise from "@src/server/franchises/PreferredFranchise";
import config from "config";

describe("PreferredFranchise", () => {
  config.DEFAULT_FRANCHISE_IMAGE =
    "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/franchises/ea-no-franchise.png";
  const franchiseResponse = {
    id: "1",
    name: "PC",
    code: "pc",
    type: "PRIMARY"
  };

  it("formats the response", () => {
    const franchise = PreferredFranchise.fromApi(franchiseResponse);

    expect(franchise.value).toBe(franchiseResponse.id);
    expect(franchise.label).toBe(franchiseResponse.name);
    expect(franchise.image).toBe(
      "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/franchises/ea-no-franchise.png"
    );
    expect(franchise.type).toBe(franchiseResponse.type);
  });

  it("should return true if type is PRIMARY", () => {
    const franchise = new PreferredFranchise("123", "Test Franchise", "test.png", "PRIMARY");

    expect(franchise.isPrimary()).toBe(true);
  });

  it("should return false if type is not PRIMARY", () => {
    const franchise = new PreferredFranchise("123", "Test Franchise", "test.png", "SECONDARY");

    expect(franchise.isPrimary()).toBe(false);
  });
});
