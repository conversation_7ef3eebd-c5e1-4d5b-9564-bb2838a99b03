import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import ConnectedAccountsHttpClient from "@src/server/channels/ConnectedAccountsHttpClient";
import Random from "__tests__/factories/Random";

describe("ConnectedAccountsHttpClient", () => {
  it("connects a YouTube account", async () => {
    const nucleusId = Random.nucleusId();
    const credentials = {
      code: Random.uuid(),
      creatorId: Random.uuid(),
      nucleusId,
      redirectUrl: "http://localhost:3040/api/youtube-connect"
    };
    const client = { post: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient((client as unknown) as TraceableHttpClient);

    await connectedAccountsHttpClient.connectYouTubeAccount(credentials);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v2/youtube-accounts", { body: credentials });
  });

  it("connects a twitch account", async () => {
    const credentials = {
      code: Random.uuid(),
      creatorId: Random.uuid(),
      nucleusId: Random.nucleusId()
    };
    const client = { post: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient((client as unknown) as TraceableHttpClient);

    await connectedAccountsHttpClient.connectTwitchAccount(credentials);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v2/twitch-accounts", { body: credentials });
  });

  it("connects an Instagram account", async () => {
    const credentials = {
      code: Random.uuid(),
      creatorId: Random.uuid(),
      nucleusId: Random.nucleusId(),
      redirectUrl: "http://localhost:3040/api/instagram-connect"
    };
    const client = { post: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient((client as unknown) as TraceableHttpClient);

    await connectedAccountsHttpClient.connectInstagramAccount(credentials);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v2/instagram-accounts", { body: credentials });
  });

  it("connects a Facebook account", async () => {
    const code = Random.uuid();
    const redirectUri = "http://localhost:3040/api/facebook-connect";
    const client = { get: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient((client as unknown) as TraceableHttpClient);

    await connectedAccountsHttpClient.facebookPages(code, redirectUri);

    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith("/v2/facebook-pages", { query: { code, redirectUri } });
  });

  it("connects a TikTok account", async () => {
    const credentials = {
      code: Random.uuid(),
      creatorId: Random.uuid(),
      nucleusId: Random.nucleusId()
    };
    const client = { post: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient((client as unknown) as TraceableHttpClient);

    await connectedAccountsHttpClient.connectTikTokAccount(credentials);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v3/tiktok-accounts", { body: credentials });
  });
});
