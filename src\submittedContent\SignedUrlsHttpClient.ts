import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { SignedURLRequestBody } from "@src/services/SubmittedContentService";

export type PreSignedUrl = {
  url: string;
  fileId: string;
};

export type UploadedFile = {
  participationId: string;
  fileName: string;
  fileId?: string | null;
};

@Service()
class SignedUrlsHttpClient {
  constructor(@Inject("contentScanningClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/viewContentUploadPreSignedUrlWithListing}
   */
  async preSignedUrlFor(signedURLRequestBody: SignedURLRequestBody): Promise<PreSignedUrl> {
    const response = await this.client.post(`/v2/upload-signed-urls`, {
      body: signed<PERSON><PERSON>equestBody
    });
    return Promise.resolve({ ...response.data });
  }
}

export default SignedUrlsHttpClient;
