import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import DashboardPagePropsController from "@src/serverprops/controllers/DashboardPagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("next-i18next/serverSideTranslations");

describe("DashboardPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the dashboard page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    req.session = { identity };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const controller = new DashboardPagePropsController(options, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        locale: currentLocale
      }
    });
  });
});
