import React from "react";
import Link from "next/link";
import ChevronRight from "@components/icons/ChevronRight";
import { Icon } from "@eait-playerexp-cn/core-ui-kit";

export type BreadCrumb = {
  label: string;
  link: string;
  isActive: boolean;
};

type BreadCrumbProps = {
  items: BreadCrumb[];
};

const BreadCrumbs = ({ items }: BreadCrumbProps) => {
  return (
    <nav className="breadcrumbs" data-testid="breadcrumbs">
      <ul className="breadcrumbs-list">
        {items.map((crumb, index) => (
          <li key={`${index}-${crumb.label}`} className="breadcrumbs-listitem">
            <Link
              href={crumb.link}
              className={crumb.isActive ? "breadcrumbs-listitem-link-active" : "breadcrumbs-listitem-link-inactive"}
            >
              {crumb.label}
            </Link>
            {!crumb.isActive && <Icon icon={ChevronRight} id="breadcrumbs-icon" className="breadcrumbs-icon" />}
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default BreadCrumbs;
