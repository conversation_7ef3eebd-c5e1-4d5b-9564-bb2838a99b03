import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { RequestHandlerOptions, ServerPropsController } from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { AgeRestrictionProps } from "@src/pages/interested-creators/age-restriction";
import { AgeRestrictionPageLabels } from "@src/server/contentManagement/AgeRestrictionPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult } from "next";

export default class AgeRestrictionPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<AgeRestrictionProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(): Promise<GetServerSidePropsResult<AgeRestrictionProps>> {
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        locale: this.currentLocale,
        pageLabels: (await this.contents.getPageLabels(
          this.currentLocale,
          "ageRestriction"
        )) as AgeRestrictionPageLabels
      }
    };
  }
}
