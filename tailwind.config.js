const coreUiKit = require("@eait-playerexp-cn/core-ui-kit/config");

module.exports = coreUiKit({
  content: ["src/pages/**/*.{ts,js,jsx,tsx}", "src/components/**/*.{ts,js,jsx,tsx}"],
  theme: {
    extend: {
      backgroundImage: {
        "migration-shape": "var(--migration-background)",
        "migration-background-tablet": "var(--migration-background-tablet)",
        "down-arrow-icon-background": "var(--down-arrow-icon-background)",
        "support-a-creator-text-background": "linear-gradient(90deg, #FFCD16 0%, #B565D4 39%, #9206C6 79.5%)",
        "migration-background-tablet": "var(--migration-background-tablet)",
        "error-desktop-background-image": "var(--error-desktop-background)",
        "error-mobile-background-image": "var(--error-mobile-background)",
        "error-tablet-background-image": "var(--error-tablet-background)",
        "error-background-image": "var(--login-error-desktop-background-image)",
        "error-desktop-character-background-image": "var(--error-desktop-character-background)",
        "error-mobile-character-background-image": "var(--error-mobile-character-background)",
        "error-tablet-character-background-image": "var(--error-tablet-character-background)",
        "access-error-desktop-background-image": "var(--error-page-background)",
        "hero-background-image": "var(--hero-background-image)",
        "start-page-web": "linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "start-page-tablet":
          "linear-gradient(186.57deg, rgba(255, 71, 71, 0.77) -3.69%, rgba(12, 15, 64, 0.77) 12.16%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "start-page-mobile":
          "linear-gradient(188.41deg, rgba(255, 71, 71, 0.77) -7.25%, rgba(12, 15, 64, 0.77) 43.94%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "migration-web": "linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "migration-default":
          "linear-gradient(186.57deg, rgba(255, 71, 71, 0.77) -3.69%, rgba(12, 15, 64, 0.77) 12.16%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)"
      },
      boxShadow: {
        sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        "gray-10-outline-10-opacity-0.7": "0px 0px 10px rgba(255, 255, 255, 0.7)",
        "navy-60-outline-60-opacity-0.8": "0px 0px 60px rgba(42, 59, 137, 0.8)",
        "gray-90-outline-4-8-apacity-0.15": " 0px 4px 8px 0px rgba(0, 0, 0, 0.15)",
        "navy-80-image-card": "0 4px 8px 0 rgb(16 14 134), 0 6px 20px 0 rgb(0 3 10)",
        "error-background-image": "var(--error-desktop-background-image)",
        "down-arrow-icon-background": "var(--down-arrow-icon-background)"
      },
      boxShadow: {
        sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        "gray-10-outline-10-opacity-0.7": "0px 0px 10px rgba(255, 255, 255, 0.7)",
        "navy-60-outline-60-opacity-0.8": "0px 0px 60px rgba(42, 59, 137, 0.8)",
        "gray-90-outline-4-8-apacity-0.15": " 0px 4px 8px 0px rgba(0, 0, 0, 0.15)",
        "navy-80-image-card": "0 4px 8px 0 rgb(16 14 134), 0 6px 20px 0 rgb(0 3 10)"
      },
      fill: (theme) => ({
        current: "currentColor",
        "gray-10": theme("colors.gray.10"),
        "gray-50": theme("colors.gray.50"),
        "indigo-50": theme("colors.indigo.50"),
        "navy-60": theme("colors.navy.60"),
        "navy-80": theme("colors.navy.80"),
        "success-30": theme("colors.success.30"),
        "success-70": theme("colors.success.70")
      })
    },
    stroke: (theme) => ({
      "navy-40": theme("colors.navy.40"),
      "navy-60": theme("colors.navy.60"),
      "navy-80": theme("colors.navy.80"),
      "success-50": theme("colors.success.50"),
      "success-70": theme("colors.success.70")
    }),
    screens: {
      cardSmall: "450px"
    }
  },
  plugins: [require("@tailwindcss/aspect-ratio"), require("@tailwindcss/typography")]
});
