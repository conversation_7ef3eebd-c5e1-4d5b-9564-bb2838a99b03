import { render, screen } from "@testing-library/react";
import HeroSection from "./HeroSection";
import Random from "__tests__/factories/Random";
import { useDetectScreen } from "@src/utils";
import { anImage } from "__tests__/factories/documentation/documentation";
import { axe } from "jest-axe";

jest.mock("../../../src/utils", () => ({
  useDetectScreen: jest.fn()
}));

describe("HeroSection", () => {
  const heroSectionProps = {
    title: Random.sentence(2),
    description: Random.sentence(4),
    icon: anImage(),
    image: anImage()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows hero section with title and description", () => {
    render(<HeroSection {...heroSectionProps} />);

    expect(screen.getByTestId("hero-section")).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: heroSectionProps.title })).toBeInTheDocument();
    expect(screen.getByText(heroSectionProps.description)).toBeInTheDocument();
  });

  it("shows desktop images when not mobile", () => {
    (useDetectScreen as jest.Mock).mockReturnValue(false);

    render(<HeroSection {...heroSectionProps} />);

    expect(screen.getByRole("img", { name: heroSectionProps.image.media.title })).toBeInTheDocument();
    expect(screen.getByRole("img", { name: heroSectionProps.icon.media.title })).toBeInTheDocument();
  });

  it("shows mobile images when on mobile", () => {
    (useDetectScreen as jest.Mock).mockReturnValue(true);

    render(<HeroSection {...heroSectionProps} />);

    expect(screen.getByRole("img", { name: heroSectionProps.image.mobileMedia.title })).toBeInTheDocument();
    expect(screen.getByRole("img", { name: heroSectionProps.icon.mobileMedia.title })).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<HeroSection {...heroSectionProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
