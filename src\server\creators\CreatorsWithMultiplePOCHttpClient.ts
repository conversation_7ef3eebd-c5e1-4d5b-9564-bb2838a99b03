import { Inject, Service } from "typedi";
import CreatorWithMultiplePOC from "./CreatorWithMultiplePOC";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
class CreatorsWithMultiplePOCHttpClient {
  constructor(@Inject("operationsClient") private client: TraceableHttpClient) {}

  async withId(id: string): Promise<CreatorWithMultiplePOC> {
    const response = await this.client.get(`/v8/creators/${id}`);
    return Promise.resolve(CreatorWithMultiplePOC.fromApi(response.data));
  }
}

export default CreatorsWithMultiplePOCHttpClient;
