import React, { memo } from "react";
import CreatorDisplayName from "../CreatorDisplayName";
import UpdateProfilePicture from "../upload/UpdateProfilePicture";

export default memo(function ProfileCard({ labels, user, accountInformation, registrationDate, data, stableDispatch }) {
  return (
    <div className="profile-card">
      <UpdateProfilePicture
        isPlaceholderDefault={false}
        src={user.avatar}
        user={user}
        labels={labels}
        stableDispatch={stableDispatch}
      />

      <div className="profile-card-info">
        <CreatorDisplayName user={user} tooltip={data} />
        {accountInformation && (
          <div className="profile-card-sub-title">
            <span className="profile-card-creator-since">{labels.creatorSince} </span>
            {registrationDate}
          </div>
        )}
      </div>
    </div>
  );
});
