import { Factory } from "fishery";
import Random from "../Random";
import { DocumentationPageProps as DocumentationPageData } from "@components/DocumentationPage/DocumentationPage";
import { Media, SystemInformation } from "@components/RichText/RichText";
import { Image } from "@components/HeroSection/HeroSection";
import { Category } from "@components/CategoryCard/CategoryCard";
import { Link, SubCategory } from "@components/SubCategoryCard/SubCategoryCard";

const systemInfo = Factory.define<SystemInformation>(() => ({
  id: Random.uuid(),
  publishedAt: Random.string(),
  firstPublishedAt: Random.string(),
  publishedVersion: Random.number()
}));

const media = Factory.define<Media>(() => ({
  url: Random.imageUrl(),
  title: Random.string(),
  description: Random.string(),
  contentType: "image/jpeg",
  fileName: "test.jpg",
  size: Random.number(),
  width: 1920,
  height: 1080
}));

const image = Factory.define<Image>(() => ({
  sys: aSystemInformation(),
  title: Random.string(),
  media: anImageMedia(),
  mobileMedia: anImageMedia(),
  altText: Random.string(),
  titleTags: Random.string(),
  ariaTags: Random.string(),
  entryType: "Image"
}));

const category = Factory.define<Category>(() => ({
  sys: aSystemInformation(),
  title: Random.sentence(2),
  description: Random.sentence(4),
  internalName: Random.string(),
  subCategories: [aSubCategory(), aSubCategory(), aSubCategory()],
  entryType: "Category"
}));

const subCategory = Factory.define<SubCategory>(() => ({
  sys: aSystemInformation(),
  title: Random.sentence(2),
  internalName: Random.string(),
  links: [aLink(), aLink(), aLink()],
  entryType: "SubCategory"
}));

const link = Factory.define<Link>(() => ({
  sys: aSystemInformation(),
  title: Random.sentence(2),
  administrativeTitle: Random.sentence(2),
  url: null,
  page: {
    slug: Random.string(),
    type: "Article"
  },
  entryType: "Link",
  openInNewTab: true
}));

const factory = Factory.define<DocumentationPageData>(() => ({
  sys: aSystemInformation(),
  administrativeTitle: Random.string(),
  slug: Random.string(),
  title: Random.sentence(2),
  description: Random.sentence(4),
  image: anImage(),
  icon: anImage(),
  categories: [aCategory(), aCategory()],
  entryType: "Index"
}));

export function documentationPageData(override = {}): DocumentationPageData {
  return factory.build(override);
}
export function aSystemInformation(override = {}): SystemInformation {
  return systemInfo.build(override);
}
export function anImageMedia(override = {}): Media {
  return media.build(override);
}
export function anImage(override = {}): Image {
  return image.build(override);
}
export function aCategory(override = {}): Category {
  return category.build(override);
}
export function aSubCategory(override = {}): SubCategory {
  return subCategory.build(override);
}
export function aLink(override = {}): Link {
  return link.build(override);
}
