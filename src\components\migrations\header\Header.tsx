import React, { FC, memo } from "react";
import Link from "next/link";
import { close, Icon, leftArrow, Stepper } from "@eait-playerexp-cn/core-ui-kit";
import classNames from "classnames";
import { useRouter } from "next/router";
import { USER_NAVIGATED } from "../../../utils";
import { NavStep } from "../migration-layout/MigrationLayout";

export const BackButton = ({ back, isFirstStep, onGoBack }) => {
  return (
    <>
      {!isFirstStep && (
        <button
          className={classNames({ "mg-header-display-back-bt": !isFirstStep, "mg-header-hide-back-bt": isFirstStep })}
          onClick={onGoBack}
          data-testid="back-button"
        >
          <Icon icon={leftArrow} className="mg-header-breadcrumb-nav" />
          <span>{back}</span>
        </button>
      )}
    </>
  );
};

export type headerProps = {
  onClose: () => void;
  steps: Array<NavStep>;
  currentPage: NavStep | null | object;
  labels: {
    back: string;
    title: string;
    close: string;
  };
  onGoBack: () => void;
  stableDispatch: (action) => void;
  setShowMigration: (value) => void;
  setNavigateToPage: (value) => void;
  completedLabel: string;
};

const Header: FC<headerProps> = ({
  onClose,
  steps,
  currentPage,
  labels,
  onGoBack,
  stableDispatch,
  setShowMigration,
  setNavigateToPage,
  completedLabel
}) => {
  const router = useRouter();

  const stepPos = steps?.findIndex(({ title }) => title === (currentPage as NavStep)?.title);

  const handleStepperNavigation = (href) => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    if (setShowMigration) {
      /** Show the confirmation modal even with stepper navigation */
      setShowMigration(true);
      setNavigateToPage(href);
    } else {
      router.push(href);
      stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: false });
    }
  };

  return (
    <div className="mg-header-container">
      <div className="mg-header">
        <div className="mg-header-back">
          <BackButton back={labels.back} isFirstStep={stepPos === 0} onGoBack={onGoBack} />
        </div>
        <div className="mg-header-logo">
          <Link
            onClick={(e) => {
              if (stepPos === steps.length - 1) {
                e.preventDefault(); // Prevent navigation if it's last step
              }
            }}
            href="/onboarding/information"
          >
            <img src="/img/sims-logo.svg" data-testid="logo-img" />
          </Link>
        </div>
        <div className="mg-header-close">
          <button onClick={onClose} data-testid="close-icon" aria-label={labels.close}>
            <Icon icon={close} />
          </button>
        </div>
      </div>
      <div className="mg-header-steps">
        <div className="mg-header-stepper-back">
          <BackButton back={labels.back} isFirstStep={stepPos === 0} onGoBack={onGoBack} />
        </div>
        <Stepper
          {...{
            steps,
            current: (currentPage as NavStep)?.title,
            handleNavigate: handleStepperNavigation,
            completedLabel: completedLabel
          }}
        />
      </div>
    </div>
  );
};

export default memo(Header);

BackButton.defaultProps = {
  onGoBack: null
};
