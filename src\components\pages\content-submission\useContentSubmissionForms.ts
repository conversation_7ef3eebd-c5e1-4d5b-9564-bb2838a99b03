import { Layout, MyContentLabels, UploadFileContentLabels, WebsiteContentLabels } from "./ContentDeliverablesTab";
import { ContentTypes } from "./SubmitWebsiteContentModal";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { ContentType } from "@eait-playerexp-cn/metadata-types";
import { useMemo } from "react";
import { useDependency } from "@src/context/DependencyContext";

export type ContentSubmissionForms = {
  websiteModalLabels: {
    title: string;
    formLabels: {
      contentTitle: string;
      contentTitlePlaceholder: string;
      contentUrl: string;
      contentUrlPlaceholder: string;
      contentType: string;
    };
    contentTypes: ContentTypes;
    buttonsLabels: {
      cancel: string;
      submit: string;
      close: string;
    };
    contentSubmissionLabels: {
      contentTitleRequired: string;
      contentUrlRequired: string;
      contentTypeRequired: string;
      duplicateUrlMessage: string;
      unsafeUrlMessage: string;
      validUrlMessage: string;
      websiteUrlMessage: string;
      success: {
        title: string;
        content: string;
      };
      contentTitleLongMessage: string;
    };
    participationId: string;
    thumbnail: string;
    YOUTUBE_HOSTS: string[];
    TWITCH_HOSTS: string[];
    INSTAGRAM_HOSTS: string[];
    FACEBOOK_HOSTS: string[];
    TIKTOK_HOSTS: string[];
  };
  getContentTypes: () => Promise<void>;
  uploadFileModalLabels: {
    title: string;
    formLabels: {
      contentTitle: string;
      contentTitlePlaceholder: string;
      contentType: string;
      fileUpload: string;
      chooseFile: string;
      noFileChoosen: string;
      acceptedFormats: string;
      maxFileSize: string;
      fileSelected: string;
      fileUploading: string;
      removeSelectedFile: string;
      uploadFileProgress: string;
      contentDescription: string;
      contentDescriptionPlaceholder: string;
      maxcharacterLimit: string;
    };
    buttonsLabels: {
      cancel: string;
      upload: string;
      close: string;
    };
    contentSubmissionLabels: {
      contentTitleRequired: string;
      contentTypeRequired: string;
      contentTitleLongMessage: string;
      maxLimitMessage: string;
      invalidFileTypeMessage: string;
      contentDescriptionRequired: string;
      contentDescriptionLongMessage: string;
      success: {
        title: string;
        content: string;
      };
      error: {
        title: string;
        content: string;
      };
    };
  };
};

export const useContentSubmissionForms = (
  websiteContentLabels: WebsiteContentLabels,
  layout: Layout,
  contentTypes: ContentTypes,
  participationId: string,
  thumbnail: string,
  YOUTUBE_HOSTS: string[],
  TWITCH_HOSTS: string[],
  INSTAGRAM_HOSTS: string[],
  FACEBOOK_HOSTS: string[],
  TIKTOK_HOSTS: string[],
  setContentTypes: (types: ContentTypes) => void,
  myContentLabels: MyContentLabels,
  stableDispatch: (action) => void,
  uploadContentLabels: UploadFileContentLabels
): ContentSubmissionForms => {
  const { metadataClient, errorHandler } = useDependency();
  const metadataService = useMemo(() => new MetadataService(metadataClient), [metadataClient]);

  return {
    websiteModalLabels: {
      title: websiteContentLabels.title,
      formLabels: {
        contentTitle: websiteContentLabels.contentTitle,
        contentTitlePlaceholder: websiteContentLabels.contentTitlePlaceholder,
        contentUrl: websiteContentLabels.contentUrl,
        contentUrlPlaceholder: websiteContentLabels.contentUrlPlaceholder,
        contentType: websiteContentLabels.contentType
      },
      contentTypes,
      buttonsLabels: {
        cancel: layout.buttons.cancel,
        submit: layout.buttons.submit,
        close: layout.buttons.close
      },
      contentSubmissionLabels: {
        contentTitleRequired: websiteContentLabels.contentTitleRequired,
        contentUrlRequired: websiteContentLabels.contentUrlRequired,
        contentTypeRequired: websiteContentLabels.contentTypeRequired,
        duplicateUrlMessage: websiteContentLabels.duplicateUrlMessage,
        unsafeUrlMessage: websiteContentLabels.unsafeUrlMessage,
        validUrlMessage: websiteContentLabels.validUrlMessage,
        websiteUrlMessage: websiteContentLabels.websiteUrlMessage,
        success: {
          title: layout.toasts.contentSubmittedTitle,
          content: layout.toasts.contentSubmittedDescription
        },
        contentTitleLongMessage: websiteContentLabels.contentTitleLongMessage
      },
      participationId: participationId,
      thumbnail: thumbnail,
      YOUTUBE_HOSTS: YOUTUBE_HOSTS,
      TWITCH_HOSTS: TWITCH_HOSTS,
      INSTAGRAM_HOSTS: INSTAGRAM_HOSTS,
      FACEBOOK_HOSTS: FACEBOOK_HOSTS,
      TIKTOK_HOSTS: TIKTOK_HOSTS
    },
    getContentTypes: async () => {
      await metadataService
        .getContentTypes()
        .then((types: ContentType[]) => {
          setContentTypes([
            { label: websiteContentLabels.selectContentType, value: "" },
            ...types.map((type: ContentType) => ({ ...type, label: myContentLabels[type.value] }))
          ]);
        })
        .catch((e) => errorHandler(stableDispatch, e));
    },
    uploadFileModalLabels: {
      title: uploadContentLabels.title,
      formLabels: {
        contentTitle: websiteContentLabels.contentTitle,
        contentTitlePlaceholder: websiteContentLabels.contentTitlePlaceholder,
        contentType: websiteContentLabels.contentType,
        fileUpload: uploadContentLabels.fileUpload,
        chooseFile: uploadContentLabels.chooseFile,
        noFileChoosen: uploadContentLabels.noFileChoosen,
        acceptedFormats: uploadContentLabels.acceptedFormats,
        maxFileSize: uploadContentLabels.maxFileSize,
        fileSelected: uploadContentLabels.fileSelected,
        fileUploading: uploadContentLabels.fileUploading,
        removeSelectedFile: uploadContentLabels.removeSelectedFile,
        uploadFileProgress: uploadContentLabels.uploadFileProgress,
        contentDescription: uploadContentLabels.contentDescription,
        contentDescriptionPlaceholder: uploadContentLabels.contentDescriptionPlaceholder,
        maxcharacterLimit: uploadContentLabels.maxcharacterLimit
      },
      buttonsLabels: {
        cancel: layout.buttons.cancel,
        upload: layout.buttons.upload,
        close: layout.buttons.close
      },
      contentSubmissionLabels: {
        contentDescriptionRequired: uploadContentLabels.contentDescriptionRequired,
        contentDescriptionLongMessage: uploadContentLabels.contentDescriptionLongMessage,
        contentTitleRequired: websiteContentLabels.contentTitleRequired,
        contentTypeRequired: websiteContentLabels.contentTypeRequired,
        contentTitleLongMessage: websiteContentLabels.contentTitleLongMessage,
        maxLimitMessage: uploadContentLabels.maxLimitMessage,
        invalidFileTypeMessage: uploadContentLabels.invalidFileTypeMessage,
        success: {
          title: uploadContentLabels.success.title,
          content: uploadContentLabels.success.content
        },
        error: {
          title: uploadContentLabels.error.title,
          content: uploadContentLabels.error.content
        }
      }
    }
  };
};
