import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { createMocks, MockResponse } from "node-mocks-http";
import CommunicationPreferencesPropsController from "@src/serverprops/controllers/CommunicationPreferencesPropsController";
import ContentManagementService from "@src/services/ContentManagementService";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import Random from "../../../factories/Random";

jest.mock("@src/configuration/runtimeConfiguration");
jest.mock("@src/services/ContentManagementService");

describe("CommunicationPreferencesPropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const currentLocale = "en-us";
  let contentService: jest.Mocked<ContentManagementService>;

  beforeEach(() => {
    jest.clearAllMocks();
    contentService = {
      getPageLabels: jest.fn()
    } as any;
  });

  it("returns server side props for Communication Preferences page", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const identity = Identity.fromStored(
      aStoredIdentity({
        programs: [aCreatorProgram({ code: program })]
      })
    );
    req.session = { identity, save: jest.fn() };

    const configuration = {};
    const mockPageLabels = {
      common: {},
      breadcrumbs: {},
      communicationPreferences: {},
      connectAccounts: {}
    };

    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    contentService.getPageLabels.mockResolvedValue(mockPageLabels);

    const controller = new CommunicationPreferencesPropsController(options, contentService, currentLocale, "");

    const result = await controller.handle(req, res);

    expect(result).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pageLabels: mockPageLabels,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        }
      }
    });

    expect(contentService.getPageLabels).toHaveBeenCalledWith(currentLocale, "communicationPreferences");
  });
});
