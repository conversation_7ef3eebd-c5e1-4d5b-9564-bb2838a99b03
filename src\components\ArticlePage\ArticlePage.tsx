import BreadCrumbs, { BreadCrumb } from "@components/BreadCrumbs/BreadCrumbs";
import RichText, { EmbeddedItem, SystemInformation } from "@components/RichText/RichText";

export type ArticleType = "Article";

type ArticlePageProps = {
  sys: SystemInformation;
  title: string;
  body: {
    richText: string;
    embeddedItems: EmbeddedItem[];
  };
  pageBreadCrumbs: BreadCrumb[];
};

const ArticlePage = ({ title, body, pageBreadCrumbs }: ArticlePageProps) => {
  return (
    <div className="article-page-container" data-testid="article-page">
      <div className="article-page-breadCrumb">
        <BreadCrumbs items={pageBreadCrumbs} />
      </div>
      <div className="article-page-content">
        <h1 className="rich-text-heading1">{title}</h1>
        <RichText {...body} />
      </div>
    </div>
  );
};

export default ArticlePage;
