import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import RemoveConnectedAccountController from "@src/server/channels/RemoveConnectedAccountController";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .delete(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const removeAccount = ApiContainer.get(RemoveConnectedAccountController);
    await removeAccount.handle(req, res);
  });

export default router.handler({ onError });
