import HeroSection from "@components/HeroSection/HeroSection";
import CategorySection, { Category } from "@components/CategoryCard/CategoryCard";
import { Image } from "@components/HeroSection/HeroSection";
import { SystemInformation } from "@components/RichText/RichText";
import React from "react";

export type IndexType = "Index";
export type DocumentationPageProps = {
  sys: SystemInformation;
  administrativeTitle: string;
  slug: string;
  title: string;
  description: string;
  image: Image;
  icon: Image;
  categories: Category[];
  entryType: IndexType;
};

const DocumentationPage = ({ title, description, icon, image, categories }: DocumentationPageProps) => {
  return (
    <div className="documentation-page-container" data-testid="documentation-page-container">
      <HeroSection {...{ title, description, icon, image }} />
      <div className="documentation-page-sections">
        {categories.map((category, index) => (
          <React.Fragment key={`category-${index}`}>
            <CategorySection {...category} />
            {index !== categories.length - 1 && (
              <hr className="documentation-page-section-seperator" data-testid="documentation-page-section-separator" />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default DocumentationPage;
