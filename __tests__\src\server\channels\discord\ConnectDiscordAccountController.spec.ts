import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import ConnectDiscordAccountController from "@src/server/channels/discord/ConnectDiscordAccountController";
import DiscordAccountHttpClient from "@src/server/channels/discord/DiscordAccountHttpClient";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "../../../../factories/Random";

describe("ConnectDiscordAccountController", () => {
  let controller: ConnectDiscordAccountController;
  const creatorId = Random.uuid();
  const session = {
    save: jest.fn(),
    identity: Identity.fromStored({
      type: "CREATOR",
      id: creatorId
    } as StoredIdentity)
  };
  const redirectUrl = new URL("https://example.com");

  beforeEach(() => jest.clearAllMocks());

  it("saves discord account", async () => {
    const code = "65e99cb6-b1c7-4bbd-b20d-5b1255f86ceb";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/disord-connect?code=${code}&scopes=email guilds guilds.join gdm.join identify`,
      session: { ...session }
    });
    const connectDiscord = ({ connectDiscordAccount: jest.fn() } as unknown) as DiscordAccountHttpClient;
    controller = new ConnectDiscordAccountController(connectDiscord, redirectUrl);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(connectDiscord.connectDiscordAccount).toHaveBeenCalledWith({
      code,
      creatorId,
      redirectUri: redirectUrl.toString(),
      nucleusId: undefined
    });
    expect(res._getData()).toContain("window.close");
  });

  it("closes the login window when user just cancels it", async () => {
    const code = "";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/disord-connect?code=${code}&error=access_denied`,
      session: { ...session }
    });
    const connectDiscord = ({ connectDiscordAccount: jest.fn() } as unknown) as DiscordAccountHttpClient;
    controller = new ConnectDiscordAccountController(connectDiscord, redirectUrl);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toContain("window.close");
  });
});
