.my-content-container {
  @apply block h-full min-h-screen w-full pb-meas24 pl-meas12 pr-meas16 pt-meas16 font-text-regular text-font-normal md:pb-meas33;
}
.my-content-title {
  @apply mb-meas10 font-display-bold xs:text-mobile-h1 md:text-desktop-h3;
}
.my-content-sub-title {
  @apply mb-meas16 font-text-regular xs:text-desktop-body-small md:text-tablet-body-large lg:text-desktop-body-large;
}
.my-content-franchise-preferences {
  @apply cursor-pointer underline;
}
.my-content-list-container {
  @apply m-auto mb-meas24 mt-meas2 flex-row;
}
.my-content-list {
  @apply px-meas12 pb-meas12 xl:py-meas0;
}
.my-content-list-remove-padding-x {
  @apply px-meas0;
}
.my-content-no-content {
  @apply mt-meas2 flex flex-col items-center justify-between rounded-md border border-card-border bg-section-background-1 p-meas10 xl:flex-row;
}
.my-content-no-content-title {
  @apply mb-meas5 mt-meas8 text-center font-text-bold text-font-normal xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large xl:mb-meas3 xl:ml-meas20 xl:mt-meas0 xl:text-left;
}
.my-content-no-content-desc {
  @apply mb-meas12 max-w-[254px] text-center font-text-regular text-font-normal xs:text-mobile-body-default md:max-w-[597px] md:text-tablet-body-default lg:text-desktop-body-default xl:mb-meas0 xl:ml-meas20 xl:text-left;
}
.my-content-no-content-desc-link {
  @apply lowercase underline;
}
.my-content-no-content-row1 {
  @apply flex flex-col items-center xl:flex-row;
}
.no-my-content-icon {
  @apply h-[70px];
}
.my-content-list .opportunity-content-card-container {
  @apply bg-page px-meas0 py-meas12;
}
