import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import ApiContainer from "@src/ApiContainer";
import ViewOpportunityParticipationStatusController from "@src/server/opportunities/ViewOpportunityParticipationStatusController";
import onError from "@src/middleware/JsonErrorHandler";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .post(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
    const controller = ApiContainer.get(ViewOpportunityParticipationStatusController);
    return await controller.handle(req, res);
  });

export default router.handler({ onError });
