import { documentation, faqs, grid, myContent, opportunities, SidePanel } from "@eait-playerexp-cn/core-ui-kit";
import React, { memo } from "react";
import { useRouter } from "next/router";
import { useDetectScreen } from "@src/utils";

export default memo(function ProgramSideNavigation() {
  const router = useRouter();
  const isMobileAndTablet = useDetectScreen(1024);
  const simsButtons = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: grid,
      onClick: () => router.push("/dashboard"),
      active: router.pathname === "/dashboard"
    },
    {
      id: "opportunities",
      label: "Opportunities",
      icon: opportunities,
      onClick: () => router.push("/opportunities"),
      active: typeof router.pathname === "string" && router.pathname.startsWith("/opportunities")
    },
    {
      id: "my_content",
      label: "My Content",
      icon: myContent,
      onClick: () => router.push("/my-content"),
      active: router.pathname === "/my-content"
    },
    {
      id: "documentation",
      label: "Documentation",
      icon: documentation,
      onClick: () => router.push("/documentation"),
      active: router.pathname === "/documentation"
    },
    {
      id: "faqs",
      label: "FAQs",
      icon: faqs,
      onClick: () => router.push("/faq"),
      active: router.pathname === "/faq"
    }
  ];

  return !isMobileAndTablet ? <SidePanel buttons={simsButtons} /> : <></>;
});
