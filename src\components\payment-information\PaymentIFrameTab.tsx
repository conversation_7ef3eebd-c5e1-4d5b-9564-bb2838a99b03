import React, { FC, memo, useEffect, useMemo } from "react";
import Loading from "../loading/Loading";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { PaymentInformationPageLabels } from "@src/server/contentManagement/PaymentInformationPageMapper";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import CreatorWithCreatorCode from "@src/server/creators/CreatorWithCreatorCode";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useRouter } from "next/router";
import { Dispatch, ErrorHandling } from "@src/utils/types";
import { LOADING } from "@src/utils";
import { AxiosError } from "axios";
import { Footer } from "@eait-playerexp-cn/onboarding-ui";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import { useDependency } from "@src/context/DependencyContext";

export type PaymentIFrameType = {
  pageLabels: PaymentInformationPageLabels & CommonPageLabels;
  embeddableUrl?: string;
  isLoading?: boolean;
  client: TraceableHttpClient;
  creator: CreatorWithCreatorCode;
  stableDispatch: Dispatch;
  errorHandler: ErrorHandling;
  onClose: () => void;
};

export class TipaltiEvent extends Event {
  data: {
    TipaltiIframeInfo: {
      height: number;
    };
  };
}

const PaymentIFrameTab: FC<PaymentIFrameType> = ({
  isLoading,
  embeddableUrl,
  pageLabels,
  creator,
  stableDispatch,
  errorHandler,
  onClose
}) => {
  const {
    configuration: { PROGRAM_CODE, DEFAULT_AVATAR_IMAGE },
    creatorsClient
  } = useDependency();
  const router = useRouter();
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE), [creatorsClient]);
  const { paymentInformationLabels, commonPageLabels } = pageLabels;

  const tipaltiHandler = (event: TipaltiEvent) => {
    const iFrame = document.querySelector(".iframe-container");
    if (event.data?.TipaltiIframeInfo?.height && iFrame) {
      iFrame.setAttribute("height", `${event.data.TipaltiIframeInfo.height}px`);
    }
  };

  useEffect(() => {
    if (window.addEventListener) {
      window.addEventListener("message", tipaltiHandler, false);
    }
    return () => window.removeEventListener("message", tipaltiHandler, false);
  }, []);

  const onNextClick = async () => {
    try {
      stableDispatch({ type: LOADING, data: true });
      const updatedAccountInformation = {
        ...creator.accountInformation,
        dateOfBirth: LocalizedDate.format(
          ((creator.accountInformation.dateOfBirth as unknown) as LocalizedDate).toDate(),
          "YYYY-MM-DD"
        )
      };
      const requestPayload = {
        accountInformation: { ...updatedAccountInformation },
        program: { code: PROGRAM_CODE, status: "ACTIVE" }
      };
      await creatorService.updateCreator((requestPayload as unknown) as UpdateCreatorRequest);
      stableDispatch({ type: LOADING, data: false });
      router.push("/signup-complete");
    } catch (e) {
      stableDispatch({ type: LOADING, data: false });
      errorHandler(stableDispatch, e as Error | AxiosError);
    }
  };

  const buttons = {
    next: paymentInformationLabels.done,
    cancel: commonPageLabels.cancel
  };

  return isLoading ? (
    <div className="loader">
      <Loading />
    </div>
  ) : (
    <div className="onboarding-payment-settings">
      <h3 className="onboarding-payment-information-title">{commonPageLabels.paymentInformation}</h3>
      <div className="onboarding-payment-information-description">
        {paymentInformationLabels.paymentSettingsDescription}
      </div>
      {embeddableUrl && (
        <section className="onboarding-payment-iframe-cont">
          <iframe
            data-testid="iframe-container"
            className="onboarding-iframe-container"
            src={embeddableUrl}
            title="Payment IFrame"
          ></iframe>
        </section>
      )}
      <Footer onSave={onNextClick} disableSubmit={false} buttons={buttons} onCancel={onClose} />
    </div>
  );
};

export default memo(PaymentIFrameTab);
