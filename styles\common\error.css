.erro-mg-container {
  @apply absolute z-0 mt-meas19 flex min-h-full w-full items-center justify-center overflow-y-hidden bg-[#182FFF] p-meas5 xs:overflow-x-hidden;
}

.error-mg-bg {
  @apply absolute h-full w-full bg-error-background-image bg-contain bg-bottom bg-no-repeat xl:bg-error-background-image xl:bg-center;
  z-index: -1;
  background-size: 100%;
}
.error-sub-container {
  @apply block max-w-[290px] rounded border border-none bg-white pb-meas14 pl-meas8 pr-meas8 pt-meas7 text-center  xs:pb-meas25 md:min-h-[605px] md:max-w-[570px] md:max-w-[640px] md:pb-meas25 md:pl-meas17 md:pr-meas15 md:pt-[80px] md:pt-meas15 xl:min-h-[605px] xl:max-w-[570px] xl:pt-[80px];
}
.error-title {
  @apply mb-meas2 mt-meas15 font-title-font xs:text-mobile-h2 md:mb-meas15 md:mt-meas8  lg:text-desktop-h2;
}
.error-sub-title {
  @apply mb-meas10 font-title-font xs:text-mobile-h3 md:mb-meas15 md:text-tablet-h4 lg:text-desktop-h4;
}
.error-image {
  @apply m-auto mb-meas16 max-w-[45px] md:mb-meas15 md:max-w-[72px];
}
.error-description {
  @apply text-center font-text-regular text-black xs:text-mobile-body-small md:text-tablet-body-large lg:text-desktop-body-large;
}

.account-deactivated-container {
  @apply max-w-[630px]  p-meas12 text-center text-white;
}
.account-deactivated-title {
  @apply mb-meas15 font-title-font xs:text-mobile-h1 md:text-desktop-h2 lg:text-desktop-h2;
}
.account-deactivated-content {
  @apply mb-meas12 font-text-regular xs:text-desktop-body-small md:text-desktop-body-large lg:text-desktop-body-large;
}

.age-restriction-container {
  @apply relative flex min-h-screen w-full flex-col items-center overflow-hidden bg-[#182FFF];
}

.access-error-container {
  @apply relative flex min-h-screen w-full flex-col items-center overflow-hidden;
}

.access-error-bg {
  @apply absolute h-full w-full bg-access-error-desktop-background-image bg-cover bg-bottom bg-no-repeat 2xl:bg-access-error-desktop-background-image;
  z-index: -1;
}

.age-restriction-bg {
  @apply absolute h-full w-full bg-migration-background-tablet bg-contain bg-bottom bg-no-repeat  2xl:bg-migration-shape;
  z-index: 0;
}
@media (min-width: 1441px) {
  .age-restriction-bg {
    background-image: var(--migration-background);
    background-size: cover;
    background-position: center;
  }
}

.age-restriction-header {
  @apply z-10 mt-meas20 hidden w-full px-meas8 py-meas4 md:block;
  align-self: flex-start;
}
.login-app-header {
  @apply z-10 flex w-full items-center px-meas8 py-meas4;
}

.sims-creator-logo {
  @apply ml-meas4;
}

.age-restriction-main-content,
.access-error-main-content {
  @apply z-10 mt-[-292px] flex flex-grow flex-col items-center justify-center;
}

.age-restriction-character-section {
  @apply flex items-center justify-center;
}

.age-restriction-characters {
  @apply object-contain xs:h-[183px] xs:w-[230px] md:h-[293px] md:w-[344px];
}

.age-restriction-content-container,
.access-error-content-container {
  @apply flex flex-col items-center justify-center px-meas4 text-center text-white;
}

.age-restriction-title,
.access-error-title {
  @apply mb-meas15 font-title-font text-mobile-display md:text-desktop-h1;
}

.age-restriction-body,
.access-error-body {
  @apply font-text-bold text-mobile-h3 md:text-desktop-h4;
}

.sims-logo {
  @apply absolute left-meas4 z-[100] hidden md:block;
  top: 0;
}
.horizontal-line {
  @apply z-10 h-meas1 w-full bg-white opacity-30;
}

.top-line {
  @apply mb-meas15;
}

.bottom-line {
  @apply mt-meas15;
}

.access-error-header {
  @apply mt-meas8;
}
.age-restriction-content,
.access-error-content {
  @apply mt-[320px] justify-start xs:px-meas10;
}
.account-deactivated-header {
  @apply mt-meas8;
}
