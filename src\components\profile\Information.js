import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import Form from "../Form";
import { CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import MailingAddressForm from "./forms/MailingAddressForm";
import PersonalInformationForm from "./forms/PersonalInformationForm";
import MiscellaneousForm from "./forms/MiscellaneousForm";
import ProfileCard from "./ProfileCard";
import CreatorForm from "../FormRules/CreatorForm";
import LegalEntityForm from "./forms/LegalEntityForm";
import Loading from "../Loading";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "../../utils";
import { useRouter } from "next/router";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { useDependency } from "../../context/DependencyContext";

export default memo(function Information({
  infoLabels,
  buttons,
  user,
  creator,
  updateCreator,
  hardwarePartners,
  countries,
  layout,
  analytics,
  allCountries
}) {
  const { errorHandler, creatorsClient, configuration: config } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const router = useRouter();
  const { dispatch, state: { isValidationError, validationErrors, isError } = {} } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const [isAccountInformationSaved, setIsAccountInformationSaved] = useState(false);
  const [isMailingAddressSaved, setIsMailingAddressSaved] = useState(false);
  const [isAdditionalInformationSaved, setIsAdditionalInformationSaved] = useState(false);
  const [isLegalEntitySaved, setIsLegalEntitySaved] = useState(false);
  const [accountInformation, setAccountInformation] = useState(null);
  const [mailingAddress, setMailingAddress] = useState(null);
  const [registrationDate, setRegistrationDate] = useState(null);
  const [legalEntity, setLegalEntity] = useState(true);
  const [additionalInformation, setAdditionalInformation] = useState(null);
  const rules = useMemo(() => CreatorForm.rules(infoLabels), [infoLabels]);
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, config.DEFAULT_AVATAR_IMAGE), [
    creatorsClient
  ]);

  const submitAccountInformation = useCallback(
    async (data) => {
      try {
        const formData = { ...accountInformation, ...data };
        const updatedAccountInformation = {
          ...formData,
          dateOfBirth: LocalizedDate.format(formData.dateOfBirth, "YYYY-MM-DD")
        };
        await creatorService.updateCreator({
          accountInformation: updatedAccountInformation,
          program: { code: config.PROGRAM_CODE }
        });
        analytics.updatedBasicInformation({ locale: router.locale });
        formData.dateOfBirth = LocalizedDate.fromFormattedDate(formData.dateOfBirth);
        setAccountInformation(formData);
        creator.accountInformation = formData;
        updateCreator(creator);
        setIsAccountInformationSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isAccountInformationSaved, accountInformation, stableDispatch]
  );
  const submitMailingAddress = useCallback(
    async (data) => {
      try {
        const updatedMailingAddress = {
          ...data,
          country: {
            code: data.country.value,
            name: data.country.name
          }
        };
        await creatorService.updateCreator({
          mailingAddress: updatedMailingAddress,
          program: { code: config.PROGRAM_CODE }
        });

        analytics.updatedBasicInformation({ locale: router.locale });
        setMailingAddress(data);
        creator.mailingAddress = data;
        updateCreator(creator);
        setIsMailingAddressSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isMailingAddressSaved, stableDispatch]
  );
  const submitAdditionalInformation = useCallback(
    async (data) => {
      try {
        const updatedAdditionalInformation = {
          hardwarePartners: data.hardwarePartners.map((item) => {
            return {
              id: item.value,
              name: item.label
            };
          }),
          hoodieSize: data.hoodieSize.value
        };
        await creatorService.updateCreator({
          additionalInformation: updatedAdditionalInformation,
          program: { code: config.PROGRAM_CODE }
        });

        analytics.updatedBasicInformation({ locale: router.locale });
        data.hardwarePartners = data.hardwarePartners.map((item) => {
          return {
            ...item,
            id: item.value,
            name: item.label
          };
        });
        data.hoodieSize = data.hoodieSize.value;
        setAdditionalInformation(data);
        creator.additionalInformation = data;
        updateCreator(creator);
        setIsAdditionalInformationSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isAdditionalInformationSaved, stableDispatch]
  );
  const submitLegalEntity = useCallback(
    async (data) => {
      try {
        const updatedLegalInformation = {
          ...data,
          country: {
            code: data.country.value,
            name: data.country.name
          },
          entityType: data.entityType.value
        };
        await creatorService.updateCreator({
          legalInformation: updatedLegalInformation,
          program: { code: config.PROGRAM_CODE }
        });

        analytics.updatedBasicInformation({ locale: router.locale });
        setLegalEntity(data);
        creator.legalEntity = data;
        updateCreator(creator);
        setIsLegalEntitySaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [isLegalEntitySaved, stableDispatch]
  );
  const { pending: pendingAccUpd, execute: onSubmitAccountInformation } = useAsync(submitAccountInformation, false);
  const { pending: pendingMailAddUpd, execute: onSubmitMailingAddress } = useAsync(submitMailingAddress, false);
  const { pending: pendingAddInfoUpd, execute: onSubmitAdditionalInformation } = useAsync(
    submitAdditionalInformation,
    false
  );
  const { pending: pendingLegAddUpd, execute: onSubmitLegalEntity } = useAsync(submitLegalEntity, false);

  const accountInformationOnChange = useCallback(() => setIsAccountInformationSaved(false), []);
  const mailingAddressOnChange = useCallback(() => setIsMailingAddressSaved(false), []);
  const additionalInformationOnChange = useCallback(() => setIsAdditionalInformationSaved(false), []);
  const legalEntityOnChange = useCallback(() => setIsLegalEntitySaved(false), []);

  useEffect(() => {
    setAccountInformation(creator.accountInformation);
    setRegistrationDate(creator.formattedRegistrationDate(router.locale));
    setMailingAddress(creator.mailingAddress);
    setAdditionalInformation(creator.additionalInformation);
    setLegalEntity(creator.legalInformation);
  }, [creator]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(validationErrors)}
          closeButtonAriaLabel={buttons.close}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  const onChangeAsMailingAddress = useCallback(
    (data, isChecked) => {
      isChecked && setLegalEntity({ ...data, ...mailingAddress });
    },
    [mailingAddress]
  );

  return (
    (!legalEntity && (
      <div className="loader">
        <Loading />
      </div>
    )) ||
    (legalEntity && (
      <div className="profile-information">
        {accountInformation && (
          <ProfileCard
            {...{
              labels: { ...infoLabels, buttons: buttons },
              user,
              registrationDate,
              accountInformation,
              data: layout?.toolTip?.badge,
              locale: router.locale,
              stableDispatch
            }}
          />
        )}
        {rules && accountInformation && (
          <Form key="personal" mode="onChange" onSubmit={onSubmitAccountInformation}>
            <PersonalInformationForm
              {...{
                infoLabels,
                rules,
                accountInformation,
                buttons,
                onChange: accountInformationOnChange,
                isSaved: isAccountInformationSaved,
                isLoader: pendingAccUpd
              }}
            />
          </Form>
        )}
        {allCountries && mailingAddress && rules && (
          <Form key="mailing" mode="onChange" onSubmit={onSubmitMailingAddress}>
            <MailingAddressForm
              {...{
                infoLabels,
                rules,
                mailingAddress,
                allCountries,
                buttons,
                onChange: mailingAddressOnChange,
                isSaved: isMailingAddressSaved,
                isLoader: pendingMailAddUpd
              }}
            />
          </Form>
        )}
        {countries && legalEntity && rules && (
          <Form key="legalEntity" mode="onChange" revalidate="onChange" onSubmit={onSubmitLegalEntity}>
            <LegalEntityForm
              {...{
                infoLabels,
                rules,
                legalEntity,
                countries,
                buttons,
                onChangeAsMailingAddress,
                onChange: legalEntityOnChange,
                isSaved: isLegalEntitySaved,
                isLoader: pendingLegAddUpd
              }}
            />
          </Form>
        )}
        {hardwarePartners && additionalInformation && rules && (
          <Form key="miscellaneous" mode="onChange" onSubmit={onSubmitAdditionalInformation}>
            <MiscellaneousForm
              {...{
                infoLabels,
                rules,
                additionalInformation,
                hardwarePartners,
                buttons,
                onChange: additionalInformationOnChange,
                isSaved: isAdditionalInformationSaved,
                isLoader: pendingAddInfoUpd
              }}
            />
          </Form>
        )}
      </div>
    ))
  );
});
