import { ReactNode } from "react";
import { ImageType, Media } from "../RichText/RichText";
import Image from "@components/RichTextComponents/Image/Image";

type EmbeddedObjectsProps = {
  entryType: ImageType;
  media: Media;
};

export type RendererFunction = (media: Media) => ReactNode;

const renderers: Record<string, RendererFunction> = {
  Image: (media) => <Image {...media} id="richText-image" className="my-meas16 flex items-start" />
};

const EmbeddedObjects = ({ entryType, media }: EmbeddedObjectsProps) => {
  return renderers[entryType] ? renderers[entryType](media) : null;
};

export default EmbeddedObjects;
