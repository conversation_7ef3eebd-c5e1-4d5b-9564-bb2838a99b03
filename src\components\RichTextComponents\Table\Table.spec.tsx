import { render, screen } from "@testing-library/react";
import Table from "./Table";
import { useDetectScreen } from "@src/utils";
import { axe } from "jest-axe";

jest.mock("@src/utils", () => ({
  useDetectScreen: jest.fn()
}));

describe("Table", () => {
  const tableData = (
    <table>
      <tr>
        <th>Column Heading</th>
      </tr>
      <tr>
        <td>Row Text</td>
      </tr>
    </table>
  );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows desktop table view", () => {
    (useDetectScreen as jest.Mock).mockReturnValue(false);

    render(<Table>{tableData}</Table>);

    expect(screen.getByTestId("rich-text-table-desktop")).toBeInTheDocument();
  });

  it("shows mobile table view with all cells", () => {
    (useDetectScreen as jest.Mock).mockReturnValue(true);

    render(<Table>{tableData}</Table>);

    expect(screen.getByTestId("rich-text-table-mobile")).toBeInTheDocument();
  });

  it("shows each cell on mobile", () => {
    (useDetectScreen as jest.Mock).mockReturnValue(true);

    render(
      <Table>
        <tr>
          <th>
            <p>Header 1</p>
          </th>
          <th>
            <p>Header 2</p>
          </th>
        </tr>
        <tr>
          <td>
            <p>Cell 1</p>
          </td>
          <td>
            <p>Cell 2</p>
          </td>
        </tr>
        <tr>
          <td>
            <p>Cell 3</p>
          </td>
          <td>
            <p>Cell 4</p>
          </td>
        </tr>
      </Table>
    );

    expect(screen.getByRole("columnheader", { name: "Header 1" })).toBeInTheDocument();
    expect(screen.getByRole("cell", { name: "Cell 1" })).toBeInTheDocument();
    expect(screen.getByRole("cell", { name: "Cell 3" })).toBeInTheDocument();
    expect(screen.getByRole("columnheader", { name: "Header 2" })).toBeInTheDocument();
    expect(screen.getByRole("cell", { name: "Cell 2" })).toBeInTheDocument();
    expect(screen.getByRole("cell", { name: "Cell 4" })).toBeInTheDocument();
  });

  it("is accessible", async () => {
    (useDetectScreen as jest.Mock).mockReturnValue(false);
    const { container } = render(<Table>{tableData}</Table>);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
