import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { PaymentsCriteria, PaymentsPayload } from "../paymentsStatistics/PaymentsService";
import PaymentHistoryDetails from "./PaymentHistoryDetails";
import { AxiosResponse } from "axios";
import { PaymentsIFrameUrlPayload } from "@components/pages/payment-information/usePayableStatus";

export type AmountUnit = "B" | "M" | "K" | "";
export type CurrencySymbol = "$";
export type Amount = {
  integralPart: string;
  decimalPart: string;
};

class PaymentInformationService {
  constructor(private readonly client: TraceableHttpClient) {}

  getPaymentsHistoryWithProgramCode = async (criteria: PaymentsCriteria): Promise<PaymentHistoryDetails> => {
    const criteriaPayload = {
      ...criteria,
      startDate: criteria.startDate.formatWithEpoch("MM/DD/YYYY"),
      endDate: criteria.endDate.formatWithEpoch("MM/DD/YYYY")
    } as PaymentsPayload;
    const response = await this.client.get("/api/v3/payments-history", { query: criteriaPayload });
    return new PaymentHistoryDetails(response.data);
  };

  getPaymentsIFrameUrl = async (
    data: PaymentsIFrameUrlPayload
  ): Promise<AxiosResponse<{ id?: string; embeddableUrl?: string }>> => {
    return (await this.client.post("/api/payment-information", { body: data })) as AxiosResponse<{
      id?: string;
      embeddableUrl?: string;
    }>;
  };
}

export default PaymentInformationService;
