.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper,
.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper {
  @apply bg-[transparent];
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-dropdown-icon-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon,
.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-dropdown-icon-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon {
  @apply h-meas12 w-meas12;
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper {
  @apply w-auto;
}

.topnav-child-container .notifications-bell-container .notification-counter {
  @apply top-meas0 md:left-meas6 md:right-meas0 md:top-meas0 lg:top-meas4;
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-dropdown-icon-container
  > .notification-bell-icon-wrapper {
  @apply w-auto;
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon
  > path,
.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon
  > path {
  @apply fill-[var(--color-font-disabled)];
}

.topnav-child-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-bell-icon-wrapper
  > .notification-bell-icon:hover
  > path {
  @apply fill-[var(--color-font-weak)];
}

.topnav-child-container > .notifications-bell-container > .notification-dropdown-container {
  @apply mr-meas0;
}

.topnav-mobile-child-container
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container {
  @apply ml-meas7 mr-meas0 w-auto;
}

.topnav-mobile-child-container > .notificaion-bell-parent-container {
  @apply flex items-center;
}

.topnav-mobile-child-container.topnav-mobile-active-child
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container {
  @apply ml-meas3;
}

.topnav-mobile-child-container > .notificaion-bell-parent-container > .notifications-bell-container {
  @apply ml-meas0;
}

.topnav-mobile-child-container.topnav-mobile-active-child
  > .notificaion-bell-parent-container
  > .notifications-bell-container
  > .notification-dropdown-container
  > .notification-dropdown-icon-container
  > .notification-bell-icon-wrapper {
  @apply bg-section-background-highlight-1;
}

.topnav-mobile-child-container.topnav-mobile-active-child {
  @apply mr-meas8 rounded-bl-none rounded-br-md rounded-tl-none rounded-tr-md border-l-8 border-solid border-l-primary bg-section-background-highlight-1;
}

.topnav-child-container.topnav-active-child > .notifications-bell-container > .notification-dropdown-container::after {
  @apply absolute bottom-meas0 h-meas2 w-meas24 self-center bg-primary opacity-0 transition-opacity duration-200;
  content: "";
}

.topnav-child-container.topnav-active-child > .notifications-bell-container > .notification-dropdown-container::after {
  @apply opacity-100;
}

.notification-bell-text {
  @apply text-left font-text-bold text-font-normal xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.topnav-navigation-button-image {
  @apply ml-meas0 h-meas20 w-meas20 cursor-pointer rounded-[40px];
}

.topnav-navigation-button-image:hover {
  @apply border-2 border-card-border;
}

.topnav-signup-page-request-to-join,
.topnav-signup-page-request-to-join:focus {
  @apply text-font-normal;
}

.topnav-signup-page-request-to-join:active {
  @apply bg-[transparent] text-font-normal;
}

.topnav-signup-page-apply-button {
  @apply inline-block w-[137px];
}

.topnav-signup-button-container {
  @apply flex gap-meas11;
}
