import { screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";
import { enterValueFor } from "../../helpers/forms";
import { renderPage } from "../../helpers/page";
import BrowserAnalytics from "../../../src/analytics/BrowserAnalytics";
import PointOfContactEmailModal from "@src/components/profile/PointOfContactEmailModal";
import "next/config";
import { delay } from "../../helpers/timer";
import { useDependency } from "@src/context/DependencyContext";
import PointOfContactService from "@src/services/PointOfContactService";

jest.mock("../../../analytics/browser/src/ampli", () => ({
  ampli: { identify: jest.fn(), receivedErrorMessage: jest.fn() }
}));
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));
jest.mock("next/config");
jest.mock("../../../src/services/PointOfContactService");
jest.mock("../../../src/context/DependencyContext");

describe("PointOfContactEmailModal", () => {
  const pointOfContactEmailModalProps = {
    pocLabels: {
      buttons: { send: "Send", cancel: "Cancel" },
      labels: { body: "Message", subject: "Subject" },
      messages: {
        body: "Please enter an email message",
        bodyTooLong: "An email subject is too long",
        subject: "Please enter an email subject",
        subjectTooLong: "An email subject is too long"
      },
      subjectPlaceholder: "subjectPlaceholder",
      success: {
        modalHeader: "Email successfully sent",
        modalMessage: "You have successfully sent  an email."
      },
      title: "Send Email"
    },
    pocName: "Check",
    buttons: { close: "Close" },
    title: "",
    analytics: ({} as unknown) as BrowserAnalytics,
    closeModal: jest.fn()
  };
  const subject = "Need help filling my Profile";
  const message = "Cannot complete my payments settings";
  const creator = { id: "124789", pointOfContactName: "TestUser" };
  const errorHandler = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      errorHandler,
      configuration: { PROGRAM_CODE: "sims_creator_program" },
      client: {}
    });
  });

  it("shows modal title", async () => {
    renderPage(<PointOfContactEmailModal {...pointOfContactEmailModalProps} />);

    expect(screen.getByRole("heading", { name: "Send Email" })).toBeInTheDocument();
  });

  it("sends an email to an  of contact", async () => {
    const pointOfContactService = ({
      sendEmailToPOC: jest.fn().mockImplementation(() => Promise.resolve())
    } as unknown) as PointOfContactService;
    (PointOfContactService as jest.Mock).mockReturnValue(pointOfContactService);
    const analytics = ({ emailSentToPoc: jest.fn() } as unknown) as BrowserAnalytics;
    const { unmount } = renderPage(
      <PointOfContactEmailModal {...pointOfContactEmailModalProps} analytics={analytics} creator={creator} />
    );
    await enterValueFor(/^Subject/i, subject);
    await enterValueFor(/Message/i, message);

    await userEvent.click(screen.getByRole("button", { name: "Send" }));

    await waitFor(() => {
      expect(analytics.emailSentToPoc).toHaveBeenCalledWith({ locale: "en-us" });
      expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledTimes(1);
      expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledWith({
        subject,
        body: message,
        creatorId: creator.id,
        creatorProgram: "sims_creator_program",
        opportunityId: null
      });
      expect(pointOfContactEmailModalProps.closeModal).toHaveBeenCalled();
    });
    unmount(); // Remove toast message
  });

  it("displays success toast when email is sent to POC", async () => {
    const pointOfContactService = ({
      sendEmailToPOC: jest.fn().mockImplementation(() => Promise.resolve())
    } as unknown) as PointOfContactService;
    (PointOfContactService as jest.Mock).mockReturnValue(pointOfContactService);
    const analytics = ({ emailSentToPoc: jest.fn() } as unknown) as BrowserAnalytics;
    const { unmount } = renderPage(
      <PointOfContactEmailModal {...pointOfContactEmailModalProps} creator={creator} analytics={analytics} />
    );
    await enterValueFor(/^Subject/i, subject);
    await enterValueFor(/Message/i, message);

    await userEvent.click(screen.getByRole("button", { name: "Send" }));

    expect(await screen.findByText(pointOfContactEmailModalProps.pocLabels.success.modalHeader)).toBeInTheDocument();
    expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledTimes(1);
    expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledWith({
      subject,
      body: message,
      creatorId: creator.id,
      creatorProgram: "sims_creator_program",
      opportunityId: null
    });
    expect(await screen.findByText(/point-of-contact:success.modalMessage/i)).toBeInTheDocument();
    expect(pointOfContactEmailModalProps.closeModal).toHaveBeenCalled();
    unmount(); // Remove toast message
  });

  it("disables the 'Cancel' and 'Close' buttons, when clicking on 'Send' button in the email popup", async () => {
    const pointOfContactService = ({
      sendEmailToPOC: jest.fn().mockImplementation(() => delay(1000).then(() => Promise.reject()))
    } as unknown) as PointOfContactService;
    (PointOfContactService as jest.Mock).mockReturnValue(pointOfContactService);
    const analytics = ({ emailSentToPoc: jest.fn() } as unknown) as BrowserAnalytics;
    const { unmount } = renderPage(
      <PointOfContactEmailModal {...pointOfContactEmailModalProps} creator={creator} analytics={analytics} />
    );
    await enterValueFor(/^Subject/i, subject);
    await enterValueFor(/Message/i, message);

    await userEvent.click(screen.getByRole("button", { name: "Send" }));

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /Cancel/i })).toBeDisabled();
      expect(screen.getByRole("button", { name: /Close/i })).toBeDisabled();
    });
    await waitFor(() => {
      expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledTimes(1);
      expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledWith({
        subject,
        body: message,
        creatorId: creator.id,
        creatorProgram: "sims_creator_program",
        opportunityId: null
      });
      expect(pointOfContactEmailModalProps.closeModal).toHaveBeenCalled();
    });
    unmount(); // Remove toast message
  });

  it("sends an email to an opportunity point of contact", async () => {
    const pointOfContactService = ({
      sendEmailToPOC: jest.fn().mockImplementation(() => Promise.resolve())
    } as unknown) as PointOfContactService;
    (PointOfContactService as jest.Mock).mockReturnValue(pointOfContactService);
    const opportunityId = "989889";
    const analytics = ({ emailSentToPoc: jest.fn() } as unknown) as BrowserAnalytics;
    const { unmount } = renderPage(
      <PointOfContactEmailModal
        {...pointOfContactEmailModalProps}
        analytics={analytics}
        creator={creator}
        opportunityId={opportunityId}
      />
    );
    await enterValueFor(/^Subject/i, subject);
    await enterValueFor(/Message/i, message);

    await userEvent.click(screen.getByRole("button", { name: "Send" }));

    await waitFor(() => {
      expect(analytics.emailSentToPoc).toHaveBeenCalledWith({ locale: "en-us" });
      expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledTimes(1);
      expect(pointOfContactService.sendEmailToPOC).toHaveBeenCalledWith({
        subject,
        body: message,
        creatorId: creator.id,
        creatorProgram: "sims_creator_program",
        opportunityId
      });
      expect(pointOfContactEmailModalProps.closeModal).toHaveBeenCalled();
    });
    unmount(); // Remove toast message
  });

  it("closes the modal when API fails", async () => {
    const pointOfContactService = ({
      sendEmailToPOC: jest.fn().mockImplementation(() => delay(1000).then(() => Promise.reject()))
    } as unknown) as PointOfContactService;
    (PointOfContactService as jest.Mock).mockReturnValue(pointOfContactService);
    const analytics = ({ emailSentToPoc: jest.fn() } as unknown) as BrowserAnalytics;
    const { unmount } = renderPage(
      <PointOfContactEmailModal {...pointOfContactEmailModalProps} creator={creator} analytics={analytics} />
    );
    await enterValueFor(/^Subject/i, subject);
    await enterValueFor(/Message/i, message);

    await userEvent.click(screen.getByRole("button", { name: "Send" }));

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /Cancel/i })).toBeDisabled();
      expect(screen.getByRole("button", { name: /Close/i })).toBeDisabled();
    });
    await waitFor(
      () => {
        expect(screen.getByRole("button", { name: /Cancel/i })).toBeEnabled();
        expect(screen.getByRole("button", { name: /Close/i })).toBeEnabled();
        expect(errorHandler).toHaveBeenCalledTimes(1);
        expect(pointOfContactEmailModalProps.closeModal).toHaveBeenCalled();
      },
      { timeout: 1_500 }
    );
    unmount(); // Remove toast message
  });
});
