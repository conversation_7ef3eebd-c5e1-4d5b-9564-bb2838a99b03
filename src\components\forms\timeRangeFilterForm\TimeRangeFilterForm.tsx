import React, { FC, memo, useCallback, useEffect, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { Button, close, DateInput, Icon, Select } from "@eait-playerexp-cn/core-ui-kit";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import Form from "../Form";
import { useAppContext } from "@src/context";
import { useRouter } from "next/router";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import Filter from "@components/icons/Filter";
import { LOADING } from "@src/utils";
import { DefaultPaymentDateRange } from "@src/services/paymentsStatistics/PaymentsService";

export type FilterLabels = {
  filters: string;
  dateRange: string;
  startDate: string;
  endDate: string;
  paymentStatus: string;
  programCode?: string;
  applyFilters: string;
  startDateRequired: string;
  endDateRequired: string;
  startDateError: string;
  endDateError: string;
  ok: string;
  cancel: string;
  calendar: string;
};

export type FilterButtonProps = {
  filterLabels: FilterLabels;
  isPending?: boolean;
  startDateErrorMessage: string;
  endDateErrorMessage: string;
};

export type SelectOptions = {
  label: string;
  value: string;
}[];

export type PaymentFilterProps = {
  label: string;
  value: string;
  code: string;
};

export type PaymentStatusType = "PAID" | "PENDING" | "ALL";
export type OpportunityType = "marketing_opportunity" | "support_a_creator" | "ALL";
export type PaymentsCriteriaProperties = {
  status?: PaymentStatusType;
  opportunityType?: OpportunityType;
  page: number;
  size: number;
};

export type PaymentsCriteria = PaymentsCriteriaProperties & {
  startDate: LocalizedDate;
  endDate: LocalizedDate;
};

export type TimeRangeFilterFormProps = {
  filterLabels: FilterLabels;
  dateRangeOptions: SelectOptions;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  PAYMENTS_DEFAULT_START_DATE: string;
  selectedFilters: PaymentFilterProps[];
  selectedCriteria: PaymentsCriteria;
  updatePaymentsFilterDetails: (formData) => void;
  analytics: BrowserAnalytics;
  format?: string;
  paymentStatusOptions?: SelectOptions;
  programCodeOptions?: SelectOptions;
  isStatusRequired?: boolean;
  isProgramCodeRequired?: boolean;
  isDateDisabled?: boolean;
};

export type TimeRangeFormProps = {
  filterLabels: FilterLabels;
  dateRangeOptions: SelectOptions;
  startDateErrorMessage: string;
  setStartDateErrorMessage: (message: string) => void;
  endDateErrorMessage: string;
  setEndDateErrorMessage: (message: string) => void;
  defaultPaymentDateRange: DefaultPaymentDateRange;
  PAYMENTS_DEFAULT_START_DATE: string;
  selectedFilters: PaymentFilterProps[];
  selectedCriteria: PaymentsCriteria;
  analytics: BrowserAnalytics;
  format?: string;
  paymentStatusOptions?: SelectOptions;
  programCodeOptions?: SelectOptions;
  isStatusRequired?: boolean;
  isProgramCodeRequired?: boolean;
  isDateDisabled?: boolean;
};

export const FilterButton = memo(function FilterButton({
  filterLabels,
  isPending,
  startDateErrorMessage,
  endDateErrorMessage
}: FilterButtonProps) {
  const { formState } = useFormContext();

  return (
    <div className="time-range-filter-footer">
      <Button
        type="submit"
        spinner={isPending}
        size="sm"
        disabled={
          Object.keys(formState.errors).length !== 0 ||
          formState.isValid === false ||
          isPending ||
          startDateErrorMessage !== "" ||
          endDateErrorMessage !== ""
        }
      >
        {filterLabels.applyFilters}
      </Button>
    </div>
  );
});

export const TimeRangeForm = memo(function TimeRangeForm({
  filterLabels,
  dateRangeOptions,
  startDateErrorMessage,
  setStartDateErrorMessage,
  endDateErrorMessage,
  setEndDateErrorMessage,
  defaultPaymentDateRange,
  PAYMENTS_DEFAULT_START_DATE,
  selectedFilters,
  selectedCriteria,
  analytics,
  format = "MM/dd/yy",
  paymentStatusOptions,
  programCodeOptions,
  isStatusRequired,
  isProgramCodeRequired,
  isDateDisabled = false
}: TimeRangeFormProps) {
  const [range, setRange] = useState("");
  const methods = useFormContext();
  const { control, setValue } = methods;
  const { locale } = useRouter();
  const paymentStartDate = defaultPaymentDateRange.startDate;
  const currentDate = LocalizedDate.now();
  const [startDate, setStartDate] = useState(selectedCriteria.startDate);
  const [isUpdated, setIsUpdated] = useState(0);
  const [endDate, setEndDate] = useState(selectedCriteria.endDate);

  const formSelectedValues = {
    range: selectedFilters.find((selectedItem) => selectedItem.code === "range"),
    status: selectedFilters.find((selectedItem) => selectedItem.code === "status"),
    programCode: selectedFilters.find((selectedItem) => selectedItem.code === "programCode")
  };
  const router = useRouter();

  useEffect(() => {
    if (formSelectedValues?.range?.value) {
      setStartDate(selectedCriteria.startDate as LocalizedDate);
      setEndDate(selectedCriteria.endDate as LocalizedDate);
      setIsUpdated(isUpdated + 1);
    }
  }, []);

  const onDateRangeChange = useCallback(
    (field) => (item) => {
      item.value ? field.onChange(item) : field.onChange("");

      if (field.name === "range") setRange(item.value);

      analytics.appliedDateRangeFilter({ locale: router.locale, selectedDateRange: item.value });
      switch (item.value) {
        case "allTime":
          setStartDate(paymentStartDate);
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "thisMonth":
          setStartDate(LocalizedDate.startOfMonth());
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "past30Days":
          setStartDate(LocalizedDate.subtractFromNow(1, "months"));
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "past90Days":
          setStartDate(LocalizedDate.subtractFromNow(3, "months"));
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "past6Months":
          setStartDate(LocalizedDate.subtractFromNow(6, "months"));
          setIsUpdated(isUpdated + 1);
          setEndDate(currentDate);
          break;
        case "yearToDate":
          setStartDate(LocalizedDate.now().startOfYear());
          setEndDate(currentDate);
          setIsUpdated(isUpdated + 1);
          break;
        case "lastYear":
          // Checking whether lastYear date is greater than launch date or not
          let lastYearStartDate = LocalizedDate.subtractFromNow(1, "years").startOfYear();
          const launchDate = LocalizedDate.fromFormattedDate(PAYMENTS_DEFAULT_START_DATE);
          lastYearStartDate = lastYearStartDate.isBefore(launchDate) ? launchDate : lastYearStartDate;
          setStartDate(lastYearStartDate);
          setEndDate(LocalizedDate.subtractFromNow(1, "years").endOfYear());
          setIsUpdated(isUpdated + 1);
          break;
      }
    },
    [startDate, endDate]
  );

  const onDateChange = (date, type) => {
    setRange("custom");
    setValue(
      "range",
      dateRangeOptions.find((option) => option.value === "custom")
    );
    type === "startDate" && setStartDate(LocalizedDate.fromFormattedDate(date));
    type === "endDate" && setEndDate(LocalizedDate.fromFormattedDate(date));
  };

  useEffect(() => {
    setValue("startDate", startDate);
    setEndDateErrorMessage("");
    if (isNaN(startDate.millisecondsEpoch)) {
      setStartDateErrorMessage(filterLabels.startDateRequired);
    } else if (startDate.isAfter(endDate)) {
      setStartDateErrorMessage(filterLabels.startDateError);
    } else {
      setStartDateErrorMessage("");
    }
  }, [startDate]);

  useEffect(() => {
    setValue("endDate", endDate);
    setStartDateErrorMessage("");
    if (isNaN(endDate.millisecondsEpoch)) {
      setEndDateErrorMessage(filterLabels.endDateRequired);
    } else if (endDate.isBefore(startDate)) {
      setEndDateErrorMessage(filterLabels.endDateError);
    } else {
      setEndDateErrorMessage("");
    }
  }, [endDate]);

  const onSelectChange = useCallback(
    (field) => (item) => {
      if (field.name === "status")
        analytics.appliedPaymentStatusFilter({ locale: router.locale, selectedPaymentStatus: item.value });
      else analytics.appliedPaymentTypeFilter({ locale: router.locale, selectedPaymentType: item.value });
      field.onChange(item.value ? item : "");
    },
    []
  );

  return (
    <div className="time-range-filter-form-elements">
      <Controller
        control={control}
        name="range"
        defaultValue={formSelectedValues.range || dateRangeOptions[0]}
        render={({ field, fieldState: { error } }) => (
          <Select
            id="date-range"
            errorMessage={error?.message}
            options={dateRangeOptions}
            label={filterLabels.dateRange}
            onChange={onDateRangeChange(field)}
            selectedOption={dateRangeOptions.find((option) => option.value === range) || formSelectedValues.range}
            skipOnChangeDuringRender
          />
        )}
      />
      <div className="filter-date-container">
        <Controller
          control={control}
          name="startDate"
          defaultValue={startDate.toDate()}
          render={({ field, fieldState: { error } }) => (
            <DateInput
              key={isUpdated}
              errorMessage={error?.message || startDateErrorMessage}
              {...field}
              label={filterLabels.startDate}
              placeholder={filterLabels.startDate}
              maxDate={new Date()}
              locale={locale}
              onChange={(date: Date) => {
                onDateChange(date, "startDate");
                field.onChange(date);
              }}
              value={startDate.toDate()}
              minDate={paymentStartDate.toDate()}
              id={"startDate"}
              format={format}
              title={filterLabels.calendar}
              cancelText={filterLabels.cancel}
              okText={filterLabels.ok}
              onCancel={(date) => {
                setValue("startDate", LocalizedDate.fromFormattedDate(date.toDateString()));
              }}
              disabled={isDateDisabled}
            />
          )}
        />
        <Controller
          control={control}
          name="endDate"
          defaultValue={endDate.toDate()}
          render={({ field, fieldState: { error } }) => (
            <DateInput
              key={isUpdated}
              errorMessage={error?.message || endDateErrorMessage}
              {...field}
              label={filterLabels.endDate}
              placeholder={filterLabels.endDate}
              maxDate={new Date()}
              locale={locale}
              onChange={(date: Date) => {
                onDateChange(date, "endDate");
                field.onChange(date);
              }}
              value={endDate.toDate()}
              minDate={paymentStartDate.toDate()}
              id={"endDate"}
              format={format}
              title={filterLabels.calendar}
              cancelText={filterLabels.cancel}
              okText={filterLabels.ok}
              onCancel={(date) => {
                setValue("endDate", LocalizedDate.fromFormattedDate(date.toDateString()));
              }}
              disabled={isDateDisabled}
            />
          )}
        />
      </div>
      {isStatusRequired && (
        <Controller
          control={control}
          name="status"
          defaultValue={formSelectedValues.status || paymentStatusOptions[0]}
          render={({ field, fieldState: { error } }) => (
            <Select
              id="payment-status"
              errorMessage={error?.message}
              options={paymentStatusOptions}
              label={filterLabels.paymentStatus}
              onChange={onSelectChange(field)}
              selectedOption={formSelectedValues.status}
              skipOnChangeDuringRender
            />
          )}
        />
      )}
      {isProgramCodeRequired && (
        <Controller
          control={control}
          name="programCode"
          defaultValue={formSelectedValues.programCode || programCodeOptions[0]}
          render={({ field, fieldState: { error } }) => (
            <Select
              id="opportunity-type"
              errorMessage={error?.message}
              options={programCodeOptions}
              label={filterLabels.programCode}
              onChange={onSelectChange(field)}
              selectedOption={formSelectedValues.programCode}
              skipOnChangeDuringRender
            />
          )}
        />
      )}
    </div>
  );
});

const TimeRangeFilterForm: FC<TimeRangeFilterFormProps> = ({
  filterLabels,
  dateRangeOptions,
  defaultPaymentDateRange,
  PAYMENTS_DEFAULT_START_DATE,
  selectedCriteria,
  selectedFilters,
  updatePaymentsFilterDetails,
  analytics,
  format,
  paymentStatusOptions,
  programCodeOptions,
  isStatusRequired,
  isProgramCodeRequired,
  isDateDisabled
}) => {
  const {
    dispatch,
    state: { isLoading }
  } = useAppContext() || {};
  const router = useRouter();
  const stableDispatch = useCallback(dispatch, []);

  const [show, setShow] = useState(false);
  const [isPending, setIsPending] = useState(isLoading);

  useEffect(() => {
    setIsPending(isLoading);
    if (!isLoading) setShow(false);
  }, [isLoading, stableDispatch]);

  const handleOutsideClick = useCallback(
    (e) => {
      if (e.target === e.currentTarget) {
        setShow(false);
      }
    },
    [show]
  );

  const submitHandler = useCallback(
    async (formData) => {
      stableDispatch({ type: LOADING, data: true });
      updatePaymentsFilterDetails(formData);
    },
    [stableDispatch]
  );

  const [startDateErrorMessage, setStartDateErrorMessage] = useState("");
  const [endDateErrorMessage, setEndDateErrorMessage] = useState("");

  const toggleFilterForm = () => {
    if (!show) analytics.openedPaymentsFiltersForm({ locale: router.locale });
    setShow(!show);
  };

  return (
    <div className="time-range-filter-container" onClick={handleOutsideClick}>
      <div className="time-range-filter-button">
        <Button variant="secondary" size="sm" onClick={toggleFilterForm}>
          <div>
            <Icon icon={Filter} /> <span title={filterLabels.filters}>{filterLabels.filters}</span>
          </div>
        </Button>
      </div>
      {show && (
        <div className={`time-range-filter-section ${show ? "show" : "hide"}`}>
          <Icon
            className="time-range-filter-close-button"
            icon={close}
            onClick={() => {
              setShow(false);
            }}
          />
          <Form onSubmit={submitHandler}>
            <TimeRangeForm
              {...{
                filterLabels,
                dateRangeOptions,
                startDateErrorMessage,
                setStartDateErrorMessage,
                endDateErrorMessage,
                setEndDateErrorMessage,
                defaultPaymentDateRange,
                PAYMENTS_DEFAULT_START_DATE,
                selectedFilters,
                selectedCriteria,
                analytics,
                format,
                paymentStatusOptions,
                programCodeOptions,
                isStatusRequired,
                isProgramCodeRequired,
                isDateDisabled
              }}
            />
            <FilterButton
              {...{
                filterLabels,
                isPending,
                startDateErrorMessage,
                endDateErrorMessage
              }}
            />
          </Form>
        </div>
      )}
    </div>
  );
};

export default TimeRangeFilterForm;
