import { CheckTermsAndConditionsUpToDateServerPropsMiddleware } from "@eait-playerexp-cn/identity";
import { serverPropsMiddlewareFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import CachedTermsAndConditions from "@src/server/pactSafe/CachedTermsAndConditions";

const checkTermsAndConditionsUpToDate = (locale: string) => {
  return serverPropsMiddlewareFactory(
    new CheckTermsAndConditionsUpToDateServerPropsMiddleware(
      ApiContainer.get("options"),
      ApiContainer.get(CachedTermsAndConditions),
      locale
    )
  );
};

export default checkTermsAndConditionsUpToDate;
