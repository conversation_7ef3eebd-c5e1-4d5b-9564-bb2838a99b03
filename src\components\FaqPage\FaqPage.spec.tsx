import { render, screen } from "@testing-library/react";
import FaqPage from "./FaqPage";
import Random from "__tests__/factories/Random";
import { aSystemInformation } from "__tests__/factories/documentation/documentation";
import { axe } from "jest-axe";

describe("FaqPage", () => {
  const faqPageProps = {
    sys: aSystemInformation(),
    title: Random.string(),
    body: {
      richText:
        '{"nodeType":"paragraph","data":{},"content":[{"nodeType":"text","value":"Sample Text","marks":[],"data":{}}]}',
      embeddedItems: []
    }
  };

  it("shows faq content", () => {
    render(<FaqPage {...faqPageProps} />);

    expect(screen.getByTestId("faq-page")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<FaqPage {...faqPageProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
