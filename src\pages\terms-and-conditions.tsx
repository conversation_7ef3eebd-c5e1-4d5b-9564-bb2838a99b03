import "reflect-metadata";
import { ComponentType, useCallback, useEffect, useState } from "react";
import { useRouter } from "next/router";
import { LOADING, SESSION_USER, useIsMounted, USER_NAVIGATED } from "../utils";
import { addLocaleCookie } from "@eait-playerexp-cn/server-kernel";
import MigrationLayout from "@components/migrations/migration-layout/MigrationLayout";
import { AuthenticatedUser } from "@src/analytics/BrowserAnalytics";
import { useAppContext } from "@src/context";
import Loading from "@components/loading/Loading";
import dynamic from "next/dynamic";
import { useDependency } from "@src/context/DependencyContext";
import { useToast } from "@eait-playerexp-cn/core-ui-kit";
import Error from "./_error";
import { CreatorProfile, CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { TermsAndConditonsPageLabels } from "@src/server/contentManagement/TermsAndConditionsPageMapper";
import { InformationPageLabels } from "@src/server/contentManagement/InformationPageMapper";
import { BreadcrumbPageLabels } from "@src/server/contentManagement/BreadcrumbPageMapper";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { createRouter } from "next-connect";
import initializeSession from "@src/serverprops/middleware/InitializeSession";
import { addIdentityTelemetryAttributes } from "@eait-playerexp-cn/identity";
import saveInitialPage from "@src/serverprops/middleware/SaveInitialPage";
import termsAndConditionsProps from "@src/serverprops/TermsAndConditionsProps";
import { GetServerSidePropsResult } from "next";
import errorLogger from "@src/serverprops/middleware/ErrorLogger";
import checkTermsAndConditionsUpToDate from "@src/serverprops/middleware/CheckTermsAndConditionsUpToDate";

export type TermsAndConditionsProps = {
  runtimeConfiguration?: Record<string, unknown>;
  pageLabels: TermsAndConditonsPageLabels & InformationPageLabels & BreadcrumbPageLabels & CommonPageLabels;
  opportunityId: string;
  user: AuthenticatedUser;
  initialPage: string;
  locale: string;
  urlLocale: string;
  FLAG_COUNTRIES_BY_TYPE: boolean;
};

const TermsAndConditionsForm: ComponentType<Record<string, unknown>> = dynamic(
  () =>
    import(
      // @ts-ignore
      "onboarding/TermsAndConditions"
    ),
  {
    ssr: false,
    loading: () => <Loading />
  }
);

export default function TermsAndConditions({
  pageLabels,
  opportunityId,
  user,
  initialPage,
  locale,
  urlLocale,
  FLAG_COUNTRIES_BY_TYPE
}: TermsAndConditionsProps) {
  const router = useRouter();
  const isMounted = useIsMounted();
  const { state, dispatch } = useAppContext();
  const stableDispatch = useCallback(dispatch, [dispatch]);
  const {
    creatorsClient,
    errorHandler,
    metadataClient,
    onBoardingClient,
    client,
    configuration: { PROGRAM_CODE, DEFAULT_AVATAR_IMAGE }
  } = useDependency();
  const [showMigration, setShowMigration] = useState(false);
  const [navigateToPage, setNavigateToPage] = useState("");
  const [showPactSafeIframe, setShowPactSafeIframe] = useState(false);
  const { error: errorToast } = useToast();
  const creatorService = new CreatorsService(creatorsClient, DEFAULT_AVATAR_IMAGE);

  const { breadcrumbPageLabels, commonPageLabels, termsAndConditionLabels, informationLabels } = pageLabels;
  const [creator, setCreator] = useState<CreatorProfile | null>(null);

  const [showConfirmation, setShowConfirmation] = useState(false);

  const { exceptionCode = null, sessionUser = null, isLoading = false } = state;

  const onClose = useCallback(() => {
    setShowConfirmation(!showConfirmation);
  }, [showConfirmation]);

  const onGoBack = () => {
    stableDispatch && stableDispatch({ type: USER_NAVIGATED, data: true });
    setShowMigration(true);
  };

  const fetchCreator = useCallback(async () => {
    try {
      const creator = await creatorService.getCreator(PROGRAM_CODE);
      stableDispatch({ type: LOADING, data: false });
      setCreator(creator);
    } catch (error) {
      stableDispatch({ type: LOADING, data: false });
      errorHandler(stableDispatch, error);
    }
  }, [client, errorHandler, stableDispatch]);

  useEffect(() => {
    fetchCreator();
  }, [fetchCreator]);

  useEffect(() => user && stableDispatch({ type: SESSION_USER, data: user }), [user, stableDispatch]);

  if (exceptionCode) {
    return <Error statusCode={exceptionCode} sessionUser={sessionUser} />;
  }

  const layoutLabels = {
    buttons: {
      cancel: commonPageLabels.cancel,
      discard: commonPageLabels.discard,
      next: commonPageLabels.next,
      close: commonPageLabels.close,
      yes: commonPageLabels.yes,
      no: commonPageLabels.no,
      save: commonPageLabels.save,
      declineTermsAndCondition: termsAndConditionLabels.declineTermsAndCondition
    }
  };

  const getIframe = (pactSafeUrl: string) => {
    return (
      <>
        <iframe src={`/pactsafe-page?url=${pactSafeUrl}`} title="PactSafe Page" /> <hr />
      </>
    );
  };

  return (
    <MigrationLayout
      showHeader={creator?.program?.status !== "ACTIVE"}
      pageTitle={termsAndConditionLabels.termsAndConditionsPagetitle}
      tabTitle={`${commonPageLabels.theSims} | ${commonPageLabels.contract}`}
      onGoBack={onGoBack}
      className="onboarding-creator"
      onClose={onClose}
      setNavigateToPage={setNavigateToPage}
      labels={{
        back: commonPageLabels.back,
        title: commonPageLabels.creatorNetwork,
        close: commonPageLabels.close
      }}
      migration={{
        information: commonPageLabels.information,
        contract: commonPageLabels.contract,
        paymentInfo: commonPageLabels.paymentInfo,
        connectAccounts: commonPageLabels.connectedAccounts,
        preferences: commonPageLabels.preferences
      }}
      stableDispatch={stableDispatch}
      isOnboardingFlow={true}
      setShowMigration={!showPactSafeIframe ? setShowMigration : undefined}
      isLoading={isLoading}
      completed={commonPageLabels.completed}
    >
      <div className="onboarding-terms-and-condtions-container">
        <TermsAndConditionsForm
          analytics={{ signedTermsAndConditions: () => {} }}
          configuration={{
            onBoardingClient,
            creatorsClient,
            metadataClient,
            navigateToNextPage: "/payment-info",
            navigateToPreviousPage: "/communication-preferences",
            DEFAULT_AVATAR_IMAGE: DEFAULT_AVATAR_IMAGE
          }}
          errorHandling={errorHandler}
          showConfirmation={showConfirmation}
          setNavigateToPage={setNavigateToPage}
          layout={layoutLabels}
          navigateToPage={navigateToPage}
          stableDispatch={stableDispatch}
          isMounted={isMounted}
          router={router}
          onClose={onClose}
          user={user}
          showPactSafeIframe={showPactSafeIframe}
          setShowPactSafeIframe={setShowPactSafeIframe}
          pageLabels={{ ...termsAndConditionLabels, ...commonPageLabels, infoLabels: informationLabels }}
          termsAndConditionsLabels={termsAndConditionLabels}
          breadcrumbLabels={breadcrumbPageLabels}
          FLAG_COUNTRIES_BY_TYPE={FLAG_COUNTRIES_BY_TYPE}
          dispatch={dispatch}
          showMigration={showMigration}
          errorToast={errorToast}
          setShowMigration={setShowMigration}
          opportunityId={opportunityId}
          locale={locale}
          getIframe={getIframe}
          urlLocale={urlLocale}
          initialPage={initialPage}
          PROGRAM_CODE={PROGRAM_CODE}
          setShowConfirmation={setShowConfirmation}
          state={state}
          enableCommunicationPreferencesTab={true}
        />
      </div>
    </MigrationLayout>
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  const router = createRouter();

  router
    .use(errorLogger)
    .use(initializeSession)
    .use(addIdentityTelemetryAttributes)
    .use(saveInitialPage(locale))
    .use(addLocaleCookie(locale))
    .use(checkTermsAndConditionsUpToDate(locale))
    .get(termsAndConditionsProps(locale));

  return (await router.run(req, res)) as GetServerSidePropsResult<TermsAndConditionsProps>;
};
