import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { SignupCompleteProps } from "@src/pages/signup-complete";
import { SignupCompletePageLabels } from "@src/server/contentManagement/SignupCompletePageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import { GetServerSidePropsResult, NextApiResponse } from "next";

export default class SignupCompletePagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<SignupCompleteProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string
  ) {
    super(options);
  }

  async handle(
    _: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<SignupCompleteProps>> {
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        pageLabels: (await this.contents.getPageLabels(
          this.currentLocale,
          "signupComplete"
        )) as SignupCompletePageLabels
      }
    };
  }
}
