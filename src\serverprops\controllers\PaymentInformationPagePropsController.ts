import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { PaymentInformationProps } from "@src/pages/payment-information";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";
import { PaymentDetailsPageLabels } from "@src/server/contentManagement/PaymentDetailsPageMapper";
import { TimeRangeFilterPageLabels } from "@src/server/contentManagement/TimeRangeFilterPageMapper";
import ContentManagementService from "@src/services/ContentManagementService";
import config from "config";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

export default class PaymentInformationPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<PaymentInformationProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly contents: ContentManagementService,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<PaymentInformationProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(),
        PAYMENTS_DEFAULT_START_DATE: config.PAYMENTS_DEFAULT_START_DATE,
        pageLabels: (await this.contents.getPageLabels(this.currentLocale, "paymentsPage")) as CommonPageLabels &
          TimeRangeFilterPageLabels &
          PaymentDetailsPageLabels,
        locale: this.currentLocale,
        user: authenticatedUser,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "payment-information",
          "notifications",
          "connect-accounts",
          "opportunities"
        ]))
      }
    };
  }
}
