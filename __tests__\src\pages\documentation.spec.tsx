import "reflect-metadata";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import { screen } from "@testing-library/react";
import { axe } from "jest-axe";
import { act } from "react-dom/test-utils";
import { useRouter } from "next/router";
import { useDependency } from "@src/context/DependencyContext";
import { useAppContext } from "@src/context";
import { mockMatchMedia } from "__tests__/helpers/window";
import { renderPage } from "__tests__/helpers/page";
import Documentation from "@src/pages/documentation";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { SESSION_USER } from "@src/utils";
import * as React from "react";
import { documentationPageData } from "__tests__/factories/documentation/documentation";
import { CommonPageLabels } from "@src/server/contentManagement/CommonPageMapper";

jest.mock("@src/context/DependencyContext");
jest.mock("@src/context", () => ({
  ...jest.requireActual("@src/context"),
  useAppContext: jest.fn()
}));

describe("Documentation", () => {
  mockMatchMedia();
  const dispatch = jest.fn();
  const router = {
    push: jest.fn(),
    locale: "en-us",
    pathname: "/documentation"
  };
  const labels = ({
    commonPageLabels: {
      myProfile: "My Profile",
      signout: "Sign Out",
      home: "Home",
      faqs: "FAQs",
      requestToJoin: "Request to Join",
      logIn: "Log in",
      signIn: "Sign in",
      notifications: "Notifications",
      documentationCenter: "Documentation Center"
    }
  } as unknown) as CommonPageLabels;
  const user = AuthenticatedUserFactory.fromIdentity(Identity.fromStored(aStoredIdentity()), "creator_network", "");
  const documentationProps = {
    user,
    indexPage: documentationPageData(),
    locale: "en-us",
    runtimeConfiguration: {
      SUPPORTED_LOCALES: ["en-us"],
      PROGRAM_CODE: "sims_creator_program"
    },
    pageLabels: labels
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: dispatch,
      state: { sessionUser: user }
    });
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] },
        BASE_PATH: "http://localhost:3050"
      }
    });
  });

  it("dispatches the user to the context on mount", () => {
    renderPage(<Documentation {...documentationProps} />);

    expect(dispatch).toHaveBeenCalledWith({
      type: SESSION_USER,
      data: user
    });
  });

  it("shows ErrorPage when exception code is present", () => {
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch: dispatch,
      state: { exceptionCode: 404, sessionUser: user }
    });

    renderPage(<Documentation {...documentationProps} />);

    expect(screen.getByText("404")).toBeInTheDocument();
  });

  it("shows Documentation page content", () => {
    renderPage(<Documentation {...documentationProps} />);

    expect(screen.getByTestId("documentation-page-container")).toBeInTheDocument();
  });

  it("is accessible", async () => {
    let results;
    const { container } = renderPage(<Documentation {...documentationProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
