import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import FaqPagePropsController from "./controllers/FaqPagePropsController";
import ArticlesHttpClient from "@src/server/contentModal/ArticlesHttpClient";
import config from "config";

const faqProps = (locale: string) =>
  serverPropsControllerFactory(
    new FaqPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ArticlesHttpClient),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default faqProps;
