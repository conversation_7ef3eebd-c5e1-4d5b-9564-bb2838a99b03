import { render, screen } from "@testing-library/react";
import Text from "./Text";
import Random from "__tests__/factories/Random";
import { axe } from "jest-axe";

describe("Text", () => {
  const text = Random.sentence(3);

  it("shows as paragraph by default", () => {
    render(<Text>{text}</Text>);

    const element = screen.getByText(text);
    expect(element.tagName).toBe("P");
  });

  it('shows with custom element type when "as" prop is provided', () => {
    render(<Text as="h1">{text}</Text>);

    const element = screen.getByText(text);
    expect(element.tagName).toBe("H1");
  });

  it("applies custom CSS class when provided", () => {
    const className = "custom-class";

    render(<Text className={className}>{text}</Text>);

    expect(screen.getByText(text)).toHaveClass(className);
  });

  it("is accessible", async () => {
    const { container } = render(<Text>{text}</Text>);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
