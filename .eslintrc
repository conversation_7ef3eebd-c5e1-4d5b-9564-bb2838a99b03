{
  "extends": ["next"],
  "rules": {
    "@next/next/no-img-element": "off",
    "import/no-anonymous-default-export": ["error"],
    "sort-imports": [
      "error",
      {
        "ignoreCase": true,
        "ignoreDeclarationSort": true
      }
    ]
  },
  "plugins": ["testing-library", "@typescript-eslint"],
  "overrides": [
    {
      "files": ["**/__tests__/**/?(*.)+(spec|test).[jt]s?(x)"],
      "extends": ["plugin:testing-library/react"]
    },
    {
      "files": ["**/*.ts", "**/*.tsx"],
      "extends": ["plugin:@typescript-eslint/recommended"],
      "rules": {
        "no-unused-vars": "off",
        "@typescript-eslint/no-unused-vars": [
          "error",
          {
            "args": "all",
            "argsIgnorePattern": "^_"
          }
        ],
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/no-unused-expressions": "off"
        //        "@typescript-eslint/explicit-module-boundary-types": ["error"]
      }
    }
  ],
  "ignorePatterns": ["stories/**/*"]
}
