import { Inject, Service } from "typedi";
import SignerInformation from "./SignerInformation";
import SignedStatus from "./SignedStatus";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type SigningUrl = {
  contractUrl: string;
};

@Service()
export default class TermsAndConditionsHttpClient {
  constructor(@Inject("legalClient") private client: TraceableHttpClient) {}

  async signedStatusWithProgram(creatorId: string, locale: string, program: string): Promise<SignedStatus> {
    const res = await this.client.get(`/v2/terms-and-conditions-status/${creatorId}`, { query: { locale, program } });
    return Promise.resolve(res.data as SignedStatus);
  }

  async signerUrl(signer: SignerInformation): Promise<Record<string, unknown>> {
    const res = await this.client.post("/v3/terms-and-conditions/signing-url", { body: signer });
    return Promise.resolve(res.data);
  }
}
