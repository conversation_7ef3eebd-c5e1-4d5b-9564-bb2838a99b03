import "reflect-metadata";
import { createRouter } from "next-connect";
import { NextApiResponse } from "next";
import ApiContainer from "../../ApiContainer";
import UploadCreatorAvatarAction from "../../actions/Creators/Avatar/UploadCreatorAvatarAction";
import AvatarInput from "../../actions/Creators/Avatar/AvatarInput";
import User from "@src/authentication/User";
import CreatorsWithPayableStatusHttpClient from "@src/server/creators/CreatorsWithPayableStatusHttpClient";
import parseUploadFiles from "../../middleware/ParseUploadedFiles";
import { NextApiRequestWithMultipartFile } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import onError from "@src/middleware/JsonErrorHandler";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithMultipartFile, NextApiResponse>();

// `config` excludes a body parser since the `parseUploadFiles` middleware will do the job
export const config = {
  api: {
    bodyParser: false
  }
};

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .use(parseUploadFiles)
  .post(async (req: NextApiRequestWithMultipartFile, res: NextApiResponse) => {
    if (!req.files.avatar || req.error) {
      res.status(400).end(); // TODO: we should return an API Problem as usual
      return;
    }

    const action = ApiContainer.get(UploadCreatorAvatarAction);
    const identity = req.session.identity;
    const user = User.from(identity);
    await action.execute(new AvatarInput(identity.id as string, req.files.avatar[0]));

    const creators = ApiContainer.get(CreatorsWithPayableStatusHttpClient);
    const updatedCreator = await creators.withId(identity.id);
    user.updateAvatar(updatedCreator?.avatar);
    req.session.user = user;
    req.session.save();

    res.status(201).end();
  });

export default router.handler({ onError });
