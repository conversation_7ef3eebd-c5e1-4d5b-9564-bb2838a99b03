import { ContentStatus } from "@eait-playerexp-cn/core-ui-kit";
import { AxiosResponse } from "axios";
import { ContentFeedbackLog, SubmittedContentFeedbackCriteria } from "@src/submittedContent/ContentFeedbackHttpClient";
import { Contents, WebsiteContent } from "@src/submittedContent/SubmittedContentHttpClient";
import { PreSignedUrl } from "@src/submittedContent/SignedUrlsHttpClient";
import ContentFeedback from "@src/submittedContent/ContentFeedback";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type ContentDetail = {
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  contentType: string;
};

export type SubmittedWebsiteContent = {
  participationId: string;
  websiteContent: {
    contentDetails: ContentDetail[];
  };
};

export type UpdatedWebsiteContent = {
  participationId: string;
  websiteContent: {
    id: string;
    contentDetails: ContentDetail;
  };
};

export type SubmittedSocialMediaContent = {
  participationId: string;
  socialMediaContent: {
    creatorId: string;
    contentUrl: string;
    accountId: string;
  };
};

export type SubmittedUploadContent = {
  fileName: string;
  title: string;
  versionId: string;
  thumbnail: string;
  id: string;
  contentType: string;
  participationId: string;
};

export type SubmittedUploadContentDeliverable = SubmittedUploadContent & {
  deliverableId: string;
};

export type SubmittedSocialMediaContentDeliverable = SubmittedSocialMediaContent & {
  socialMediaContent: {
    deliverableId: string;
  };
};

export type SubmittedWebsiteContentDeliverable = {
  participationId: string;
  websiteContent: WebsiteContent & {
    deliverableId: string;
  };
};

export type SubmittedContentWithProgramCodePage = {
  count: number;
  total: number;
  contents: SubmittedContentWithProgramCode[];
};

export type ContentScanSourceType = "FILE_UPLOAD" | "LISTING";
export type SignedURLRequestBody = {
  participationId: string;
  creatorId: string;
  deliverableId: string;
  fileName: string;
  contentTitle: string;
  contentDescription: string;
  contentScanSourceType: ContentScanSourceType;
  extension: string;
  contentType: string;
  mimeType: string;
  thumbnail: string;
  fileSize: number;
  programCode: string;
  nucleusId: number;
};

export class SubmittedContent {
  readonly submittedDate: LocalizedDate;
  readonly id: string;
  readonly name: string;
  readonly type: string;
  readonly thumbnail: string;
  readonly status: ContentStatus;
  readonly contentUri: string;
  readonly contentType: string;
  readonly contentTypeLabel: string;
  readonly opportunityName: string;
  readonly opportunityId: string;
  readonly sourceType: string;
  readonly submittedOn: number;

  constructor(content) {
    Object.assign(this, content);
    this.submittedDate = new LocalizedDate(content.submittedDate);
    this.contentType = content.contentType === "carousel_album" ? "image" : content.contentType;
  }

  public formattedSubmittedDate(locale: string): string {
    return `${this.submittedDate.formatWithoutTime(undefined, locale)} (${LocalizedDate.timeZoneAbbreviation()})`;
  }

  public requiresChanges(): boolean {
    return this.status === "CHANGE_REQUESTED";
  }
}
export class SubmittedContentWithProgramCode extends SubmittedContent {
  readonly reviewFinalRemark: {
    content: string;
    author: string;
    date: LocalizedDate;
  };
  readonly deliverableId: string;

  constructor(content: Contents) {
    super(content);
    this.deliverableId = content.deliverableId;
    this.reviewFinalRemark = content.reviewFinalRemark
      ? {
          content: content.reviewFinalRemark?.content,
          author: content.reviewFinalRemark?.author,
          date: content.reviewFinalRemark?.date ? new LocalizedDate(content.reviewFinalRemark.date) : null
        }
      : null;
  }

  public formattedReviewFinalRemarkDate(locale: string): string {
    return this.reviewFinalRemark?.date
      ? `${this.reviewFinalRemark?.date?.formatWithCalendarLabels(
          "MMM DD, YYYY",
          locale
        )} (${LocalizedDate.timeZoneAbbreviation()})`
      : null;
  }
}

export class ContentsFeedback {
  private readonly lastUpdateDate: LocalizedDate;

  constructor(content) {
    Object.assign(this, content);
    this.lastUpdateDate = new LocalizedDate(content.lastUpdateDate);
  }

  public formattedSubmittedDate(locale: string): string {
    return `${this.lastUpdateDate.formatWithCalendarLabels(
      "MMM DD, YYYY",
      locale
    )} (${LocalizedDate.timeZoneAbbreviation()})`;
  }
}

export type ContentsWithDeliverable = Contents & { deliverableId: string };

export class SubmittedContentWithDeliverableDetail extends SubmittedContent {
  readonly deliverableId: string;

  constructor(content: ContentsWithDeliverable) {
    super(content);
    this.deliverableId = content.deliverableId;
  }
}

export type ContentsWithReviewFinalRemark = ContentsWithDeliverable & {
  reviewFinalRemark: {
    content: string;
    author: string;
    date: number;
  };
};

export type SubmittedContentWithFinalRemarkPage = {
  count: number;
  total: number;
  contents: ContentsWithReviewFinalRemark[];
};

export class SubmittedContentWithFinalRemark extends SubmittedContentWithDeliverableDetail {
  readonly reviewFinalRemark: {
    content: string;
    author: string;
    date: LocalizedDate;
  };

  constructor(content: ContentsWithReviewFinalRemark) {
    super(content);
    this.reviewFinalRemark = content.reviewFinalRemark
      ? {
          content: content.reviewFinalRemark?.content,
          author: content.reviewFinalRemark?.author,
          date: content.reviewFinalRemark?.date ? new LocalizedDate(content.reviewFinalRemark.date) : null
        }
      : null;
  }

  public formattedReviewFinalRemarkDate(locale: string): string {
    return this.reviewFinalRemark?.date
      ? `${this.reviewFinalRemark?.date?.formatWithCalendarLabels(
          "MMM DD, YYYY",
          locale
        )} (${LocalizedDate.timeZoneAbbreviation()})`
      : null;
  }
}

export default class SubmittedContentService {
  constructor(private readonly client: TraceableHttpClient) {}

  async validateContent(url: { urls: string[] }, type: string): Promise<AxiosResponse<void>> {
    return (await this.client.post("/api/verify-content-url", { body: url, query: { type } })) as AxiosResponse<void>;
  }

  async submitWebsiteContent(submittedWebsiteContent: SubmittedWebsiteContent): Promise<AxiosResponse<void>> {
    return (await this.client.post("/api/submitted-website-content", {
      body: submittedWebsiteContent
    })) as AxiosResponse<void>;
  }

  async updateWebsiteContent(updatedWebsiteContent: UpdatedWebsiteContent): Promise<AxiosResponse<void>> {
    return (await this.client.put("/api/submitted-website-content", {
      body: updatedWebsiteContent
    })) as AxiosResponse<void>;
  }

  async getSignedUrl(signedURLRequestBody: SignedURLRequestBody): Promise<AxiosResponse<PreSignedUrl>> {
    return (await this.client.post("/api/v2/upload-signed-urls", {
      body: signedURLRequestBody
    })) as AxiosResponse<PreSignedUrl>;
  }

  async getContentsFeedback(criteria: SubmittedContentFeedbackCriteria): Promise<AxiosResponse<ContentFeedbackLog>> {
    return await this.client
      .get("/api/submitted-content-feedback", { query: criteria })
      .then((response: AxiosResponse) => {
        response.data.contentsFeedback = response.data.contentsFeedback?.map(
          (content: ContentFeedback) => new ContentsFeedback(content)
        );
        return response;
      });
  }

  async submitSocialMediaContents(
    submittedSocialMediaContentDeliverable: SubmittedSocialMediaContentDeliverable
  ): Promise<AxiosResponse<void>> {
    return (await this.client.post("/api/content-submission/v5/submitted-social-media-content", {
      body: submittedSocialMediaContentDeliverable
    })) as AxiosResponse<void>;
  }

  async submitWebsiteContentDeliverable(
    submittedWebsiteContentDeliverable: SubmittedWebsiteContentDeliverable
  ): Promise<AxiosResponse<void>> {
    return (await this.client.post("/api/v2/submitted-website-content", {
      body: submittedWebsiteContentDeliverable
    })) as AxiosResponse<void>;
  }

  async submitUploadedContentDeliverable(
    submittedUploadContentDeliverable: SubmittedUploadContentDeliverable
  ): Promise<AxiosResponse<void>> {
    return (await this.client.post("/api/v2/uploaded-contents", {
      body: submittedUploadContentDeliverable
    })) as AxiosResponse<void>;
  }

  async getSubmittedContentsFinalRemarks(
    size: number,
    page: number,
    participationId: string,
    deliverableId?: string
  ): Promise<AxiosResponse<SubmittedContentWithFinalRemarkPage>> {
    return this.client
      .get("/api/v5/submitted-content", { query: { size, page, participationId, deliverableId } })
      .then((response: AxiosResponse) => {
        response.data.contents = response.data.contents?.map(
          (content: ContentsWithReviewFinalRemark) => new SubmittedContentWithFinalRemark(content)
        );
        return response;
      });
  }

  async markUploadComplete(contentId: string): Promise<AxiosResponse<void>> {
    return (await this.client.post("/api/v2/uploaded-contents", { body: { contentId } })) as AxiosResponse<void>;
  }

  async getSubmittedContentsWithProgramCode(
    size: number,
    page: number,
    participationId?: string,
    deliverableId?: string,
    creatorId?: string
  ): Promise<AxiosResponse<SubmittedContentWithProgramCodePage>> {
    return await this.client
      .get("/api/submitted-content", { query: { size, page, participationId, deliverableId, creatorId } })
      .then((response: AxiosResponse) => {
        response.data.contents = response.data.contents?.map(
          (content: Contents) => new SubmittedContentWithProgramCode(content)
        );
        return response;
      });
  }
}
