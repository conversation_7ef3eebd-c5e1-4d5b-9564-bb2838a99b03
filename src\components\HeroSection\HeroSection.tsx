import { ImageType, Media, SystemInformation } from "@components/RichText/RichText";
import Image from "@components/RichTextComponents/Image/Image";
import { useDetectScreen } from "@src/utils";

export type Image = {
  sys: SystemInformation;
  title: string;
  media: Media;
  mobileMedia: Media;
  altText: string;
  titleTags: string;
  ariaTags: string;
  entryType: ImageType;
};
type HeroSectionProps = {
  title: string;
  description: string;
  icon: Image;
  image: Image;
};

const HeroSection = ({ title, description, icon, image }: HeroSectionProps) => {
  const isMobile = useDetectScreen(767);
  return (
    <div className="hero-section" data-testid="hero-section">
      <Image className="hero-section-image" {...(isMobile ? image.mobileMedia : image.media)} />
      <div className="hero-section-content">
        <Image className="hero-section-icon" {...(isMobile ? icon.mobileMedia : icon.media)} />
        <h2 className="hero-section-title">{title}</h2>
        <p className="hero-section-description">{description}</p>
      </div>
    </div>
  );
};

export default HeroSection;
