import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import DashboardPagePropsController from "@src/serverprops/controllers/DashboardPagePropsController";
import config from "config";

const dashboardProps = (locale: string) =>
  serverPropsControllerFactory(
    new DashboardPagePropsController(ApiContainer.get("options"), locale, config.DEFAULT_AVATAR_IMAGE)
  );

export default dashboardProps;
