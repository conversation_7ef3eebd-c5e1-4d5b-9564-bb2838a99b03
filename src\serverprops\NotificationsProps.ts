import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import NotificationsPagePropsController from "@src/serverprops/controllers/NotificationsPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";
import config from "config";

const notificationsProps = (locale: string) =>
  serverPropsControllerFactory(
    new NotificationsPagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ContentManagementService),
      locale,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default notificationsProps;
