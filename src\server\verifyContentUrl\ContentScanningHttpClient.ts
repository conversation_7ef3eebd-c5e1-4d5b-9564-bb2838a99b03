import { Inject, Service } from "typedi";
import { AxiosResponse } from "axios";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type ContentUrls = {
  urls: Array<string>;
};

type ScannedUrl = {
  url: string;
  isSecure: boolean;
};

type ContentScanResult = {
  results: Array<ScannedUrl>;
};

export type ScanType = "INTERESTED_CREATORS" | "CREATORS";

@Service()
class ContentScanningHttpClient {
  constructor(@Inject("contentScanningClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#operation/validateContentUrl Validate Content URL}
   */
  async verifyUrls(urls: ContentUrls, type: ScanType): Promise<AxiosResponse<ContentScanResult>> {
    return (await this.client.post("/v1/secure-content", {
      body: { urls },
      query: { type }
    })) as AxiosResponse<ContentScanResult>;
  }

  async markUploadComplete(contentId: string): Promise<void> {
    await this.client.post(`/v1/completed-uploads`, { body: { contentId } });
  }
}

export default ContentScanningHttpClient;
