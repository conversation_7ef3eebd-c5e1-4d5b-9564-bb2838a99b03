import { MicroCopy } from "./MicroCopy";
import { MicroCopyMapper } from "./MicroCopyMapper";

export type PaymentBannerPageLabels = {
  paymentInformationBannerWarning: string;
  paymentInformationBannerDescription: string;
  paymentInformationBannerDescription_2: string;
  paymentInformationClick: string;
  paymentInformationBannerDescription_3: string;
};

export type PaymentOverviewPageLabels = {
  paymentOverview: string;
  paymentTotalPaid: string;
  paymentPendingPayments: string;
  transactionHistory: string;
  transactionHistoryDetails: string;
  paymentInformationClick: string;
  filteredBy: string;
};

export type TransactionInforamtionPageLabels = {
  paymentSettings: string;
  invoicesHistory: string;
  paymentsHistory: string;
  paymentSettingsDescription: string;
  paymentInvoicesDescription: string;
  paymentHistoryDescription: string;
  pageTitle: string;
  payableStatus: string;
  nonPayableStatus: string;
  nonPayableStatusHelp: string;
  nonPayableStatusClickHere: string;
  nonPayableStatusStart: string;
  yourPayments: string;
};

export type TransactionHistoryPageLabels = {
  paymentGridDescription: string;
  paymentGridType: string;
  paymentGridStatus: string;
  paymentGridAmountDue: string;
  paymentGridDate: string;
  paymentGridDateHelp: string;
  paymentGridContract: string;
  statusPending: string;
  statusProcessed: string;
  downloadContract: string;
  typeCreatorCode: string;
  typeOpportunity: string;
  noPayments: string;
  noPaymentsDescription: string;
  noPaymentsLink: string;
  downloadContractLabel?: string;
  opportunityImageLabel?: string;
  noProcessedPayments: string;
  noProcessedPaymentsDescription: string;
  noPendingPayments: string;
  noPendingPaymentsDescription: string;
  descriptionLabel: string;
  paymentPeriodLabel: string;
};

export type PaymentDetailsPageLabels = {
  paymentDetailsPageLabels: {
    banner: PaymentBannerPageLabels;
    overview: PaymentOverviewPageLabels;
    transaction: TransactionInforamtionPageLabels;
    transactionHistory: TransactionHistoryPageLabels;
  };
};

export class PaymentDetailsPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): PaymentDetailsPageLabels {
    const microCopy = new MicroCopy(microCopies);
    return {
      paymentDetailsPageLabels: {
        banner: {
          paymentInformationBannerWarning: microCopy.get("paymentDetails.banner.paymentInformationBannerWarning"),
          paymentInformationBannerDescription: microCopy.get(
            "paymentDetails.banner.paymentInformationBannerDescription"
          ),
          paymentInformationBannerDescription_2: microCopy.get(
            "paymentDetails.banner.paymentInformationBannerDescription_2"
          ),
          paymentInformationClick: microCopy.get("paymentDetails.banner.paymentInformationClick"),
          paymentInformationBannerDescription_3: microCopy.get(
            "paymentDetails.banner.paymentInformationBannerDescription_3"
          )
        },
        overview: {
          paymentOverview: microCopy.get("paymentDetails.overview.paymentOverview"),
          paymentTotalPaid: microCopy.get("paymentDetails.overview.paymentTotalPaid"),
          paymentPendingPayments: microCopy.get("paymentDetails.overview.paymentPendingPayments"),
          transactionHistory: microCopy.get("paymentDetails.overview.transactionHistory"),
          transactionHistoryDetails: microCopy.get("paymentDetails.overview.transactionHistoryDetails"),
          paymentInformationClick: microCopy.get("paymentDetails.overview.paymentInformationClick"),
          filteredBy: microCopy.get("paymentDetails.overview.filteredBy")
        },
        transaction: {
          paymentSettings: microCopy.get("paymentDetails.transaction.paymentSettings"),
          invoicesHistory: microCopy.get("paymentDetails.transaction.invoicesHistory"),
          paymentsHistory: microCopy.get("paymentDetails.transaction.paymentsHistory"),
          paymentSettingsDescription: microCopy.get("paymentDetails.transaction.paymentSettingsDescription"),
          paymentInvoicesDescription: microCopy.get("paymentDetails.transaction.paymentInvoicesDescription"),
          paymentHistoryDescription: microCopy.get("paymentDetails.transaction.paymentHistoryDescription"),
          pageTitle: microCopy.get("paymentDetails.transaction.pageTitle"),
          payableStatus: microCopy.get("paymentDetails.transaction.payableStatus"),
          nonPayableStatus: microCopy.get("paymentDetails.transaction.nonPayableStatus"),
          nonPayableStatusHelp: microCopy.get("paymentDetails.transaction.nonPayableStatusHelp"),
          nonPayableStatusClickHere: microCopy.get("paymentDetails.transaction.nonPayableStatusClickHere"),
          nonPayableStatusStart: microCopy.get("paymentDetails.transaction.nonPayableStatusStart"),
          yourPayments: microCopy.get("paymentDetails.transaction.yourPayments")
        },
        transactionHistory: {
          paymentGridDescription: microCopy.get("paymentDetails.transactionHistory.paymentGridDescription"),
          paymentGridType: microCopy.get("paymentDetails.transactionHistory.paymentGridType"),
          paymentGridStatus: microCopy.get("paymentDetails.transactionHistory.paymentGridStatus"),
          paymentGridAmountDue: microCopy.get("paymentDetails.transactionHistory.paymentGridAmountDue"),
          paymentGridDate: microCopy.get("paymentDetails.transactionHistory.paymentGridDate"),
          paymentGridDateHelp: microCopy.get("paymentDetails.transactionHistory.paymentGridDateHelp"),
          paymentGridContract: microCopy.get("paymentDetails.transactionHistory.paymentGridContract"),
          statusPending: microCopy.get("paymentDetails.transactionHistory.statusPending"),
          statusProcessed: microCopy.get("paymentDetails.transactionHistory.statusProcessed"),
          downloadContract: microCopy.get("paymentDetails.transactionHistory.downloadContract"),
          typeCreatorCode: microCopy.get("paymentDetails.transactionHistory.typeCreatorCode"),
          typeOpportunity: microCopy.get("paymentDetails.transactionHistory.typeOpportunity"),
          noPayments: microCopy.get("paymentDetails.transactionHistory.noPayments"),
          noPaymentsDescription: microCopy.get("paymentDetails.transactionHistory.noPaymentsDescription"),
          noPaymentsLink: microCopy.get("paymentDetails.transactionHistory.noPaymentsLink"),
          downloadContractLabel: microCopy.get("paymentDetails.transactionHistory.downloadContractLabel"),
          opportunityImageLabel: microCopy.get("paymentDetails.transactionHistory.opportunityImageLabel"),
          noProcessedPayments: microCopy.get("paymentDetails.transactionHistory.noProcessedPayments"),
          noProcessedPaymentsDescription: microCopy.get(
            "paymentDetails.transactionHistory.noProcessedPaymentsDescription"
          ),
          noPendingPayments: microCopy.get("paymentDetails.transactionHistory.noPendingPayments"),
          noPendingPaymentsDescription: microCopy.get("paymentDetails.transactionHistory.noPendingPaymentsDescription"),
          descriptionLabel: microCopy.get("paymentDetails.transactionHistory.descriptionLabel"),
          paymentPeriodLabel: microCopy.get("paymentDetails.transactionHistory.paymentPeriodLabel")
        }
      }
    };
  }
}
