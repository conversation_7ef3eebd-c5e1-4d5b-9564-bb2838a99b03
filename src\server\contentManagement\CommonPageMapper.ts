import { MicroCopyMapper } from "./MicroCopyMapper";
import { MicroCopy } from "./MicroCopy";

export type CommonPageLabels = {
  commonPageLabels: {
    creatorNetwork: string;
    how: string;
    reward: string;
    perks: string;
    faq: string;
    faqs: string;
    messages: string;
    policies: string;
    legal: string;
    disclaimer: string;
    updates: string;
    terms: string;
    privacy: string;
    rights: string;
    report: string;
    home: string;
    works: string;
    rewards: string;
    signIn: string;
    logIn: string;
    apply: string;
    applyNow: string;
    requestToJoin: string;
    cancel: string;
    ok: string;
    calendar: string;
    paymentInfo: string;
    franchises: string;
    completed: string;
    contract: string;
    discard: string;
    next: string;
    save: string;
    edit: string;
    join: string;
    back: string;
    browse: string;
    yes: string;
    no: string;
    remove: string;
    pageNotFound: string;
    pageNotFoundContent: string;
    unhandledError: string;
    unhandledErrorMessage: string;
    logout: string;
    notifyMe: string;
    emailAddress: string;
    unauthorized: string;
    dashboard: string;
    opportunities: string;
    myContent: string;
    documentation: string;
    about: string;
    notifications: string;
    myProfile: string;
    signout: string;
    prev: string;
    submit: string;
    information: string;
    creatorType: string;
    connect: string;
    send: string;
    copyright: string;
    approved: string;
    rejected: string;
    approvalNotRequired: string;
    submitted: string;
    updated: string;
    pendingApproval: string;
    changesRequired: string;
    // inReview: string;
    viewChangesRequired: string;
    hideChangesRequired: string;
    inScan: string;
    video: string;
    sentOn: string;
    additionalDescription: string;
    file: string;
    url: string;
    update: string;
    from: string;
    modalConfirmationTitle: string;
    confirmationDesc1: string;
    confirmationDesc2: string;
    connectedAccounts: string;
    legalDocuments: string;
    paymentInformation: string;
    justNow: string;
    today: string;
    policy: string;
    disclosure: string;
    toasts: {
      contentSubmittedTitle: string;
      contentSubmittedDescription: string;
      declineInvitationTitle: string;
      declineInvitationDescription: string;
    };
    months: {
      January: string;
      February: string;
      March: string;
      April: string;
      May: string;
      June: string;
      July: string;
      August: string;
      September: string;
      October: string;
      November: string;
    };
    declineTermsAndCondition: string;
    close: string;
    preferences: string;
    theSims?: string;
    documentationCenter?: string;
  };
};

export class CommonPageMapper implements MicroCopyMapper {
  map(microCopies: Record<string, string>): CommonPageLabels {
    const microCopy = new MicroCopy(microCopies);

    return {
      commonPageLabels: {
        creatorNetwork: microCopy.get("common.creatorNetwork"),
        how: microCopy.get("common.how"),
        reward: microCopy.get("common.reward"),
        perks: microCopy.get("common.perks"),
        faq: microCopy.get("common.faq"),
        faqs: microCopy.get("common.faqs"),
        messages: microCopy.get("common.messages"),
        policies: microCopy.get("common.policies"),
        legal: microCopy.get("common.legal"),
        disclaimer: microCopy.get("common.disclaimer"),
        updates: microCopy.get("common.updates"),
        terms: microCopy.get("common.terms"),
        privacy: microCopy.get("common.privacy"),
        rights: microCopy.get("common.rights"),
        report: microCopy.get("common.report"),
        home: microCopy.get("common.home"),
        works: microCopy.get("common.works"),
        rewards: microCopy.get("common.rewards"),
        signIn: microCopy.get("common.signIn"),
        logIn: microCopy.get("common.logIn"),
        apply: microCopy.get("common.apply"),
        applyNow: microCopy.get("common.applyNow"),
        requestToJoin: microCopy.get("common.requestToJoin"),
        cancel: microCopy.get("common.cancel"),
        ok: microCopy.get("common.ok"),
        calendar: "Calendar",
        paymentInfo: microCopy.get("common.paymentInfo"),
        completed: microCopy.get("common.completed"),
        // calendar: microCopy.get("common.calendar"),
        discard: microCopy.get("common.discard"),
        next: microCopy.get("common.next"),
        save: microCopy.get("common.save"),
        edit: microCopy.get("common.edit"),
        join: microCopy.get("common.join"),
        back: microCopy.get("common.back"),
        browse: microCopy.get("common.browse"),
        yes: microCopy.get("common.yes"),
        no: microCopy.get("common.no"),
        remove: microCopy.get("common.remove"),
        pageNotFound: microCopy.get("common.pageNotFound"),
        pageNotFoundContent: microCopy.get("common.pageNotFoundContent"),
        unhandledError: microCopy.get("common.unhandledError"),
        unhandledErrorMessage: microCopy.get("common.unhandledErrorMessage"),
        logout: microCopy.get("common.logout"),
        notifyMe: microCopy.get("common.notifyMe"),
        emailAddress: microCopy.get("common.emailAddress"),
        unauthorized: microCopy.get("common.unauthorized"),
        dashboard: microCopy.get("common.dashboard"),
        opportunities: microCopy.get("common.opportunities"),
        myContent: microCopy.get("common.myContent"),
        documentation: microCopy.get("common.documentation"),
        about: microCopy.get("common.about"),
        notifications: microCopy.get("common.notifications"),
        myProfile: microCopy.get("common.myProfile"),
        signout: microCopy.get("common.signout"),
        prev: microCopy.get("common.prev"),
        franchises: microCopy.get("common.franchises"),
        submit: microCopy.get("common.submit"),
        information: microCopy.get("common.information"),
        creatorType: microCopy.get("common.creatorType"),
        connect: microCopy.get("common.connect"),
        send: microCopy.get("common.send"),
        copyright: microCopy.get("common.copyright"),
        approved: microCopy.get("common.approved"),
        rejected: microCopy.get("common.rejected"),
        approvalNotRequired: microCopy.get("common.approvalNotRequired"),
        submitted: microCopy.get("common.submitted"),
        updated: microCopy.get("common.updated"),
        pendingApproval: microCopy.get("common.pendingApproval"),
        changesRequired: microCopy.get("common.changesRequired"),
        // inReview: microCopy.get("common.inReview"),
        viewChangesRequired: microCopy.get("common.viewChangesRequired"),
        hideChangesRequired: microCopy.get("common.hideChangesRequired"),
        inScan: microCopy.get("common.inScan"),
        video: microCopy.get("common.video"),
        sentOn: microCopy.get("common.sentOn"),
        additionalDescription: microCopy.get("common.additionalDescription"),
        file: microCopy.get("common.file"),
        url: microCopy.get("common.url"),
        update: microCopy.get("common.update"),
        from: microCopy.get("common.from"),
        modalConfirmationTitle: microCopy.get("common.modalConfirmationTitle"),
        confirmationDesc1: microCopy.get("common.confirmationDesc1"),
        confirmationDesc2: microCopy.get("common.confirmationDesc2"),
        connectedAccounts: microCopy.get("common.connectedAccounts"),
        legalDocuments: microCopy.get("common.legalDocuments"),
        paymentInformation: microCopy.get("common.paymentInformation"),
        justNow: microCopy.get("common.justNow"),
        today: microCopy.get("common.today"),
        policy: microCopy.get("common.policy"),
        disclosure: microCopy.get("common.disclosure"),
        contract: microCopy.get("common.contract"),
        preferences: "Preferences",
        toasts: {
          contentSubmittedTitle: microCopy.get("common.toasts.contentSubmittedTitle"),
          contentSubmittedDescription: microCopy.get("common.toasts.contentSubmittedDescription"),
          declineInvitationTitle: microCopy.get("common.toasts.declineInvitationTitle"),
          declineInvitationDescription: microCopy.get("common.toasts.declineInvitationDescription")
        },
        months: {
          January: microCopy.get("common.months.January"),
          February: microCopy.get("common.months.February"),
          March: microCopy.get("common.months.March"),
          April: microCopy.get("common.months.April"),
          May: microCopy.get("common.months.May"),
          June: microCopy.get("common.months.June"),
          July: microCopy.get("common.months.July"),
          August: microCopy.get("common.months.August"),
          September: microCopy.get("common.months.September"),
          October: microCopy.get("common.months.October"),
          November: microCopy.get("common.months.November")
        },
        declineTermsAndCondition: microCopy.get("common.declineTermsAndCondition"),
        close: microCopy.get("common.close"),
        theSims: microCopy.get("common.theSims"),
        documentationCenter: microCopy.get("common.documentationCenter")
      }
    };
  }
}
