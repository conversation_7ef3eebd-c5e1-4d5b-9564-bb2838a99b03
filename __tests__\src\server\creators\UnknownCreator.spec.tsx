import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import UnknownCreator from "@src/server/creators/UnknownCreator";
import Random from "__tests__/factories/Random";

describe("UnknownCreator", () => {
  it("should initialize properties correctly", () => {
    const originEmail = "<EMAIL>";
    const nucleusId = Random.nucleusId();
    const defaultGamerTag = "gamer123";
    const dateOfBirth = 631152000000;
    const message = "Test error message";

    const unknownCreator = new UnknownCreator(originEmail, nucleusId, defaultGamerTag, dateOfBirth, message);

    expect(unknownCreator.originEmail).toBe(originEmail);
    expect(unknownCreator.nucleusId).toBe(nucleusId);
    expect(unknownCreator.userName).toBe(defaultGamerTag);
    expect(unknownCreator.dateOfBirth).toBe(LocalizedDate.format(new Date(dateOfBirth), "YYYY-MM-DD"));
    expect(unknownCreator.message).toBe(message);
  });

  it("should create an instance with static method withIdentity", () => {
    const originEmail = "<EMAIL>";
    const nucleusId = Random.nucleusId();
    const defaultGamerTag = "gamer123";
    const dateOfBirth = 631152000000;

    const unknownCreator = UnknownCreator.withIdentity(originEmail, nucleusId, defaultGamerTag, dateOfBirth);

    expect(unknownCreator.originEmail).toBe(originEmail);
    expect(unknownCreator.nucleusId).toBe(nucleusId);
    expect(unknownCreator.userName).toBe(defaultGamerTag);
    expect(unknownCreator.dateOfBirth).toBe(LocalizedDate.format(new Date(dateOfBirth), "YYYY-MM-DD"));
    expect(unknownCreator.message).toBe(`Cannot find creator with Nucleus ID '${nucleusId}'`);
  });
});
