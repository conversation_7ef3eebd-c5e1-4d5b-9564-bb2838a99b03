export const submitWebsiteContentTranslations = {
  title: "Add content from a Website",
  formLabels: {
    contentTitle: "Content Title",
    contentUrl: "Please enter the Content URL:",
    contentTitlePlaceholder: "Add Content Title",
    contentUrlPlaceholder: "Example: https://www.mywebsite.com/mylink",
    contentType: "Content Type",
    contentDescription: "Description",
    contentDescriptionPlaceholder: "Content Description",
    maxcharacterLimit: "800 characters"
  },
  buttonsLabels: {
    cancel: "Cancel",
    submit: "Submit",
    close: "Close"
  },
  contentSubmissionLabels: {
    contentTitleRequired: "Content Title is required",
    contentUrlRequired: "Content URL is required",
    contentTypeRequired: "Content Type is required",
    duplicateUrlMessage: "This URL has already been submitted.",
    unsafeUrlMessage: "You can not submit content from this website or domain.",
    validUrlMessage: "Please enter a valid URL.",
    success: {
      title: "Content submission successful",
      content: "You have successfully submitted content for this opportunity!"
    },
    error: {
      title: "File Upload Failed",
      content: "There was an error uploading your file. Please try again."
    },
    websiteUrlMessage:
      "URLs from the following social channels are not accepted: (Facebook, Twitch, Instagram, YouTube, TikTok)",
    contentTitleLongMessage: "Content Title is too long",
    maxLimitMessage: "Please select file within allowed size limits.",
    invalidFileTypeMessage: "Please select file with allowed extensions.",
    contentDescriptionRequired: "Content Description is required",
    contentDescriptionLongMessage: "Content Description is too long"
  }
};
