import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";

import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ClearTermsAndConditionsStatusController from "@src/server/controllers/ClearTermsAndConditionsStatusController";
import { aUser } from "__tests__/factories/User/User";
import CachedTermsAndConditions from "@src/server/pactSafe/CachedTermsAndConditions";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import config from "config";
import Random from "../../../factories/Random";

describe("ClearTermsAndConditionsStatusController", () => {
  let controller: ClearTermsAndConditionsStatusController;
  const id = Random.uuid();
  const session = {
    save: jest.fn(),
    identity: Identity.fromStored({
      type: "CREATOR",
      id
    } as StoredIdentity)
  };

  it("clears signed status detail from cache", async () => {
    const user = aUser({ id });
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "DELETE",
      url: `/v2/terms-and-conditions/terms-and-conditions-status`,
      query: { locale: "en-us" },
      session: { ...session, user }
    });
    const signedStatus = { signedStatusWithProgram: jest.fn() };
    controller = new ClearTermsAndConditionsStatusController((signedStatus as unknown) as CachedTermsAndConditions);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(signedStatus.signedStatusWithProgram).toHaveBeenCalledTimes(1);
    expect(signedStatus.signedStatusWithProgram).toHaveBeenCalledWith(id, "en-us", config.PROGRAM_CODE);
  });
});
