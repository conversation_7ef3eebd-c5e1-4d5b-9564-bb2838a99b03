.signup-complete-container {
  @apply relative flex min-h-screen w-full flex-col items-center overflow-hidden bg-[#182FFF] pb-meas35;
}

.signup-complete-bg {
  @apply absolute h-full w-full bg-migration-background-tablet bg-contain bg-bottom bg-no-repeat xl:bg-cover 2xl:bg-migration-shape;
}

.signup-complete-header {
  @apply z-10 mt-meas20 hidden w-full px-meas8 py-meas4 md:block;
}

.signup-complete-characters {
  @apply object-contain xs:h-[183px] xs:w-[230px] md:h-[276px] md:w-[514px] xl:h-[328px];
}

.signup-complete-horizontal-line {
  @apply z-10 h-meas1 w-full bg-gray-40 opacity-40;
}

.signup-complete-main-content {
  @apply z-10 mt-meas35 flex w-[288px] flex-grow flex-col items-center md:mt-meas34 md:w-[630px] xl:mt-meas0;
}

.signup-complete-character-section {
  @apply mb-meas4 flex items-center justify-center;
}

.signup-complete-content-container {
  @apply mb-meas16 mt-meas20 flex flex-col items-center justify-center gap-y-meas10 text-center text-white md:mt-meas16;
}

.signup-complete-title {
  @apply font-title-font text-mobile-h1 md:text-desktop-h3;
}

.signup-complete-subtitle {
  @apply font-text-bold text-desktop-body-large;
}

.signup-complete-body {
  @apply font-text-regular text-desktop-body-large;
}

.signup-complete-button-container {
  @apply mt-meas16;
}

.signup-complete-logo {
  @apply ml-meas4;
}
