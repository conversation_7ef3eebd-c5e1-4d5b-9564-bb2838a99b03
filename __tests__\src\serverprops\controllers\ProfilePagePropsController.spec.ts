import { aCreatorProgram, aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import ProfilePagePropsController from "@src/serverprops/controllers/ProfilePagePropsController";
import { createMocks, MockResponse } from "node-mocks-http";
import Random from "../../../factories/Random";
import ContentManagementService from "@src/services/ContentManagementService";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import flags from "@src/utils/feature-flags";
import { FacebookPage } from "@src/server/channels/ConnectedAccountsHttpClient";
import { OAuthError } from "@src/server/channels/tiktok/ConnectTikTokAccountController";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");
jest.mock("../../../../src/utils/feature-flags");
jest.mock("next-i18next/serverSideTranslations", () => ({
  serverSideTranslations: jest.fn().mockResolvedValue({
    _nextI18Next: {
      initialI18nStore: {},
      initialLocale: "en-us",
      userConfig: {}
    }
  })
}));

describe("ProfilePagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";
  const identity = Identity.fromStored(
    aStoredIdentity({
      programs: [aCreatorProgram({ code: program })]
    })
  );

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props with authenticated user", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = { identity, save: jest.fn() };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const isCountriesByTypeEnabled = true;
    (flags.isCountriesByTypeEnabled as jest.Mock).mockReturnValue(isCountriesByTypeEnabled);
    const controller = new ProfilePagePropsController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(flags.isCountriesByTypeEnabled).toHaveBeenCalled();
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pages: [],
        error: null,
        invalidTikTokScope: false,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        _nextI18Next: {
          initialI18nStore: {},
          initialLocale: "en-us",
          userConfig: {}
        },
        FLAG_COUNTRIES_BY_TYPE: isCountriesByTypeEnabled
      }
    });
  });

  it("gets server side props with Facebook pages", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const fbPages: FacebookPage[] = [
      { id: "123", name: "Test Page 1", accessToken: "token1" },
      { id: "456", name: "Test Page 2", accessToken: "token2" }
    ];
    req.session = { identity, fbPages: { pages: fbPages }, save: jest.fn() };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const isCountriesByTypeEnabled = true;
    (flags.isCountriesByTypeEnabled as jest.Mock).mockReturnValue(isCountriesByTypeEnabled);
    const controller = new ProfilePagePropsController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(flags.isCountriesByTypeEnabled).toHaveBeenCalled();
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pages: fbPages,
        error: null,
        invalidTikTokScope: false,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        _nextI18Next: {
          initialI18nStore: {},
          initialLocale: "en-us",
          userConfig: {}
        },
        FLAG_COUNTRIES_BY_TYPE: isCountriesByTypeEnabled
      }
    });
  });

  it("gets server side props with OAuth error", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    const error: OAuthError = {
      code: "access_denied",
      message: "The user denied the request"
    };
    req.session = { identity, error, save: jest.fn() };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const isCountriesByTypeEnabled = false;
    (flags.isCountriesByTypeEnabled as jest.Mock).mockReturnValue(isCountriesByTypeEnabled);
    const controller = new ProfilePagePropsController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(flags.isCountriesByTypeEnabled).toHaveBeenCalled();
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pages: [],
        error,
        invalidTikTokScope: false,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        _nextI18Next: {
          initialI18nStore: {},
          initialLocale: "en-us",
          userConfig: {}
        },
        FLAG_COUNTRIES_BY_TYPE: isCountriesByTypeEnabled
      }
    });
  });

  it("gets server side props for the profile page with invalid TikTok scope", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    req.session = { identity, INVALID_TIKTOK_SCOPE: true, save: jest.fn() };
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const isCountriesByTypeEnabled = true;
    (flags.isCountriesByTypeEnabled as jest.Mock).mockReturnValue(isCountriesByTypeEnabled);
    const controller = new ProfilePagePropsController(options, contents, currentLocale, "");

    const props = await controller.handle(req, res);

    expect(flags.isCountriesByTypeEnabled).toHaveBeenCalled();
    expect(props).toEqual({
      props: {
        runtimeConfiguration: configuration,
        pages: [],
        error: null,
        invalidTikTokScope: true,
        user: {
          analyticsId: identity.analyticsId(),
          needsMigration: identity.needsMigration,
          username: identity.username,
          status: identity.programs[0]?.status,
          avatar: identity.avatar,
          tier: identity.tier,
          isPayable: identity.payable,
          isFlagged: identity.flagged,
          programs: identity.programs.map((program) => program.code),
          creatorCode: identity.creatorCode,
          type: "CREATOR"
        },
        _nextI18Next: {
          initialI18nStore: {},
          initialLocale: "en-us",
          userConfig: {}
        },
        FLAG_COUNTRIES_BY_TYPE: isCountriesByTypeEnabled
      }
    });
  });
});
