import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewCreatorProfileWithCreatorCodeController from "@src/server/controllers/ViewCreatorProfileWithCreatorCodeController";
import CreatorsWithCreatorCodeHttpClient from "@src/server/creators/CreatorsWithCreatorCodeHttpClient";
import { aCreatorWithCreatorCode } from "__tests__/factories/creators/CreatorWithCreatorCode";
import { Identity, StoredIdentity } from "@eait-playerexp-cn/identity-types";
import Random from "__tests__/factories/Random";

describe("ViewCreatorProfileWithCreatorCodeController", () => {
  let controller: ViewCreatorProfileWithCreatorCodeController;

  beforeEach(() => jest.clearAllMocks());

  it("shows a creator with creatorCode", async () => {
    const userId = Random.uuid();
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/v4/creators`,
      session: {
        identity: Identity.fromStored(({
          id: userId
        } as unknown) as StoredIdentity)
      }
    });
    const creator = aCreatorWithCreatorCode();
    const creators = { withId: jest.fn().mockResolvedValue(creator) };
    controller = new ViewCreatorProfileWithCreatorCodeController(
      (creators as unknown) as CreatorsWithCreatorCodeHttpClient
    );

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(creator);
    expect(creators.withId).toHaveBeenCalledTimes(1);
    expect(creators.withId).toHaveBeenCalledWith(userId);
  });
});
