import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type SubmittedContentFeedbackCriteria = {
  contentId: string;
};

export type ContentFeedback = {
  requestedBy: string;
  feedbackStatus: string;
  description: string;
  lastUpdateDate: number;
  contentUrl: string;
  contentVersion: string;
};

export type ContentFeedbackLog = {
  contentsFeedback: ContentFeedback[];
};

@Service()
class ContentFeedbackHttpClient {
  constructor(@Inject("contentSubmissionClient") private client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Submitted-Content/operation/viewContentFeedback View Submitted Content Feedback}
   */
  async matching(criteria: SubmittedContentFeedbackCriteria): Promise<ContentFeedbackLog> {
    const { contentId } = criteria;

    const contentFeedbackResponse = await this.client.get(`/v1/submitted-content-feedback/${contentId}`);
    return Promise.resolve(contentFeedbackResponse.data);
  }
}

export default ContentFeedbackHttpClient;
