import React from "react";

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  id?: string;
  url: string;
  title?: string;
  width?: number;
  height?: number;
  className?: string;
}

const Image = ({ id, url, title, width, height, className }: ImageProps) => {
  return <img src={url} alt={title} data-testid={id} {...{ width, height, className }} />;
};

export default Image;
