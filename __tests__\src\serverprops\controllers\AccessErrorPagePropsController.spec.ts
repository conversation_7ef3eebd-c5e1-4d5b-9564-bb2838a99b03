import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import AccessErrorPagePropsController from "@src/serverprops/controllers/AccessErrorPagePropsController";
import ContentManagementService from "@src/services/ContentManagementService";
import Random from "../../../factories/Random";

jest.mock("../../../../src/services/ContentManagementService");
jest.mock("../../../../src/configuration/runtimeConfiguration");

describe("AccessErrorPagePropsController", () => {
  const program = Random.programCode();
  const options = { program };
  const contents = ({} as unknown) as ContentManagementService;
  const currentLocale = "en-us";

  beforeEach(() => jest.clearAllMocks());

  it("gets server side props for the access error page", async () => {
    const configuration = {};
    (runtimeConfiguration as jest.Mock).mockReturnValue(configuration);
    const labels = {};
    contents.getPageLabels = jest.fn().mockReturnValue(labels);
    const controller = new AccessErrorPagePropsController(options, contents, currentLocale);

    const props = await controller.handle();

    expect(contents.getPageLabels).toHaveBeenCalledWith(currentLocale, "error");
    expect(props).toEqual({ props: { pageLabels: labels, runtimeConfiguration: configuration } });
  });
});
