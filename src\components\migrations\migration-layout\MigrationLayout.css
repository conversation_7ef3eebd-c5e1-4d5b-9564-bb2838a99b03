.mg-container {
  @apply absolute z-0 mt-meas13 flex min-h-full w-full flex-col items-center overflow-y-hidden bg-[#182FFF] xs:overflow-x-hidden;
}

.mg-container .stepper-inactive {
  @apply text-white;
}

.mg-container .stepper-complete {
  @apply text-success-40;
}

.mg-container.mg-loading {
  @apply h-full;
}
.mg-bg {
  @apply absolute h-full w-full bg-migration-background-tablet bg-cover bg-bottom bg-no-repeat xl:bg-migration-shape;
  z-index: -1;
}

.mg-page {
  @apply mb-[194px] flex h-full min-h-screen w-full flex-col items-center text-gray-10;
}

.mg-page form {
  @apply mx-auto flex flex-col items-center justify-center xs:w-[280px] md:w-[760px] lg:w-[900px] xl:w-[672px];
}

.mg-page .onboarding-terms-and-condtions-container {
  @apply xs:pr-meas18;
}

.interested-creator {
  @apply w-full;
}

.onboarding-information-container {
  @apply flex items-center justify-center;
}

.mg-container-header-hidden .terms-and-conditions {
  @apply pt-[212px];
}
