.PHONY: help
help: ## Show help
	@echo Please specify a build target. The choices are:
	@grep -E '^[0-9a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: bootstrap
bootstrap: ## Create default .env file
	@echo "Creating default .env file..."
	@cp .env.dist .env
	@echo "Installing Amplitude CLI"
	@npm install -g @amplitude/ampli
	@echo "Installing project dependencies"
	@npm ci

.PHONY: migrate
migrate: ## Run pending database migrations for the Operations API
	@echo "Running pending database migrations..."
	@docker-compose pull --include-deps operations-migrations
	@docker-compose run --rm operations-migrations migrate

.PHONY: rollback
rollback: ## Rollback latest database migration for the Operations API
	@echo "Rolling back last database migration..."
	@docker-compose run --rm operations-migrations rollback

.PHONY: lint
lint: ## Check code format and run test suite
	@echo "Checking coding standards..."
	@npm run format:check
	@echo "Checking linting rules..."
	@npm run lint

.PHONY: check/ci
check/ci: ## Tun test coverage and verify types validity
	@echo "Running test suite with code coverage..."
	@npm run test:coverage
	@echo "Checking validity of types..."
	@npm run test:types

.PHONY: mutation
mutation: # Run mutation test suite
	@echo "Nothing to do here"

.PHONY: format
format: ## Format code
	@echo "Applying coding standard formatting..."
	@npm run lint -- --fix
	@npm run format

.PHONY: docs
docs: ## Start documentation container
	@echo "Starting container for documentation..."
	@docker-compose up -d docs

.PHONY: stop
stop: ## Stop Docker containers
	@echo "Stopping Docker containers..."
	@docker-compose stop

.PHONY: publish
publish: ## Publish Docker Image
	@echo "Building Docker image  $(REGISTRY):$(TAG)"
	@docker build -t $(REGISTRY):$(TAG) . $(BUILD_ARGS)
	@echo "Publishing Docker image $(REGISTRY):$(TAG)"
	@docker push $(REGISTRY):$(TAG)

.PHONY: login
login: ## Log in to GitLab Registry
	@echo "Log in to GitLab Registry"
	@docker login -u $(CN_USER) -p $(CN_GITLAB_KEY) registry.gitlab.ea.com

.PHONY: redis
redis: ## Create a tunnel to Redis
	@echo "Creating a tunnel to Redis cluster. Please keep the private key with name id_rsa in path: ~/.ssh/id_rsa"
	@echo "Creating tunnel with bastion user: "kgonti
	@ssh -M -S ~/tunnel -o "ExitOnForwardFailure yes" -fN -L 6379:eait-plex-cn-preprod-redis.uusj74.clustercfg.use1.cache.amazonaws.com:6379 kgonti@54.163.238.80 -i ~/.ssh/id_rsa
	
.PHONY: redis/status
redis/status: ## Close existing tunnel to Redis
	@echo "Checking SSH Redis tunnel status"
	@ssh -S ~/tunnel -O check kgonti@54.163.238.80

.PHONY: redis/stop
redis/stop: ## Close existing tunnel to Redis
	@echo "Closing SSH Redis tunnel"
	@ssh -S ~/tunnel -O exit kgonti@54.163.238.80
