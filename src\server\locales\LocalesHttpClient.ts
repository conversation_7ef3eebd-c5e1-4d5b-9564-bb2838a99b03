import { Inject, Service } from "typedi";
import Locales from "./Locales";
import Locale from "./Locale";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
export default class LocalesHttpClient implements Locales {
  constructor(@Inject("metadataClient") private client: TraceableHttpClient) {}

  async all(): Promise<Array<Locale>> {
    const response = await this.client.get("/locales");
    return Promise.resolve(response.data.map((item) => Locale.fromApi(item)));
  }
}
