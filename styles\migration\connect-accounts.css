.mg-connect-accounts {
  @apply text-center md:w-[630px];
}
.myprofile-view > .mg-connect-accounts {
  @apply m-[0] text-left;
}
.mg-connect-accounts.card-container {
  @apply flex flex-row align-middle;
}
.mg-connect-accounts.empty-card {
  @apply h-[231px] w-[231px];
}
.mg-connect-accounts-title {
  @apply pb-meas10 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 xl:w-[698px];
}
.mg-connect-accounts-description {
  @apply font-text-regular text-desktop-body-default xl:w-[698px];
}
.connect-accounts-form {
  @apply grid grid-cols-1 xl:grid xl:grid-cols-none;
}
.connect-accounts {
  @apply mb-[70px] flex  w-auto flex-1 flex-col;
}
.mg-connected-accounts-container {
  @apply mt-meas16 flex w-[293px] flex-col border-t border-gray-40 border-opacity-[0.33] pt-meas16 md:w-[640px] md:items-center xl:w-[889px];
}
.onboarding-creator .mg-connected-accounts-container {
  @apply mt-meas16 flex w-[293px] flex-col border-t border-gray-40 border-opacity-[0.33] pt-meas16 md:w-[730px] md:items-center xl:w-[756px];
}
.myprofile-view .mg-connected-accounts-container {
  @apply items-center md:items-start xl:w-[790px];
}

.myprofile-view > .mg-connect-accounts > h4 {
  @apply pb-meas16 font-display-bold font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}

.myprofile-view > .mg-connect-accounts > .mg-connect-accounts-title {
  @apply xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.myprofile-view .mg-connected-accounts-title {
  @apply self-start xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.mg-connected-accounts-title {
  @apply pb-meas10 font-display-bold text-desktop-h4 xs:text-center md:self-center  xl:self-start;
}
.connect-accounts-container .card-container {
  @apply flex flex-row align-middle;
}
.connect-accounts-container .empty-card {
  @apply mb-[10px] h-[231px] w-[231px];
}
.connect-accounts-container .connected-acc-card-container .empty-card {
  box-shadow: rgba(115, 204, 117, 0.8) 0px 1px 4px, rgba(115, 204, 117, 0.8) 0px 0px 1px 4px;
}
.connect-accounts-container {
  @apply flex flex-col items-center;
}
.myprofile-view.connect-accounts-container {
  @apply flex flex-col items-start text-black;
}
.connect-account-card-container {
  @apply grid grid-cols-1 gap-meas3 self-center md:grid-cols-3 md:gap-meas13 xl:grid-cols-4 xl:px-meas0;
}
/* My Profile view css for connect-card(connected account) here  */
.myprofile-view .connect-account-card-container {
  @apply grid grid-cols-1 gap-meas16 md:grid-cols-3 md:self-start xl:grid-cols-3 xl:gap-[30px];
}
.onboarding-creator .connect-account-card-container {
  @apply grid grid-cols-1 gap-meas3 self-center md:grid-cols-3 md:gap-meas13 lg:grid-cols-3 lg:gap-y-meas4 lg:px-meas0;
}
.connected-acc-card-container {
  @apply grid grid-cols-1 justify-center gap-meas16 md:grid-cols-3 md:items-center xl:grid-cols-4 xl:gap-[30px] xl:self-start xl:px-meas0;
}

.onboarding-creator .connected-acc-card-container {
  @apply grid grid-cols-1 justify-center gap-meas16 md:grid-cols-3 md:items-center xl:grid-cols-3 xl:gap-[30px] xl:self-start xl:px-meas0;
}

.onboarding-creator .mg-connected-accounts-container .mg-connected-accounts-title {
  @apply text-left md:flex md:w-full md:items-start;
}

.onboarding-creator .account-container {
  @apply flex flex-col items-center justify-center p-meas10;
}
.account-container > .empty-card > button {
  @apply flex items-center justify-center;
}
.account-container > .empty-card > button > span {
  @apply ml-meas4;
}
.account-name {
  @apply flex justify-center p-meas4 font-text-regular xs:text-mobile-body-default md:justify-center md:p-meas0 md:text-tablet-body-default lg:text-desktop-body-default xl:justify-center;
}
.connect-account-card {
  @apply flex flex-row flex-wrap justify-start xl:min-w-[1052px];
}
.connect-accounts-add-icon {
  @apply h-meas10 w-meas10;
}
.add-account-text {
  @apply ml-meas4 self-center font-display-regular font-bold text-white xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.connected-accounts-image {
  @apply h-meas32 w-meas32;
}
.connected-youTube-image {
  @apply mb-meas8 h-[50px] w-meas34;
}
.accounts-image {
  @apply mb-meas33 h-meas32 w-meas32;
}
.youTube-image {
  @apply mb-[5.5rem] h-[50px] w-meas34;
}
.connect-accounts-add-bt {
  @apply flex w-full flex-row xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.connect-accounts-remove-bt {
  @apply mt-meas16 flex w-full cursor-pointer flex-row justify-center text-gray-10;
}
.remove-account-content {
  @apply mt-[2px] self-center font-text-regular xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.connect-accounts-delete-icon {
  @apply flex h-meas13 w-meas13 items-center justify-center;
}

.connect-accounts-delete-icon > svg {
  @apply h-[14px] w-[12px];
}

.user-content {
  @apply mt-meas6 align-middle font-text-regular text-white xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.user-name-content {
  @apply break-all text-center text-white;
}

.connect-acc-confirmation-footer {
  @apply flex;
}

.connect-acc-confirmation-footer > button {
  @apply flex justify-center;
}

.connect-acc-button-remove {
  @apply font-bold;
}
.connect-accounts-form .form-cancel {
  @apply mr-meas2 md:mr-meas10;
}

main > div.myprofile-view form {
  @apply items-start;
}

.fb-pages-cont {
  @apply flex flex-col items-start justify-start;
}

section.fb-pages {
  @apply p-meas8;
}

.mg-modal-confirmation-footer.fb-footer,
.fb-footer > button {
  @apply w-auto;
}
.mg-modal-confirmation-footer .mg-bt-yes {
  @apply justify-start;
}

.fb-pages label {
  @apply pl-meas4;
}

/* My Profile view css for empty connect-card(add account) here  */
.myprofile-view div.connected-acc-card-container {
  @apply items-start md:items-center
  md:justify-center xl:grid xl:grid-cols-3 xl:justify-start xl:gap-[30px];
}

.connect-account-insta-steps {
  @apply text-link;
}

.connect-account-insta-warning {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.mg-connected-accounts-input-container {
  @apply mt-meas4 flex w-[288px] flex-col border-t border-gray-40 border-opacity-[0.33] pt-meas16 md:w-[640px] md:items-center xl:w-[889px];
}

.onboarding-creator .mg-connected-accounts-input-container {
  @apply mt-meas4 flex w-[288px] flex-col border-t border-gray-40 border-opacity-[0.33] pt-meas16 md:w-[730px] md:items-center xl:w-[756px];
}
.myprofile-view .mg-connected-accounts-input-container {
  @apply items-center md:items-start xl:w-[790px];
}

.onboarding-creator .mg-connected-accounts-input-container .mg-connected-accounts-title {
  @apply text-left md:flex md:w-full md:items-start;
}
