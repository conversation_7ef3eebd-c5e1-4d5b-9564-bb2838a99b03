import { useCallback, useEffect } from "react";
import Join<PERSON><PERSON><PERSON>unityFooter from "../opportunities/JoinOpportunityFooter";
import { useRouter } from "next/router";
import { useAppContext } from "@src/context";
import Form from "../Form";
import OperationsService from "@src/services/OperationsService";
import {
  ERROR,
  JOIN_OPPORTUNITY_FORM,
  onToastClose,
  PARTICIPATION,
  toastContent,
  useAsync,
  VALIDATION_ERROR
} from "../../utils";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { useDependency } from "@src/context/DependencyContext";
import { ValidationError } from "@src/utils/types";
import { OpportunityWithDeliverables } from "@src/services/OpportunityService";
import BrowserAnalytics from "@src/analytics/BrowserAnalytics";
import { CommonLabelsProps } from "@src/config/translations/common";
import { OpportunityLabels } from "@components/opportunities/OpportunityWithPerksHeader";
import { ContentSubmissionLabels } from "@src/config/translations/content-submission";

export type ContentGuidelinesProps = {
  opportunity: OpportunityWithDeliverables;
  layout: CommonLabelsProps;
  opportunitiesLabels: OpportunityLabels & { contentGuidelines: string };
  contentSubmissionLabels: ContentSubmissionLabels;
  analytics: BrowserAnalytics;
};
export default function ContentGuidelines({
  opportunity,
  layout,
  opportunitiesLabels,
  contentSubmissionLabels,
  analytics
}: ContentGuidelinesProps) {
  const { errorHandler, client } = useDependency();
  const {
    main: { unhandledError }
  } = layout;
  const router = useRouter();
  const {
    dispatch,
    state: { joinOpportunityFormData, isError, isValidationError }
  } = useAppContext() || {};
  const stableDispatch = useCallback(dispatch, []);
  const { error: errorToast } = useToast();
  const operationsService = new OperationsService(client);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent((isValidationError as unknown) as ValidationError[])}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, dispatch)
        }
      );
    }
  }, [isError, isValidationError, unhandledError]);

  const submitHandle = useCallback(
    async (data) => {
      try {
        analytics.completedJoinOpportunityFlow({
          locale: router.locale,
          opportunity
        });
        const participation = await operationsService.saveParticipation(
          { ...joinOpportunityFormData, ...data },
          opportunity
        );
        stableDispatch({ type: PARTICIPATION, data: participation.data });
        operationsService.navigateToJoinOpportunityNextSteps(opportunity, router);
      } catch (e) {
        errorHandler(dispatch, e);
      } finally {
        stableDispatch({ type: JOIN_OPPORTUNITY_FORM, data: null });
      }
    },
    [router, opportunity, joinOpportunityFormData, stableDispatch]
  );

  const { pending, execute: navigateToJoinOpportunityNextSteps } = useAsync(submitHandle, false);

  return (
    <div className="content-guidelines">
      <div className="content-guidelines-container">
        {opportunity?.contentSubmission?.guidelines && (
          <>
            <h5 className="content-guidelines-title">{opportunitiesLabels.contentGuidelines}</h5>
            <div
              className="content-guidelines-message"
              id="content-guidelines"
              dangerouslySetInnerHTML={{ __html: opportunity.contentSubmission.guidelines }}
            />
            {opportunity.hasAttachments && (
              <button className="content-guidelines-download-attachments">
                <a href={opportunity.attachmentsUrl} download>
                  {contentSubmissionLabels.downloadAttachments}
                </a>
              </button>
            )}
          </>
        )}

        <Form mode="onChange" onSubmit={navigateToJoinOpportunityNextSteps}>
          {opportunity && (
            <JoinOpportunityFooter
              {...{
                button: layout.buttons.join,
                back: layout.buttons.back,
                prev: opportunity.hasGameCodes
                  ? `/opportunities/${router.query.id}/registrations?step=game-code`
                  : `/opportunities/${opportunity.id}/registrations`,
                layout,
                pending
              }}
            />
          )}
        </Form>
      </div>
    </div>
  );
}
