import { render, screen } from "@testing-library/react";
import Image from "./Image";
import Random from "__tests__/factories/Random";
import { axe } from "jest-axe";

describe("Image", () => {
  const imageProps = {
    url: Random.imageUrl(),
    title: Random.string(),
    width: Random.number({ min: 400, max: 800 }),
    height: Random.number({ min: 400, max: 800 }),
    className: "rich-text-image"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders image with correct attributes", () => {
    render(<Image {...imageProps} />);

    const image = screen.getByRole("img");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", imageProps.url);
    expect(image).toHaveAttribute("alt", imageProps.title);
    expect(image).toHaveAttribute("width", imageProps.width.toString());
    expect(image).toHaveAttribute("height", imageProps.height.toString());
    expect(image).toHaveClass("rich-text-image");
  });

  it("is accessible", async () => {
    const { container } = render(<Image {...imageProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
