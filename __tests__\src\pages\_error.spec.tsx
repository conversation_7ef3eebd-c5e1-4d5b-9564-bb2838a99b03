import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";
import React from "react";
import { screen } from "@testing-library/react";
import { axe } from "jest-axe";
import { useRouter } from "next/router";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import { useDependency } from "@src/context/DependencyContext";
import Error from "@src/pages/_error";
import { renderPage } from "__tests__/helpers/page";
import { useAppContext } from "@src/context";
import { useDetectScreen } from "@src/utils";
import { mockMatchMedia } from "__tests__/helpers/window";

jest.mock("../../../src/context", () => ({
  ...(jest.requireActual("../../../src/context") as Record<string, unknown>),
  useAppContext: jest.fn().mockReturnValue({ dispatch: jest.fn(), state: {} })
}));
jest.mock("../../../src/utils/hooks");
jest.mock("../../../src/context/DependencyContext");

describe("Error", () => {
  const ErrorProps = {
    statusCode: 404,
    sessionUser: AuthenticatedUserFactory.fromIdentity(Identity.fromStored(aStoredIdentity()), "creator_network", "")
  };

  mockMatchMedia();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDetectScreen as jest.Mock).mockImplementation((width) => width === 10000);
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
    (useDependency as jest.Mock).mockReturnValue({
      notificationsClient: {},
      configuration: {
        SUPPORTED_LOCALES: ["en-us"],
        PROGRAM_CODE: "sims_creator_program",
        MENU_ITEMS: {
          sims_creator_program: { label: "the_sims", gradients: ["#2E900A", "#6FF049"] },
          affiliate: { label: "Support A Creator", gradients: ["#6A30D3", "#A133DD"] },
          creator_network: { label: "Creator Network", gradients: ["#2D2EFE", "#4CCEF7"] }
        },
        user: { programs: ["the_sims"] }
      }
    });
  });

  it("shows 404 error page", async () => {
    renderPage(<Error {...ErrorProps} />);

    expect(screen.getByText("404")).toBeInTheDocument();
  });

  it("shows 500 error page", async () => {
    renderPage(<Error {...ErrorProps} statusCode={500} />);

    expect(screen.getByText("500")).toBeInTheDocument();
  });

  it("cleans up application context when leaving an error page, to prevent showing it in other pages", async () => {
    const dispatch = jest.fn();
    (useAppContext as jest.Mock).mockReturnValue({
      dispatch,
      state: {
        exceptionCode: false
      }
    });
    const { unmount } = renderPage(<Error {...ErrorProps} />);
    expect(screen.getByText("404")).toBeInTheDocument();

    unmount();

    expect(dispatch).toHaveBeenCalledTimes(1);
    expect(dispatch).toHaveBeenCalledWith({ data: false, type: "HAS_EXCEPTION" });
    expect(screen.queryByText("404")).not.toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = renderPage(<Error {...ErrorProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
