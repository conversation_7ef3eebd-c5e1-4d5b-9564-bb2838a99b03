.content-deliverables-container {
  @apply mx-auto pb-meas38 xs:px-meas8 md:px-meas32 xl:max-w-[1200px] xl:px-meas0;
}
.content-deliverables-container-with-flag {
  @apply mb-meas34 flex h-[100%] w-full max-w-[1024px] flex-col px-meas8 lg:mx-auto lg:max-w-[1024px] lg:px-meas24 xl:px-meas0;
}
.content-deliverables-disclosure-paragraph1,
.content-deliverables-link-submission-paragraph,
.content-deliverables-instruction-text {
  @apply mt-meas10  xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.content-deliverables-requirement-container {
  @apply flex w-full flex-col xl:flex-row;
}
.content-deliverables-disclosure-paragraph2,
.content-deliverables-link-submission,
.content-deliverables-available-resources-section,
.content-deliverables-section-title,
.content-deliverables-guidelines,
.content-deliverables-container-with-flag .opportunity-content-guide-line-container,
.content-deliverables-container .opportunity-content-guide-line-container {
  @apply mt-meas16;
}
.content-deliverables-after-join-opportunity-section-with-flag
  .content-deliverables-deliverable-card
  .deliverable-card-container
  .deliverable-card-details-section
  .deliverable-content-section {
  @apply xl:w-[800px];
}
.content-deliverables-link-submission-title,
.content-deliverables-disclosure-title,
.content-deliverables-section-title,
.content-deliverables-guidelines-title,
.content-deliverables-available-resources-title {
  @apply font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.content-deliverables-disclosure-paragraph2 {
  @apply xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.content-deliverables-download-watermark .btn-secondary,
.content-deliverables-download-attachments .btn-secondary {
  @apply xs:w-full md:w-[310px] xl:w-[261px];
}
.opportunity-content-guide-line-title {
  @apply text-font-normal;
}
.content-deliverables-container-with-flag .opportunity-description-content,
.content-deliverables-container-with-flag .opportunity-content-guide-line-container,
.content-deliverables-container-with-flag .content-deliverables-instruction-text {
  @apply text-font-normal;
}
.content-deliverables-download-attachments {
  @apply mt-meas10;
}
.content-deliverables-download-watermark {
  @apply mt-meas10;
}
.content-deliverables-download-buttons-container {
  @apply md:flex md:flex-row md:items-center;
}
.content-deliverables-deliverable-card {
  @apply mt-meas10;
}

/* Overriding css properties in opportunity-description, content-guide-line components from core-ui-kit to sync with other sections in deliverables page */
.content-deliverables-guidelines .opportunity-description,
.content-deliverables-disclosure .opportunity-content-guide-line-container {
  @apply mt-meas0;
}
.content-deliverables-container-with-flag .opportunity-description-title,
.content-deliverables-container-with-flag .opportunity-content-guide-line-title {
  @apply xs:text-mobile-h4  md:text-tablet-h4 lg:text-desktop-h4;
}
.content-deliverables-container .opportunity-description-title,
.content-deliverables-container .opportunity-content-guide-line-title {
  @apply xs:text-mobile-h4  md:text-tablet-h4 lg:text-desktop-h4;
}
.content-deliverables-link-submission .opportunity-content-guide-line-description {
  @apply mb-meas0;
}
.content-deliverables-joined-submission-window-not-started {
  @apply mt-meas10 font-text-regular text-[1rem] font-normal leading-6 tracking-wider xl:text-[1.25rem] xl:leading-[30px];
}
.content-deliverables-add-content-submitted-list {
  @apply mt-meas8;
}
/** Added this overrides, as the text color is inherited from the parent container*/

.content-deliverables-container .deliverables-badge-unlimited_content {
  @apply text-black;
}
.content-deliverables-container-with-flag .deliverables-badge-unlimited_content {
  @apply text-black;
}
