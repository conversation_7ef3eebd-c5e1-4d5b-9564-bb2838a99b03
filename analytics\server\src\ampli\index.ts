/* tslint:disable */
/* eslint-disable */
// @ts-nocheck
/**
 * Ampli - A strong typed wrapper for your Analytics
 *
 * This file is generated by Amplitude.
 * To update run 'ampli pull typescript_sdk'
 *
 * Required dependencies: @amplitude/node@^1.10.2
 * Tracking Plan Version: 34
 * Build: 1.0.0
 * Runtime: node.js:typescript-ampli
 *
 * [View Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest)
 *
 * [Full Setup Instructions](https://data.amplitude.com/itpxd/Creator%20Network/implementation/typescript_sdk)
 */

import { Identify as AmplitudeIdentify } from "@amplitude/identify";
import { init as initNodeClient, NodeClient, Response, Status } from "@amplitude/node";
import {
  BaseEvent,
  Event,
  EventOptions,
  GroupOptions,
  IdentifyEvent,
  IdentifyOptions,
  Options,
  MiddlewareExtra,
  SpecialEventType
} from "@amplitude/types";

export type Environment = "production" | "development";

export const ApiKey: Record<Environment, string> = {
  production: "",
  development: ""
};

/**
 * Default NodeClient Options. Contains tracking plan information.
 */
export const DefaultOptions: Partial<Options> = {
  plan: {
    version: "34",
    branch: "main",
    source: "typescript_sdk",
    versionId: "7d20b2a3-8ad8-472b-a31f-44cef958ab0f"
  },
  ...{
    ingestionMetadata: {
      source_name: "node.js-typescript-ampli",
      source_version: "1.0.0"
    }
  }
};

export interface LoadOptionsBase {
  disabled?: boolean;
}

export type LoadOptionsWithEnvironment = LoadOptionsBase & {
  environment: Environment;
  client?: { options?: Partial<Options> };
};
export type LoadOptionsWithApiKey = LoadOptionsBase & { client: { apiKey: string; options?: Partial<Options> } };
export type LoadOptionsWithClientInstance = LoadOptionsBase & { client: { instance: NodeClient } };

export type LoadOptions = LoadOptionsWithEnvironment | LoadOptionsWithApiKey | LoadOptionsWithClientInstance;

export interface IdentifyProperties {
  /**
   * Name of the community manager assigned to this creator
   */
  "Assigned Community Manager"?: string;
  /**
   * List of types of the social accounts connected to Creator Network
   *
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Connected Social Accounts"?: string[];
  /**
   * Comma separated list of languages that a creator speaks in their connected accounts
   */
  "Content Languages"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Creator Types"?: string[];
  /**
   * Creators that have been flagged by Community Managers in the CRM
   */
  "Is Flagged"?: boolean;
  /**
   * Creator has a completed and up-to-date Tipalti payment profile
   */
  "Is Payable"?: boolean;
  Locale?: string;
  "Primary Franchise"?: string;
  "Primary Platform"?: string;
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Secondary Franchise"?: string[];
  /**
   * | Rule | Value |
   * |---|---|
   * | Item Type | string |
   */
  "Secondary Platform"?: string[];
  /**
   * Identify the current "Status" of Creators on Creator Network to determine whether they are "Active", "Flagged", or "Unregistered"
   */
  Status?: string;
  /**
   * Identify whether a user is a "Creator" or "Applicant"
   */
  Type?: string;
}

export class Identify implements BaseEvent {
  event_type = SpecialEventType.IDENTIFY;

  constructor(public event_properties?: IdentifyProperties) {
    this.event_properties = event_properties;
  }
}

export class SignedInToCreatorNetwork implements BaseEvent {
  event_type = "Signed in to Creator Network";
}

const getDefaultPromiseResponse = () =>
  Promise.resolve<Response>({
    status: Status.Skipped,
    statusCode: 200
  });

function getIdentifyEvent(amplitudeIdentify: AmplitudeIdentify, userId?: string, deviceId?: string): IdentifyEvent {
  const identifyEvent = amplitudeIdentify.identifyUser("tmp-user-id-to-pass-validation");
  identifyEvent.user_id = userId;
  identifyEvent.device_id = deviceId;

  return identifyEvent;
}

// prettier-ignore
export class Ampli {
  private disabled: boolean = false;
  private amplitude: NodeClient | undefined;

  get client(): NodeClient {
    this.isInitializedAndEnabled();
    return this.amplitude!;
  }

  get isLoaded(): boolean {
    return this.amplitude != null;
  }

  private isInitializedAndEnabled(): boolean {
    if (!this.isLoaded) {
      console.error('ERROR: Ampli is not yet initialized. Have you called ampli.load() on app start?');
      return false;
    }
    return !this.disabled;
  }

  /**
   * Initialize the Ampli SDK. Call once when your application starts.
   * @param options Configuration options to initialize the Ampli SDK with.
   */
  load(options: LoadOptions): void {
    this.disabled = options.disabled ?? false;

    if (this.isLoaded) {
      console.warn('WARNING: Ampli is already initialized. Ampli.load() should be called once at application startup.');
      return;
    }

    let apiKey: string | null = null;
    if (options.client && 'apiKey' in options.client) {
      apiKey = options.client.apiKey;
    } else if ('environment' in options) {
      apiKey = ApiKey[options.environment];
    }
    
    if (options.client && 'instance' in options.client) {
      this.amplitude = options.client.instance;
    } else if (apiKey) {
      this.amplitude = initNodeClient(apiKey, { ...DefaultOptions, ...options.client?.options });
    } else {
      console.error("ERROR: ampli.load() requires 'environment', 'client.apiKey', or 'client.instance'");
    }
  }

  identify(
    userId: string | undefined,
    properties?: IdentifyProperties,
    options?: IdentifyOptions,
    extra?: MiddlewareExtra,
  ) {
    const identify = new AmplitudeIdentify();
    const eventProperties = properties;
    if (eventProperties != null) {
      for (const [key, value] of Object.entries(eventProperties)) {
        if (value !== undefined) {
          identify.set(key, value);
        }
      }
    }
    const identifyEvent = getIdentifyEvent(identify, userId || options?.user_id, options?.device_id);
    const promise = this.isInitializedAndEnabled()
      ? this.amplitude!.logEvent({ ...options, ...identifyEvent }, extra)
      : getDefaultPromiseResponse();

    return { promise };
  }

  track(userId: string | undefined, event: Event, options?: EventOptions, extra?: MiddlewareExtra) {
    const promise = this.isInitializedAndEnabled()
      ? this.amplitude!.logEvent({ ...options, ...event,  user_id: userId }, extra)
      : getDefaultPromiseResponse();

    return { promise };
  }

  flush() {
    const promise = this.isInitializedAndEnabled()
      ? this.amplitude!.flush()
      : getDefaultPromiseResponse();

    return { promise };
  }

  /**
   * Signed in to Creator Network
   *
   * [View in Tracking Plan](https://data.amplitude.com/itpxd/Creator%20Network/events/main/latest/Signed%20in%20to%20Creator%20Network)
   *
   * **TRIGGER: User entered EA account credentials, clicked sign in, and successfully landed on the My Dashboard page**
   *
   * Owner: Sky Powell
   *
   * @param userId The user's ID.
   * @param options Amplitude event options.
   * @param extra Extra untyped parameters for use in middleware.
   */
  signedInToCreatorNetwork(
    userId: string | undefined,
    options?: EventOptions,
    extra?: MiddlewareExtra,
  ) {
    return this.track(userId, new SignedInToCreatorNetwork(), options, extra);
  }
}

/**
 * Export 'ampli' the default instance of Ampli.
 *
 * More instances can be created with 'const a = new Ampli()'
 */
export const ampli = new Ampli();
