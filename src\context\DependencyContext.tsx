import React, { createContext, Dispatch, ReactNode, useContext, useMemo } from "react";
import { Action } from "@src/shared/errorHandling/errorHandler";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

const DependencyContext = createContext(undefined);

export function useDependency() {
  return useContext(DependencyContext);
}

export function DependencyProvider({
  configuration,
  errorHandler,
  notificationsClient,
  metadataClient,
  applicationsClient,
  onBoardingClient,
  creatorsClient,
  client,
  children
}: Readonly<{
  readonly configuration: Record<string, unknown>;
  errorHandler: (dispatch: Dispatch<Action>, e: Error) => void;
  metadataClient: TraceableHttpClient;
  notificationsClient: TraceableHttpClient;
  applicationsClient: TraceableHttpClient;
  creatorsClient: TraceableHttpClient;
  onBoardingClient: TraceableHttpClient;
  client: TraceableHttpClient;
  children: ReactNode;
}>) {
  const value = useMemo(
    () => ({
      configuration,
      errorHandler,
      notificationsClient,
      metadataClient,
      applicationsClient,
      onBoardingClient,
      client,
      creatorsClient
    }),
    [
      configuration,
      onBoardingClient,
      errorHandler,
      notificationsClient,
      metadataClient,
      applicationsClient,
      client,
      creatorsClient
    ]
  );
  return <DependencyContext.Provider value={value}>{children}</DependencyContext.Provider>;
}
