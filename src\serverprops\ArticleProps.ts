import { serverPropsControllerFactory } from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ArticlePagePropsController from "./controllers/ArticlePagePropsController";
import ArticlesHttpClient from "@src/server/contentModal/ArticlesHttpClient";
import config from "config";

const articleProps = (locale: string, slug: string[]) =>
  serverPropsControllerFactory(
    new ArticlePagePropsController(
      ApiContainer.get("options"),
      ApiContainer.get(ArticlesHttpClient),
      locale,
      slug,
      config.DEFAULT_AVATAR_IMAGE
    )
  );

export default articleProps;
