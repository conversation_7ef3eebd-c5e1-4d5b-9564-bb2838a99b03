import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import {
  NextApiRequestWithSession,
  RequestHandlerOptions,
  ServerPropsController
} from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedUserFactory } from "@src/analytics/BrowserAnalytics";
import runtimeConfiguration from "@src/configuration/runtimeConfiguration";
import { GetServerSidePropsResult, NextApiResponse } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import config from "config";
import { RegistrationsPageProps } from "../RegistrationsProps";

export default class RegistrationsPagePropsController
  extends AuthenticatedRequestHandler
  implements ServerPropsController<RegistrationsPageProps> {
  constructor(
    options: RequestHandlerOptions,
    private readonly currentLocale: string,
    private readonly defaultAvatar: string
  ) {
    super(options);
  }

  async handle(
    req: NextApiRequestWithSession,
    _res: NextApiResponse
  ): Promise<GetServerSidePropsResult<RegistrationsPageProps>> {
    const authenticatedUser = AuthenticatedUserFactory.fromIdentity(
      this.identity(req),
      this.program,
      this.defaultAvatar
    );
    return {
      props: {
        runtimeConfiguration: runtimeConfiguration(authenticatedUser),
        user: authenticatedUser,
        WATERMARKS_URL: config.WATERMARKS_URL,
        ...(await serverSideTranslations(this.currentLocale, [
          "common",
          "opportunities",
          "criteria-opportunity",
          "game-code",
          "thanks-opportunity",
          "content-submission"
        ]))
      }
    };
  }
}
