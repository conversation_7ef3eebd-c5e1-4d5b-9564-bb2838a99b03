import { render, screen } from "@testing-library/react";
import CategorySection from "./CategoryCard";
import { aCategory } from "__tests__/factories/documentation/documentation";
import { axe } from "jest-axe";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("@src/context/DependencyContext");

describe("CategorySection", () => {
  const category = aCategory();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        BASE_PATH: "https://example.com"
      }
    });
  });

  it("shows category title and description", () => {
    render(<CategorySection {...category} />);

    expect(screen.getByRole("heading", { name: category.title })).toBeInTheDocument();
    expect(screen.getByText(category.description)).toBeInTheDocument();
  });

  it("shows correct number of subcategory cards", () => {
    render(<CategorySection {...category} />);

    expect(screen.getAllByTestId("subcategory-card-container")).toHaveLength(category.subCategories.length);
  });

  it.each(category.subCategories)("shows subcategory card with title", (subCategory) => {
    render(<CategorySection {...category} />);

    expect(screen.getByRole("heading", { name: subCategory.title })).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<CategorySection {...category} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
