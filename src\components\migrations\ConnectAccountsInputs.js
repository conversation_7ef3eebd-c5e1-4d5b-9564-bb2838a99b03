import React, { memo, useCallback } from "react";
import {
  AccountCard,
  facebookIcon,
  instagramIcon,
  tiktokIcon,
  twitchIcon,
  youTubeIcon
} from "@eait-playerexp-cn/core-ui-kit";
import { useAppContext } from "../../context";
import {
  GET_FB_PAGES,
  POPUP_OPENED,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS,
  SHOW_FACEBOOK_PAGES,
  WINDOW_PARAMS
} from "../../utils";
import ConnectedAccountsService from "../../../src/services/ConnectedAccountsService";
import { toast } from "react-toastify";
import { useDependency } from "@src/context/DependencyContext";

let accounts = [
  { label: "youTube", accountIcon: youTubeIcon, accountType: "YOUTUBE", url: "/api/youtube-login" },
  { label: "twitch", accountIcon: twitchIcon, accountType: "TWITCH", url: "/api/twitch-login" },
  { label: "facebook", accountIcon: facebookIcon, accountType: "FACEBOOK", url: "/api/facebook-login" },
  { label: "instagram", accountIcon: instagramIcon, accountType: "INSTAGRAM", url: "/api/instagram-login" },
  { label: "tiktok", accountIcon: tiktokIcon, accountType: "TIKTOK", url: "/api/tiktok-login" }
];

const AddAccounts = memo(function AddAccounts({
  imageType,
  accountType,
  url,
  setShowAddConfirmation,
  showAddConfirmation,
  accountId,
  disconnected,
  isExpired,
  labels,
  isInterestedCreator
}) {
  const { errorHandler } = useDependency();
  const { dispatch } = useAppContext();
  const stableDispatch = useCallback(dispatch, []);
  const onAddAccount = useCallback(() => {
    if (!showAddConfirmation) {
      setShowAddConfirmation(true);
      const loginWindow = window.open(url, "_blank", WINDOW_PARAMS);
      isInterestedCreator && toast.dismiss();
      stableDispatch({ type: POPUP_OPENED, data: true });
      const loop = setInterval(function () {
        if (loginWindow.closed) {
          clearInterval(loop);
          stableDispatch({ type: POPUP_OPENED, data: false });
          setShowAddConfirmation(false);
          ConnectedAccountsService.clearAccountType()
            .then(() => {
              isInterestedCreator && accountType === "FACEBOOK" && dispatch({ type: GET_FB_PAGES, data: true });
              isInterestedCreator && accountType === "FACEBOOK" && dispatch({ type: SHOW_FACEBOOK_PAGES, data: true });
              isInterestedCreator
                ? dispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true })
                : location.reload(); // refresh to trigger getServersideProps rerun and spew new session props
            })
            .catch((e) => errorHandler(stableDispatch, e));
        }
      }, 100);
    }
  }, [showAddConfirmation, url, setShowAddConfirmation, stableDispatch]);

  return (
    <>
      <AccountCard
        {...{
          ...{
            accountIcon: imageType,
            noAccount: true,
            expired: !disconnected && isExpired,
            handleAddAccount: onAddAccount,
            accountId,
            accountType,
            labels
          }
        }}
      />
    </>
  );
});

const ConnectAccountsInputs = ({ labels, setShowAddConfirmation, showAddConfirmation, isInterestedCreator }) => {
  return (
    <div>
      <div className="mg-connected-accounts-input-container">
        <h4 className="mg-connected-accounts-title">{labels.subTitle}</h4>
        <div className="connect-account-card-container">
          {accounts.map(({ url, accountType, accountIcon }, ind) => (
            <AddAccounts
              key={`accounts-${ind}`}
              {...{ url, setShowAddConfirmation, showAddConfirmation, accountType, labels, accountIcon }}
              accountType={accountType}
              imageType={accountIcon}
              isInterestedCreator={isInterestedCreator}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
export default ConnectAccountsInputs;
