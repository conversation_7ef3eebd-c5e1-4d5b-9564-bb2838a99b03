import { render, screen } from "@testing-library/react";
import DocumentationPage from "./DocumentationPage";
import { documentationPageData } from "__tests__/factories/documentation/documentation";
import { axe } from "jest-axe";
import { useDependency } from "@src/context/DependencyContext";

jest.mock("../../../src/utils", () => ({
  useDetectScreen: jest.fn().mockImplementation((width) => width === 10000)
}));
jest.mock("@src/context/DependencyContext");

describe("DocumentationPage", () => {
  const documentationPageProps = documentationPageData();

  beforeEach(() => {
    jest.clearAllMocks();
    (useDependency as jest.Mock).mockReturnValue({
      configuration: {
        BASE_PATH: "https://example.com"
      }
    });
  });

  it("shows hero section", () => {
    render(<DocumentationPage {...documentationPageProps} />);

    expect(screen.getByTestId("hero-section")).toBeInTheDocument();
    expect(screen.getByText(documentationPageProps.title)).toBeInTheDocument();
    expect(screen.getByText(documentationPageProps.description)).toBeInTheDocument();
  });

  it.each(documentationPageProps.categories)("shows all categories", (category) => {
    render(<DocumentationPage {...documentationPageProps} />);

    expect(screen.getByText(category.title)).toBeInTheDocument();
    expect(screen.getByText(category.description)).toBeInTheDocument();
  });

  it("shows section separators between categories", () => {
    render(<DocumentationPage {...documentationPageProps} />);

    expect(screen.getAllByTestId("documentation-page-section-separator")).toHaveLength(
      documentationPageProps.categories.length - 1
    );
  });

  it("is accessible", async () => {
    const { container } = render(<DocumentationPage {...documentationPageProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
