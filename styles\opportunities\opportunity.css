.opportunity-container {
  @apply min-h-screen pb-meas24 pl-meas8 pr-meas8 pt-meas22 font-text-regular text-white md:pb-meas33 md:pl-[65px] md:pr-[65px] md:pt-meas33 xl:pb-meas43 xl:pr-[118px];
}
.opportunity-container {
  @apply block w-full;
  background: linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 100) 100%) top center no-repeat,
    linear-gradient(90deg, rgba(13, 16, 66, 100) 00%, rgba(13, 16, 66, 0) 60%) top center no-repeat;
  background-size: 100% 1000px, 100% 1000px;
  background-color: #0d1042;
  height: 100%;
}
@media screen and (min-width: 768px) {
  .opportunity-container {
    @apply block w-full;
    background: linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 100) 100%) top center no-repeat,
      linear-gradient(90deg, rgba(13, 16, 66, 100) 00%, rgba(13, 16, 66, 0) 60%) top center no-repeat,
      var(--background-characters) top right no-repeat;
    background-size: 100% 1000px, 100% 1000px, auto 283px;
    background-color: #0d1042;
    height: 100%;
  }
}
@media screen and (min-width: 1280px) {
  .opportunity-container {
    @apply block w-full;
    background: linear-gradient(180deg, rgba(13, 16, 66, 0) 0%, rgba(13, 16, 66, 100) 100%) top center no-repeat,
      linear-gradient(90deg, rgba(13, 16, 66, 100) 00%, rgba(13, 16, 66, 0) 60%) top center no-repeat,
      var(--background-characters) top right no-repeat, var(--background-shapes) center 920px no-repeat;
    background-size: 100% 1000px, 100% 1000px, auto 583px, auto 1400px;
    background-color: #0d1042;
    height: 100%;
  }
}
.opportunity-full-screen {
  @apply m-auto max-w-[840px];
}
.opportunity-back-nav {
  @apply mr-[40px] flex font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.opportunity-back-nav > span:last-child {
  @apply ml-meas4;
}
.opportunity-back-nav .icon {
  @apply h-meas9 w-meas9;
}
.opportunity-hero-card {
  @apply mt-meas12 rounded bg-white md:mt-meas33 md:grid md:grid-cols-2 xl:mt-meas18;
}
.opportunity-hero-image {
  @apply mb-meas6 h-[140px] w-full rounded-t md:mb-meas0 md:h-[250px] md:w-[640px] md:rounded-l md:rounded-r-none md:object-fill xl:w-[395px];
}
.opportunity-hero-card-row2 {
  @apply pl-meas7 pr-meas18 md:self-center md:pl-meas12 md:pt-meas10;
}
.opportunity-title {
  @apply break-words font-display-bold font-bold text-indigo-50 xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.opportunity-text-tags {
  @apply mt-meas6 flex flex-wrap pb-meas12 md:mt-meas16;
}
.opportunity-text-tag {
  @apply mb-meas4 mr-meas4 h-meas8 rounded-[100px] pl-meas4 pr-meas4 xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.opportunity-text-tag-red {
  @apply bg-indigo-50;
}
.opportunity-text-tag-blue {
  @apply bg-info-60;
}
.opportunity-card2 {
  @apply mb-meas12 mt-meas6 rounded bg-white md:mt-meas10 md:h-[284px] md:w-full xl:h-[260px];
}
.opportunity-content {
  @apply md:grid md:grid-cols-3;
}
.opportunity-content2 {
  @apply md:pb-meas13;
}
.opportunity-content-container {
  @apply p-meas8 pb-meas0;
}
.opportunity-content-title {
  @apply flex font-text-bold text-indigo-50 xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.opportunity-content-block {
  @apply mt-meas8 md:mr-meas12 md:border-r md:border-navy-60 md:border-opacity-30;
}
.opportunity-content-block:last-child {
  @apply md:mr-meas0 md:border-r-0;
}
.opportunity-content-block-signup {
  @apply pb-meas9 text-center md:pb-meas0;
}
.opportunity-content-label {
  @apply text-navy-40 xs:text-mobile-caption1 md:pr-meas9 md:text-tablet-body-small xl:text-desktop-caption1;
}
.opportunity-content-value {
  @apply mt-[2px] text-gray-90 xs:text-mobile-body-default md:mt-meas2 md:pr-meas9 md:text-tablet-body-default lg:text-desktop-body-default;
}
.opportunity-content-value .icon {
  @apply text-info-50;
}
.opportunity-content-title-icon {
  @apply mr-meas2 mt-[2px] h-meas12 w-meas13 pb-[1.5px] md:mt-meas0 md:self-center;
}
.opportunity-content-todo-icon {
  @apply mr-meas2 mt-[2px] md:mt-meas0 md:self-center;
}
.opportunity-content-todo-icon-enabled {
  @apply text-success-50;
}
.opportunity-content-title-success-icon {
  @apply mr-meas2 mt-[2px] h-meas10 w-meas10 text-success-70 md:mt-meas0 md:self-center;
}
.opportunity-content-block-signup-button {
  @apply m-auto inline-block md:min-w-[170px] xl:min-w-[260px];
}
.opportunity-secondary-content-container {
  @apply mt-meas18 md:mt-meas26;
}
.opportunity-secondary-content-title {
  @apply font-display-regular font-bold text-white xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.opportunity-secondary-content-description {
  @apply mt-meas6  text-white xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.opportunity-secondary-content-description ul {
  @apply list-disc pl-meas8;
}
.opportunity-secondary-content-description ol {
  @apply list-decimal pl-meas8;
}
.opportunity-secondary-content-things-to-do-row {
  @apply mb-meas8 flex;
}
.opportunity-secondary-content-things-to-do-row-hide {
  @apply mb-meas8 hidden;
}
.opportunity-content-platforms {
  @apply flex w-[290px] flex-wrap md:w-[640px] xl:w-[790px];
}
.opportunity-content-platform-card-container {
  @apply mr-meas9 mt-meas6 md:mt-meas10;
}
.opportunity-content-platform-card {
  @apply flex h-[98px] w-[98px] items-center justify-center rounded-[2px] border-[0.5px] border-navy-60 bg-navy-80 text-gray-30;
}
.opportunity-content-platform-image {
  @apply h-[49px] w-[49px];
}
.opportunity-content-platform-card-label {
  @apply mt-meas4 w-[98px] text-center font-text-regular text-gray-10 xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.opportunity-remote-event-wrapper {
  @apply mt-[45px];
}
.opportunity-payment-banner-wrapper {
  @apply mb-meas16;
}
.opportunity-perks-what-to-do-with-flag .opportunity-details-content-container {
  @apply mb-meas0;
}
.opportunity-disclosure-policy-flag,
.opportunity-perks-what-to-do-with-flag,
.opportunity-perks-what-to-do-with-flag .opportunity-perks-what-to-do,
.opportunity-perks-what-to-do-with-flag .opportunity-perks-free-game-code-details-container,
.opportunity-perks-what-to-do-with-flag .opportunity-perks-what-to-do-with-flag,
.point-of-contact-card-point-of-contact-container-with-flag .point-of-contact-card-point-of-contact-container {
  @apply mt-meas16;
}
.opportunity-payment-banner-wrapper .payment-details-banner {
  @apply mt-meas0;
}
.opportunity-disclosure-policy-flag .opportunity-content-guide-line-title,
.opportunity-disclosure-policy-flag .opportunity-content-guide-line-container {
  @apply mt-meas0 text-white;
}
.opportunity-description-container-with-flag .formatted-content,
.opportunity-description-container-with-flag .formatted-content :is(:where(strong):not(:where([class~="not-prose"] *))),
.opportunity-payment-details,
.opportunity-key-date-label,
.opportunity-key-date-value,
.opportunity-in-person-event-location-details,
.opportunity-in-person-event-location,
.opportunity-event-information-section .opportunity-event-information-description,
.opportunity-perks-additional-info-container .opportunity-perks-information-content,
.opportunity-perks-what-to-do-with-flag .opportunity-details-description,
.opportunity-perks-what-to-do-with-flag .opportunity-details-action-label,
.opportunity-disclosure-policy-flag .opportunity-content-guide-line-container {
  @apply text-font-normal;
}
.opportunity-key-dates-section,
.opportunity-in-person-event-section,
.opportunity-game-and-creator-code-section,
.opportunity-payment-section,
.opportunity-event-information-section,
.opportunity-online-event-card-section {
  @apply mt-[30px] text-font-normal md:mt-meas16;
}
.opportunity-key-dates-header,
.opportunity-in-person-event-header,
.opportunity-game-and-creator-code-header,
.opportunity-payment-header,
.opportunity-event-information-header,
.opportunity-online-event-card-header {
  @apply mb-meas10 font-display-bold text-[1.5rem] leading-7 tracking-[0.0625rem];
}
.opportunity-key-date,
.opportunity-in-person-event-location-details,
.opportunity-payment-details,
.opportunity-event-information-description {
  @apply font-text-regular tracking-[0.05rem] xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.opportunity-key-date-label {
  @apply font-text-bold;
}
.opportunity-key-date-value {
  @apply block md:inline;
}
.opportunity-key-date:not(:first-child) {
  @apply mt-meas8 md:mt-meas0;
}
.opportunity-view-deliverables-container {
  @apply mt-meas10 w-full md:w-[17.5rem];
}
.opportunity-view-deliverables-container .btn-secondary {
  @apply w-[inherit];
}
.opportunity-in-person-event-location-details {
  @apply font-normal;
}
.opportunity-in-person-event-location {
  @apply font-text-bold;
}
.opportunity-layout {
  @apply bg-page pb-[1px];
}
.header-container
  + div
  > .opportunity-perks-container
  .opportunity-perks-full-screen
  .opportunity-perks-header-container-wrapper,
.header-container
  + div
  > .opportunity-perks-container
  .opportunity-perks-full-screen
  .opportunity-perks-header-mobile-or-tab-container,
.header-container
  + div
  > .opportunity-perks-container
  .opportunity-perks-full-screen
  .opportunity-perks-header-container,
.header-container + div > section {
  @apply pt-[6.25rem];
  transition: all 0.35s ease-in-out;
}
