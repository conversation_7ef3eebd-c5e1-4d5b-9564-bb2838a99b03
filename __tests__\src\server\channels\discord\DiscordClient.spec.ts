import DiscordClient from "@src/server/channels/discord/DiscordClient";
import config from "../../../../../config";

describe("DiscordClient", () => {
  let discordClient: DiscordClient;
  config.DISCORD_CLIENT_ID = "CLIENT_ID";
  config.DISCORD_CLIENT_REDIRECT_URI = new URL("http://localhost/callback");
  config.DISCORD_SCOPES = "identify email";

  beforeEach(() => {
    discordClient = new DiscordClient();
  });

  it("should initialize with correct config values", () => {
    expect(discordClient["clientID"]).toBe(config.DISCORD_CLIENT_ID);
    expect(discordClient["redirectUri"]).toBe(config.DISCORD_CLIENT_REDIRECT_URI);
    expect(discordClient["scopes"]).toBe(config.DISCORD_SCOPES);
  });

  it("should generate the correct authorization URL", () => {
    const expectedUrl = `https://discord.com/api/oauth2/authorize?client_id=${config.DISCORD_CLIENT_ID}&redirect_uri=${
      config.DISCORD_CLIENT_REDIRECT_URI
    }&response_type=code&scope=${encodeURIComponent(config.DISCORD_SCOPES)}`;

    expect(discordClient.authorizationUrl()).toBe(expectedUrl);
  });
});
